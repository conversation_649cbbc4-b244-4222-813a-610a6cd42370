/**
  ******************************************************************************
  * @file    串口屏测试程序.c
  * <AUTHOR> 第三问 串口屏集成测试
  * @version V1.0
  * @date    2024
  * @brief   串口屏控制功能测试程序
  ******************************************************************************
  * @attention
  * 
  * 本文件提供串口屏控制功能的测试代码
  * 可以独立运行，用于验证串口屏通信是否正常
  * 
  ******************************************************************************
  */

/* 测试说明：
 * 1. 将此代码添加到main.c中的适当位置进行测试
 * 2. 或者创建独立的测试函数调用
 * 3. 通过串口助手或串口屏观察输出结果
 */

#include "main.h"
#include "../Modules/Control/external_control.h"
#include "../Modules/Core/systick.h"

/**
 * @brief  串口屏功能测试
 * @param  None
 * @retval None
 */
void UartScreen_FunctionTest(void)
{
    // 测试1：发送初始化信息
    HMI_SendString("t0.txt", "TEST_MODE");
    Delay_ms(500);
    
    // 测试2：发送数字信息
    HMI_SendNumber("n0.val", 123);
    Delay_ms(500);
    
    // 测试3：发送浮点数信息
    HMI_SendFloat("x0.val", 3.14);
    Delay_ms(500);
    
    // 测试4：循环发送不同的频率值
    for (int i = 1; i <= 10; i++) {
        char freq_str[16];
        snprintf(freq_str, sizeof(freq_str), "%d.000MHz", i);
        HMI_SendString("t0.txt", freq_str);
        HMI_SendNumber("n0.val", i);
        Delay_ms(1000);
    }
    
    // 测试5：发送错误信息测试
    HMI_SendString("t1.txt", "TEST_OK");
    Delay_ms(500);
}

/**
 * @brief  串口屏命令模拟测试
 * @param  None
 * @retval None
 */
void UartScreen_CommandTest(void)
{
    // 模拟接收到不同的命令
    uint8_t test_commands[] = {'1', '2', '3', '4', '5'};
    
    for (int i = 0; i < 5; i++) {
        // 这里可以直接调用命令处理逻辑
        switch (test_commands[i]) {
            case '1':
                HMI_SendString("t0.txt", "6.000MHz");
                break;
            case '2':
                HMI_SendFloat("x0.val", 0.8);
                break;
            case '3':
                HMI_SendNumber("n0.val", 1);
                break;
            case '4':
                HMI_SendNumber("n0.val", 0);
                break;
            case '5':
                HMI_SendString("t0.txt", "STATUS_OK");
                break;
        }
        Delay_ms(2000);
    }
}

/**
 * @brief  串口屏通信测试主函数
 * @param  None
 * @retval None
 * @note   在main函数中调用此函数进行测试
 */
void UartScreen_MainTest(void)
{
    // 初始化串口屏
    if (UartScreen_Init() != 0) {
        // 初始化失败，LED快速闪烁
        while (1) {
            GPIO_ToggleBits(GPIOE, GPIO_Pin_6);
            Delay_ms(100);
        }
    }
    
    // LED慢闪表示初始化成功
    for (int i = 0; i < 6; i++) {
        GPIO_ToggleBits(GPIOE, GPIO_Pin_6);
        Delay_ms(300);
    }
    
    // 发送欢迎信息
    HMI_SendString("t0.txt", "UART_READY");
    HMI_SendString("t1.txt", "TEST_START");
    HMI_SendNumber("n0.val", 0);
    HMI_SendFloat("x0.val", 0.0);
    
    Delay_ms(2000);
    
    // 执行功能测试
    UartScreen_FunctionTest();
    
    Delay_ms(2000);
    
    // 执行命令测试
    UartScreen_CommandTest();
    
    // 发送测试完成信息
    HMI_SendString("t0.txt", "TEST_DONE");
    HMI_SendString("t1.txt", "ALL_OK");
    
    // 进入正常工作模式
    while (1) {
        // 处理串口屏命令
        ExternalControl_Process();
        
        // 定期发送心跳信息
        static uint32_t heartbeat_counter = 0;
        heartbeat_counter++;
        
        if (heartbeat_counter % 1000000 == 0) {
            HMI_SendNumber("n0.val", heartbeat_counter / 1000000);
        }
        
        Delay_ms(1);
    }
}

/* 使用方法：
 * 
 * 方法1：在main函数中直接调用测试
 * int main(void) {
 *     SystemClock_Config();
 *     SysTick_Init();
 *     BSP_Init();
 *     
 *     UartScreen_MainTest();  // 调用测试函数
 * }
 * 
 * 方法2：在现有main函数中添加测试代码
 * 将UartScreen_FunctionTest()或UartScreen_CommandTest()
 * 添加到适当的位置进行测试
 * 
 * 方法3：通过串口助手测试
 * 1. 连接串口助手到PA9/PA10
 * 2. 设置波特率9600
 * 3. 发送字符'1','2','3','4','5'观察响应
 * 4. 观察串口助手接收到的HMI命令格式
 */
