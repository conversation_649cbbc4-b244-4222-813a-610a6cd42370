/**
  ******************************************************************************
  * @file    keypad_4x4.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   4x4矩阵键盘驱动实现文件 - 电赛G题专用
  ******************************************************************************
  * @attention
  *
  * 本文件实现4x4矩阵键盘的驱动功能，专为电赛G题信号发生器设计
  * 
  * 电赛G题要求:
  * - 频率步长: 100Hz
  * - 频率范围: 最高不低于1MHz  
  * - 电压步长: 0.1V
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "keypad_4x4.h"
#include "../Control/ad9910_control.h"
#include "../HardWare/systick.h"
#include <string.h>

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
#define KEYPAD_EVENT_QUEUE_SIZE     8       ///< 事件队列大小
#define KEYPAD_MATRIX_ROWS          4       ///< 矩阵行数
#define KEYPAD_MATRIX_COLS          4       ///< 矩阵列数

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/* 键盘状态变量 */
static bool keypad_enabled = false;
static uint32_t last_scan_time = 0;
static KeypadCallback_t keypad_callback = NULL;

/* 按键状态跟踪 */
static KeypadKey_t current_key = KEY_NONE;
static KeypadKey_t last_key = KEY_NONE;
static uint32_t key_press_time = 0;
static bool key_processed = false;

/* 事件队列 */
static KeypadEvent_t event_queue[KEYPAD_EVENT_QUEUE_SIZE];
static uint8_t event_queue_head = 0;
static uint8_t event_queue_tail = 0;
static uint8_t event_queue_count = 0;

/* 按键映射表 (行列索引到按键值) */
static const KeypadKey_t key_map[KEYPAD_MATRIX_ROWS][KEYPAD_MATRIX_COLS] = {
    {KEY_100HZ,        KEY_1KHZ,         KEY_10KHZ,        KEY_FREQ_UP},      // 第1行
    {KEY_100KHZ,       KEY_500KHZ,       KEY_1MHZ,         KEY_FREQ_DOWN},    // 第2行
    {KEY_0_1V,         KEY_0_5V,         KEY_1_0V,         KEY_VOLT_UP},      // 第3行
    {KEY_OUTPUT_TOGGLE, KEY_RESET,       KEY_SAVE,         KEY_VOLT_DOWN}     // 第4行
};

/* 行引脚数组 */
static const uint16_t row_pins[KEYPAD_MATRIX_ROWS] = {
    KEYPAD_ROW0_PIN, KEYPAD_ROW1_PIN, KEYPAD_ROW2_PIN, KEYPAD_ROW3_PIN
};

/* 列引脚数组 */
static const uint16_t col_pins[KEYPAD_MATRIX_COLS] = {
    KEYPAD_COL0_PIN, KEYPAD_COL1_PIN, KEYPAD_COL2_PIN, KEYPAD_COL3_PIN
};

/* Private function prototypes -----------------------------------------------*/
static void Keypad_GPIO_Init(void);
static KeypadKey_t Keypad_ScanMatrix(void);
static void Keypad_AddEvent(KeypadKey_t key, KeypadState_t state);
static uint32_t Keypad_GetTick(void);

/* Private functions ---------------------------------------------------------*/

/**
 * @brief  4x4矩阵键盘初始化
 * @param  None
 * @retval None
 */
void Keypad4x4_Init(void)
{
    /* GPIO初始化 */
    Keypad_GPIO_Init();
    
    /* 初始化状态变量 */
    keypad_enabled = true;
    last_scan_time = 0;
    current_key = KEY_NONE;
    last_key = KEY_NONE;
    key_press_time = 0;
    key_processed = false;
    
    /* 清空事件队列 */
    event_queue_head = 0;
    event_queue_tail = 0;
    event_queue_count = 0;
    memset(event_queue, 0, sizeof(event_queue));
}

/**
 * @brief  GPIO初始化
 * @param  None
 * @retval None
 */
static void Keypad_GPIO_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    
    /* 使能GPIO时钟 */
    RCC_AHB1PeriphClockCmd(KEYPAD_GPIO_RCC, ENABLE);
    
    /* 配置行引脚为输出 (推挽输出，初始高电平) */
    GPIO_InitStructure.GPIO_Pin = KEYPAD_ALL_ROWS;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(KEYPAD_ROW_PORT, &GPIO_InitStructure);
    
    /* 配置列引脚为输入 (上拉输入) */
    GPIO_InitStructure.GPIO_Pin = KEYPAD_ALL_COLS;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(KEYPAD_COL_PORT, &GPIO_InitStructure);
    
    /* 设置所有行为高电平 (非激活状态) */
    GPIO_SetBits(KEYPAD_ROW_PORT, KEYPAD_ALL_ROWS);
}

/**
 * @brief  键盘扫描 (在主循环中调用)
 * @param  None
 * @retval None
 */
void Keypad4x4_Scan(void)
{
    uint32_t current_time;
    KeypadKey_t scanned_key;
    
    if (!keypad_enabled) return;
    
    current_time = Keypad_GetTick();
    
    /* 检查扫描间隔 */
    if (current_time - last_scan_time < KEYPAD_SCAN_INTERVAL) {
        return;
    }
    last_scan_time = current_time;
    
    /* 扫描矩阵 */
    scanned_key = Keypad_ScanMatrix();
    
    /* 按键状态处理 */
    if (scanned_key != KEY_NONE) {
        if (current_key == KEY_NONE) {
            /* 新按键按下 */
            current_key = scanned_key;
            key_press_time = current_time;
            key_processed = false;
        } else if (current_key == scanned_key) {
            /* 按键持续按下 */
            uint32_t press_duration = current_time - key_press_time;
            
            if (!key_processed && press_duration >= KEYPAD_DEBOUNCE_TIME) {
                /* 防抖时间到，确认按键按下 */
                Keypad_AddEvent(current_key, KEY_STATE_PRESSED);
                key_processed = true;
            } else if (key_processed && press_duration >= KEYPAD_LONG_PRESS_TIME) {
                /* 长按检测 */
                Keypad_AddEvent(current_key, KEY_STATE_LONG_PRESSED);
                key_press_time = current_time; // 重置时间，避免重复触发
            }
        } else {
            /* 按键变化，重新开始 */
            current_key = scanned_key;
            key_press_time = current_time;
            key_processed = false;
        }
    } else {
        if (current_key != KEY_NONE && key_processed) {
            /* 按键释放 */
            Keypad_AddEvent(current_key, KEY_STATE_RELEASED);
        }
        current_key = KEY_NONE;
        key_processed = false;
    }
}

/**
 * @brief  矩阵扫描
 * @param  None
 * @retval KeypadKey_t 扫描到的按键值
 */
static KeypadKey_t Keypad_ScanMatrix(void)
{
    uint8_t row, col;
    
    for (row = 0; row < KEYPAD_MATRIX_ROWS; row++) {
        /* 设置当前行为低电平，其他行为高电平 */
        GPIO_SetBits(KEYPAD_ROW_PORT, KEYPAD_ALL_ROWS);
        GPIO_ResetBits(KEYPAD_ROW_PORT, row_pins[row]);
        
        /* 短暂延时，等待电平稳定 */
        for (volatile int i = 0; i < 100; i++);
        
        /* 检查各列 */
        for (col = 0; col < KEYPAD_MATRIX_COLS; col++) {
            if (GPIO_ReadInputDataBit(KEYPAD_COL_PORT, col_pins[col]) == Bit_RESET) {
                /* 检测到按键按下 (低电平) */
                GPIO_SetBits(KEYPAD_ROW_PORT, KEYPAD_ALL_ROWS); // 恢复所有行为高电平
                return key_map[row][col];
            }
        }
    }
    
    /* 恢复所有行为高电平 */
    GPIO_SetBits(KEYPAD_ROW_PORT, KEYPAD_ALL_ROWS);
    return KEY_NONE;
}

/**
 * @brief  添加事件到队列
 * @param  key: 按键值
 * @param  state: 按键状态
 * @retval None
 */
static void Keypad_AddEvent(KeypadKey_t key, KeypadState_t state)
{
    if (event_queue_count >= KEYPAD_EVENT_QUEUE_SIZE) {
        return; // 队列满，丢弃事件
    }
    
    KeypadEvent_t* event = &event_queue[event_queue_tail];
    event->key = key;
    event->state = state;
    event->timestamp = Keypad_GetTick();
    event->duration = (state == KEY_STATE_RELEASED) ? 
                     (event->timestamp - key_press_time) : 0;
    
    event_queue_tail = (event_queue_tail + 1) % KEYPAD_EVENT_QUEUE_SIZE;
    event_queue_count++;
    
    /* 调用回调函数 */
    if (keypad_callback != NULL) {
        keypad_callback(event);
    }
    
    /* 处理按键功能 */
    if (state == KEY_STATE_PRESSED) {
        Keypad4x4_ProcessKey(key, state);
    }
}

/**
 * @brief  获取系统时间戳
 * @param  None
 * @retval 时间戳 (ms)
 */
static uint32_t Keypad_GetTick(void)
{
    return GetSysTickCount(); // 使用系统滴答计数器
}

/**
 * @brief  获取按键值 (非阻塞)
 * @param  None
 * @retval KeypadKey_t 按键值
 */
KeypadKey_t Keypad4x4_GetKey(void)
{
    return current_key;
}

/**
 * @brief  检查是否有按键事件
 * @param  event: 按键事件输出
 * @retval true-有事件, false-无事件
 */
bool Keypad4x4_HasEvent(KeypadEvent_t* event)
{
    if (event_queue_count == 0) {
        return false;
    }
    
    if (event != NULL) {
        *event = event_queue[event_queue_head];
    }
    
    event_queue_head = (event_queue_head + 1) % KEYPAD_EVENT_QUEUE_SIZE;
    event_queue_count--;
    
    return true;
}

/**
 * @brief  注册按键回调函数
 * @param  callback: 回调函数指针
 * @retval None
 */
void Keypad4x4_RegisterCallback(KeypadCallback_t callback)
{
    keypad_callback = callback;
}

/**
 * @brief  清空按键事件队列
 * @param  None
 * @retval None
 */
void Keypad4x4_ClearEvents(void)
{
    event_queue_head = 0;
    event_queue_tail = 0;
    event_queue_count = 0;
    memset(event_queue, 0, sizeof(event_queue));
}

/**
 * @brief  启用/禁用键盘扫描
 * @param  enable: true-启用, false-禁用
 * @retval None
 */
void Keypad4x4_Enable(bool enable)
{
    keypad_enabled = enable;
    if (!enable) {
        current_key = KEY_NONE;
        key_processed = false;
    }
}

/**
 * @brief  获取键盘状态
 * @retval true-启用, false-禁用
 */
bool Keypad4x4_IsEnabled(void)
{
    return keypad_enabled;
}

/* ==================== 电赛G题专用控制函数 ==================== */

/**
 * @brief  处理频率预设按键 (100Hz-1MHz)
 * @param  key: 按键值
 * @retval true-处理成功, false-非频率预设键
 */
bool Keypad4x4_HandleFrequencyPreset(KeypadKey_t key)
{
    uint32_t frequency = 0;
    
    switch (key) {
        case KEY_100HZ:   frequency = FREQ_PRESET_100HZ;   break;
        case KEY_1KHZ:    frequency = FREQ_PRESET_1KHZ;    break;
        case KEY_10KHZ:   frequency = FREQ_PRESET_10KHZ;   break;
        case KEY_100KHZ:  frequency = FREQ_PRESET_100KHZ;  break;
        case KEY_500KHZ:  frequency = FREQ_PRESET_500KHZ;  break;
        case KEY_1MHZ:    frequency = FREQ_PRESET_1MHZ;    break;
        default:          return false;
    }
    
    /* 设置频率 */
    AD9910_Control_SetFrequency(frequency);
    return true;
}

/**
 * @brief  处理电压预设按键 (0.1V-1.0V)
 * @param  key: 按键值
 * @retval true-处理成功, false-非电压预设键
 */
bool Keypad4x4_HandleVoltagePreset(KeypadKey_t key)
{
    uint16_t voltage = 0;
    
    switch (key) {
        case KEY_0_1V:    voltage = VOLT_PRESET_0_1V;    break;
        case KEY_0_5V:    voltage = VOLT_PRESET_0_5V;    break;
        case KEY_1_0V:    voltage = VOLT_PRESET_1_0V;    break;
        default:          return false;
    }
    
    /* 设置电压 */
    AD9910_Control_SetTargetAmplitude(voltage);
    return true;
}

/**
 * @brief  处理频率步进按键 (±100Hz)
 * @param  key: 按键值
 * @retval true-处理成功, false-非频率步进键
 */
bool Keypad4x4_HandleFrequencyStep(KeypadKey_t key)
{
    SystemParams_t params;
    uint32_t new_frequency;

    if (key != KEY_FREQ_UP && key != KEY_FREQ_DOWN) {
        return false;
    }

    /* 获取当前参数 */
    if (AD9910_Control_GetParams(&params) != CTRL_STATUS_OK) {
        return false;
    }

    /* 计算新频率 */
    if (key == KEY_FREQ_UP) {
        new_frequency = params.frequency_hz + FREQ_STEP_HZ;
        if (new_frequency > FREQ_MAX_HZ) {
            new_frequency = FREQ_MAX_HZ;
        }
    } else {
        if (params.frequency_hz > FREQ_STEP_HZ) {
            new_frequency = params.frequency_hz - FREQ_STEP_HZ;
        } else {
            new_frequency = FREQ_MIN_HZ;
        }
    }

    /* 设置新频率 */
    AD9910_Control_SetFrequency(new_frequency);
    return true;
}

/**
 * @brief  处理电压步进按键 (±0.1V)
 * @param  key: 按键值
 * @retval true-处理成功, false-非电压步进键
 */
bool Keypad4x4_HandleVoltageStep(KeypadKey_t key)
{
    SystemParams_t params;
    uint16_t new_voltage;

    if (key != KEY_VOLT_UP && key != KEY_VOLT_DOWN) {
        return false;
    }

    /* 获取当前参数 */
    if (AD9910_Control_GetParams(&params) != CTRL_STATUS_OK) {
        return false;
    }

    /* 计算新电压 */
    if (key == KEY_VOLT_UP) {
        new_voltage = params.target_amplitude_mv + VOLT_STEP_MV;
        if (new_voltage > VOLT_MAX_MV) {
            new_voltage = VOLT_MAX_MV;
        }
    } else {
        if (params.target_amplitude_mv > VOLT_STEP_MV) {
            new_voltage = params.target_amplitude_mv - VOLT_STEP_MV;
        } else {
            new_voltage = VOLT_MIN_MV;
        }
    }

    /* 设置新电压 */
    AD9910_Control_SetTargetAmplitude(new_voltage);
    return true;
}

/**
 * @brief  处理系统控制按键 (输出开关, 复位, 保存)
 * @param  key: 按键值
 * @retval true-处理成功, false-非系统控制键
 */
bool Keypad4x4_HandleSystemControl(KeypadKey_t key)
{
    SystemParams_t params;

    switch (key) {
        case KEY_OUTPUT_TOGGLE:
            /* 切换输出状态 */
            if (AD9910_Control_GetParams(&params) == CTRL_STATUS_OK) {
                AD9910_Control_EnableOutput(!params.output_enabled);
            }
            break;

        case KEY_RESET:
            /* 系统复位到默认状态 */
            AD9910_Control_SetFrequency(FREQ_PRESET_1KHZ);      // 默认1kHz
            AD9910_Control_SetTargetAmplitude(VOLT_PRESET_0_5V); // 默认0.5V
            AD9910_Control_EnableOutput(false);                  // 默认关闭输出
            break;

        case KEY_SAVE:
            /* 保存当前参数 (这里可以扩展为保存到EEPROM) */
            // 暂时只是获取参数，实际保存功能可以后续添加
            AD9910_Control_GetParams(&params);
            break;

        default:
            return false;
    }

    return true;
}

/**
 * @brief  处理所有按键 (统一入口)
 * @param  key: 按键值
 * @param  state: 按键状态
 * @retval None
 */
void Keypad4x4_ProcessKey(KeypadKey_t key, KeypadState_t state)
{
    if (state != KEY_STATE_PRESSED) {
        return; // 只处理按下事件
    }

    /* 尝试各种按键处理 */
    if (Keypad4x4_HandleFrequencyPreset(key)) {
        return;
    }

    if (Keypad4x4_HandleVoltagePreset(key)) {
        return;
    }

    if (Keypad4x4_HandleFrequencyStep(key)) {
        return;
    }

    if (Keypad4x4_HandleVoltageStep(key)) {
        return;
    }

    if (Keypad4x4_HandleSystemControl(key)) {
        return;
    }
}

/**
 * @brief  获取按键功能描述
 * @param  key: 按键值
 * @retval 按键功能描述字符串
 */
const char* Keypad4x4_GetKeyDescription(KeypadKey_t key)
{
    switch (key) {
        case KEY_100HZ:         return "100Hz";
        case KEY_1KHZ:          return "1kHz";
        case KEY_10KHZ:         return "10kHz";
        case KEY_FREQ_UP:       return "FREQ+";
        case KEY_100KHZ:        return "100kHz";
        case KEY_500KHZ:        return "500kHz";
        case KEY_1MHZ:          return "1MHz";
        case KEY_FREQ_DOWN:     return "FREQ-";
        case KEY_0_1V:          return "0.1V";
        case KEY_0_5V:          return "0.5V";
        case KEY_1_0V:          return "1.0V";
        case KEY_VOLT_UP:       return "VOLT+";
        case KEY_OUTPUT_TOGGLE: return "OUT";
        case KEY_RESET:         return "RESET";
        case KEY_SAVE:          return "SAVE";
        case KEY_VOLT_DOWN:     return "VOLT-";
        default:                return "UNKNOWN";
    }
}

/**
 * @brief  电赛G题演示模式 (按要求循环显示参数)
 * @param  enable: true-启用演示, false-禁用演示
 * @retval None
 */
void Keypad4x4_ContestDemoMode(bool enable)
{
    static bool demo_running = false;
    static uint8_t demo_step = 0;

    if (enable && !demo_running) {
        demo_running = true;
        demo_step = 0;
    } else if (!enable) {
        demo_running = false;
    }

    if (!demo_running) return;

    /* 演示序列：展示电赛G题要求的各种参数 */
    switch (demo_step) {
        case 0: AD9910_Control_SetFrequency(FREQ_PRESET_100HZ);   break;
        case 1: AD9910_Control_SetFrequency(FREQ_PRESET_1KHZ);    break;
        case 2: AD9910_Control_SetFrequency(FREQ_PRESET_10KHZ);   break;
        case 3: AD9910_Control_SetFrequency(FREQ_PRESET_100KHZ);  break;
        case 4: AD9910_Control_SetFrequency(FREQ_PRESET_500KHZ);  break;
        case 5: AD9910_Control_SetFrequency(FREQ_PRESET_1MHZ);    break;
        case 6: AD9910_Control_SetTargetAmplitude(VOLT_PRESET_0_1V); break;
        case 7: AD9910_Control_SetTargetAmplitude(VOLT_PRESET_0_5V); break;
        case 8: AD9910_Control_SetTargetAmplitude(VOLT_PRESET_1_0V); break;
        default: demo_step = 0; return;
    }

    demo_step++;
    if (demo_step > 8) {
        demo_step = 0;
    }
}

/**
 * @brief  验证参数是否符合电赛G题要求
 * @param  frequency_hz: 频率值
 * @param  voltage_mv: 电压值
 * @retval true-符合要求, false-超出范围
 */
bool Keypad4x4_ValidateContestParams(uint32_t frequency_hz, uint16_t voltage_mv)
{
    /* 检查频率范围 */
    if (frequency_hz < FREQ_MIN_HZ || frequency_hz > FREQ_MAX_HZ) {
        return false;
    }

    /* 检查电压范围 */
    if (voltage_mv < VOLT_MIN_MV || voltage_mv > VOLT_MAX_MV) {
        return false;
    }

    /* 检查频率步长 */
    if ((frequency_hz - FREQ_MIN_HZ) % FREQ_STEP_HZ != 0) {
        return false;
    }

    /* 检查电压步长 */
    if ((voltage_mv - VOLT_MIN_MV) % VOLT_STEP_MV != 0) {
        return false;
    }

    return true;
}

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
