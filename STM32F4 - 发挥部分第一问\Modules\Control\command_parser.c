/**
  ******************************************************************************
  * @file    command_parser.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   外部控制命令解析器实现文件
  ******************************************************************************
  * @attention
  *
  * 本文件实现外部设备控制命令的解析和处理功能
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "command_parser.h"
#include "../Core/systick.h"
#include <string.h>
#include <stdio.h>
#include <stdlib.h>

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/* 串口命令缓冲区 */
static char uart_buffer[CMD_BUFFER_SIZE];
static uint16_t uart_buffer_pos = 0;

/* 键盘状态 (预留给后续扩展) */
// static uint8_t keypad_mode = 0;  // 0=正常模式, 1=数值输入模式
// static uint32_t keypad_input_value = 0;
// static uint8_t keypad_input_type = 0;  // 1=频率, 2=幅度, 3=增益

/* Private function prototypes -----------------------------------------------*/
static ParseStatus_t ParseUARTCommand(const char* cmd_string, ParsedCommand_t* command);
static ParseStatus_t ParseKeypadSequence(uint8_t key_code, ParsedCommand_t* command);
static double ParseDoubleParameter(const char* param_string);

/* Private functions ---------------------------------------------------------*/

/**
 * @brief  命令解析器初始化
 * @param  None
 * @retval None
 */
void CommandParser_Init(void)
{
    memset(uart_buffer, 0, sizeof(uart_buffer));
    uart_buffer_pos = 0;
    // keypad_mode = 0;
    // keypad_input_value = 0;
    // keypad_input_type = 0;
}

/**
 * @brief  解析串口命令
 * @param  uart_data: 串口接收数据
 * @param  length: 数据长度
 * @param  command: 解析结果输出
 * @retval ParseStatus_t 解析状态
 */
ParseStatus_t CommandParser_ParseUART(const uint8_t* uart_data, uint16_t length, ParsedCommand_t* command)
{
    if (uart_data == NULL || command == NULL || length == 0) {
        return PARSE_STATUS_INVALID;
    }
    
    /* 添加数据到缓冲区 */
    CommandParser_AddUARTData(uart_data, length);
    
    /* 处理待解析命令 */
    return CommandParser_ProcessPendingUART(command);
}

/**
 * @brief  解析键盘命令
 * @param  key_code: 按键代码
 * @param  command: 解析结果输出
 * @retval ParseStatus_t 解析状态
 */
ParseStatus_t CommandParser_ParseKeypad(uint8_t key_code, ParsedCommand_t* command)
{
    if (command == NULL) {
        return PARSE_STATUS_INVALID;
    }
    
    return ParseKeypadSequence(key_code, command);
}

/**
 * @brief  生成命令响应
 * @param  command: 原始命令
 * @param  status: 执行状态
 * @param  response: 响应结构体输出
 * @retval None
 */
void CommandParser_GenerateResponse(const ParsedCommand_t* command, ControlStatus_t status, CommandResponse_t* response)
{
    if (command == NULL || response == NULL) return;
    
    response->status = status;
    response->data_count = 0;
    
    /* 根据命令类型生成响应 */
    switch (command->command) {
        case CMD_SET_FREQUENCY:
            if (status == CTRL_STATUS_OK) {
                snprintf(response->message, sizeof(response->message), "FREQ_SET:%lu", command->param1);
                response->data[0] = command->param1;
                response->data_count = 1;
            } else {
                strcpy(response->message, "FREQ_SET_FAILED");
            }
            break;
            
        case CMD_SET_AMPLITUDE:
            if (status == CTRL_STATUS_OK) {
                snprintf(response->message, sizeof(response->message), "AMP_SET:%lu", command->param1);
                response->data[0] = command->param1;
                response->data_count = 1;
            } else {
                strcpy(response->message, "AMP_SET_FAILED");
            }
            break;
            
        case CMD_SET_GAIN:
            if (status == CTRL_STATUS_OK) {
                double gain = (double)command->param1 + (double)command->param2 / 1000.0;
                snprintf(response->message, sizeof(response->message), "GAIN_SET:%.3f", gain);
                response->data[0] = command->param1;
                response->data[1] = command->param2;
                response->data_count = 2;
            } else {
                strcpy(response->message, "GAIN_SET_FAILED");
            }
            break;
            
        case CMD_ENABLE_OUTPUT:
            strcpy(response->message, (status == CTRL_STATUS_OK) ? "OUTPUT_ENABLED" : "OUTPUT_ENABLE_FAILED");
            break;
            
        case CMD_DISABLE_OUTPUT:
            strcpy(response->message, (status == CTRL_STATUS_OK) ? "OUTPUT_DISABLED" : "OUTPUT_DISABLE_FAILED");
            break;
            
        case CMD_GET_STATUS:
            if (status == CTRL_STATUS_OK) {
                SystemParams_t params;
                AD9910_Control_GetParams(&params);
                snprintf(response->message, sizeof(response->message), 
                        "STATUS:FREQ:%lu:AMP:%u:GAIN:%.3f:OUT:%d",
                        params.frequency_hz, params.target_amplitude_mv, 
                        params.gain_factor, params.output_enabled ? 1 : 0);
                response->data[0] = params.frequency_hz;
                response->data[1] = params.target_amplitude_mv;
                response->data[2] = (uint32_t)(params.gain_factor * 1000);
                response->data[3] = params.output_enabled ? 1 : 0;
                response->data_count = 4;
            } else {
                strcpy(response->message, "STATUS_GET_FAILED");
            }
            break;
            
        default:
            strcpy(response->message, "UNKNOWN_COMMAND");
            break;
    }
}

/**
 * @brief  格式化响应为串口输出
 * @param  response: 响应结构体
 * @param  output_buffer: 输出缓冲区
 * @param  buffer_size: 缓冲区大小
 * @retval 实际输出长度
 */
uint16_t CommandParser_FormatUARTResponse(const CommandResponse_t* response, char* output_buffer, uint16_t buffer_size)
{
    if (response == NULL || output_buffer == NULL || buffer_size == 0) {
        return 0;
    }
    
    const char* status_str = (response->status == CTRL_STATUS_OK) ? "OK" : "ERROR";
    
    return snprintf(output_buffer, buffer_size, "%s:%s\r\n", status_str, response->message);
}

/**
 * @brief  格式化响应为键盘显示
 * @param  response: 响应结构体
 * @param  display_buffer: 显示缓冲区
 * @param  buffer_size: 缓冲区大小
 * @retval 实际输出长度
 */
uint16_t CommandParser_FormatKeypadResponse(const CommandResponse_t* response, char* display_buffer, uint16_t buffer_size)
{
    if (response == NULL || display_buffer == NULL || buffer_size == 0) {
        return 0;
    }
    
    /* 键盘显示格式更简洁 */
    if (response->status == CTRL_STATUS_OK) {
        return snprintf(display_buffer, buffer_size, "OK: %s", response->message);
    } else {
        return snprintf(display_buffer, buffer_size, "ERR: %s", response->message);
    }
}

/**
 * @brief  添加串口数据到解析缓冲区
 * @param  data: 接收数据
 * @param  length: 数据长度
 * @retval None
 */
void CommandParser_AddUARTData(const uint8_t* data, uint16_t length)
{
    if (data == NULL || length == 0) return;
    
    for (uint16_t i = 0; i < length; i++) {
        if (uart_buffer_pos < CMD_BUFFER_SIZE - 1) {
            uart_buffer[uart_buffer_pos++] = data[i];
            uart_buffer[uart_buffer_pos] = '\0';
        }
    }
}

/**
 * @brief  处理待解析的串口命令
 * @param  command: 解析结果输出
 * @retval ParseStatus_t 解析状态
 */
ParseStatus_t CommandParser_ProcessPendingUART(ParsedCommand_t* command)
{
    if (command == NULL) return PARSE_STATUS_INVALID;
    
    /* 查找命令结束符 */
    char* cmd_end = strchr(uart_buffer, CMD_DELIMITER);
    if (cmd_end == NULL) {
        return PARSE_STATUS_INCOMPLETE;  // 命令不完整
    }
    
    /* 提取完整命令 */
    *cmd_end = '\0';
    ParseStatus_t status = ParseUARTCommand(uart_buffer, command);
    
    /* 移除已处理的命令 */
    uint16_t cmd_length = cmd_end - uart_buffer + 1;
    if (uart_buffer_pos > cmd_length) {
        memmove(uart_buffer, uart_buffer + cmd_length, uart_buffer_pos - cmd_length);
        uart_buffer_pos -= cmd_length;
        uart_buffer[uart_buffer_pos] = '\0';
    } else {
        CommandParser_ClearBuffer();
    }
    
    return status;
}

/**
 * @brief  清空解析缓冲区
 * @param  None
 * @retval None
 */
void CommandParser_ClearBuffer(void)
{
    memset(uart_buffer, 0, sizeof(uart_buffer));
    uart_buffer_pos = 0;
}

/* ==================== 私有函数实现 ==================== */

/**
 * @brief  解析串口命令字符串
 * @param  cmd_string: 命令字符串
 * @param  command: 解析结果输出
 * @retval ParseStatus_t 解析状态
 */
static ParseStatus_t ParseUARTCommand(const char* cmd_string, ParsedCommand_t* command)
{
    if (cmd_string == NULL || command == NULL) {
        return PARSE_STATUS_INVALID;
    }
    
    /* 初始化命令结构 */
    memset(command, 0, sizeof(ParsedCommand_t));
    command->source = CMD_SOURCE_UART;
    command->timestamp = SysTick_GetTick();
    
    /* 查找参数分隔符 */
    char* param_start = strchr(cmd_string, CMD_PARAM_SEPARATOR);
    
    if (strncmp(cmd_string, "SET_FREQ", 8) == 0) {
        command->command = CMD_SET_FREQUENCY;
        if (param_start) {
            command->param1 = strtoul(param_start + 1, NULL, 10);
        }
    }
    else if (strncmp(cmd_string, "SET_AMP", 7) == 0) {
        command->command = CMD_SET_AMPLITUDE;
        if (param_start) {
            command->param1 = strtoul(param_start + 1, NULL, 10);
        }
    }
    else if (strncmp(cmd_string, "SET_GAIN", 8) == 0) {
        command->command = CMD_SET_GAIN;
        if (param_start) {
            double gain = ParseDoubleParameter(param_start + 1);
            command->param1 = (uint32_t)gain;
            command->param2 = (uint32_t)((gain - command->param1) * 1000);
        }
    }
    else if (strncmp(cmd_string, "ENABLE_OUT", 10) == 0) {
        command->command = CMD_ENABLE_OUTPUT;
    }
    else if (strncmp(cmd_string, "DISABLE_OUT", 11) == 0) {
        command->command = CMD_DISABLE_OUTPUT;
    }
    else if (strncmp(cmd_string, "GET_STATUS", 10) == 0) {
        command->command = CMD_GET_STATUS;
    }
    else if (strncmp(cmd_string, "LOAD_PRESET", 11) == 0) {
        command->command = CMD_SET_PRESET;
        if (param_start) {
            command->param1 = strtoul(param_start + 1, NULL, 10);
        }
    }
    else if (strncmp(cmd_string, "RESET", 5) == 0) {
        command->command = CMD_RESET;
    }
    else {
        return PARSE_STATUS_INVALID;
    }
    
    return PARSE_STATUS_OK;
}

/**
 * @brief  解析键盘按键序列
 * @param  key_code: 按键代码
 * @param  command: 解析结果输出
 * @retval ParseStatus_t 解析状态
 */
static ParseStatus_t ParseKeypadSequence(uint8_t key_code, ParsedCommand_t* command)
{
    if (command == NULL) return PARSE_STATUS_INVALID;
    
    /* 初始化命令结构 */
    memset(command, 0, sizeof(ParsedCommand_t));
    command->source = CMD_SOURCE_KEYPAD;
    command->timestamp = SysTick_GetTick();
    
    /* 根据按键代码生成命令 */
    switch (key_code) {
        case KEY_FREQ_UP:
            command->command = CMD_SET_FREQUENCY;
            /* 获取当前频率并增加 */
            {
                SystemParams_t params;
                AD9910_Control_GetParams(&params);
                command->param1 = params.frequency_hz + 100000;  // 增加100kHz
            }
            break;
            
        case KEY_FREQ_DOWN:
            command->command = CMD_SET_FREQUENCY;
            /* 获取当前频率并减少 */
            {
                SystemParams_t params;
                AD9910_Control_GetParams(&params);
                command->param1 = (params.frequency_hz > 100000) ? (params.frequency_hz - 100000) : 1;
            }
            break;
            
        case KEY_AMP_UP:
            command->command = CMD_SET_AMPLITUDE;
            /* 获取当前幅度并增加 */
            {
                SystemParams_t params;
                AD9910_Control_GetParams(&params);
                command->param1 = params.target_amplitude_mv + 50;  // 增加50mV
            }
            break;
            
        case KEY_AMP_DOWN:
            command->command = CMD_SET_AMPLITUDE;
            /* 获取当前幅度并减少 */
            {
                SystemParams_t params;
                AD9910_Control_GetParams(&params);
                command->param1 = (params.target_amplitude_mv > 50) ? (params.target_amplitude_mv - 50) : 10;
            }
            break;
            
        case KEY_PRESET_1:
            command->command = CMD_SET_PRESET;
            command->param1 = 0;
            break;
            
        case KEY_PRESET_2:
            command->command = CMD_SET_PRESET;
            command->param1 = 1;
            break;
            
        case KEY_PRESET_3:
            command->command = CMD_SET_PRESET;
            command->param1 = 2;
            break;
            
        case KEY_PRESET_4:
            command->command = CMD_SET_PRESET;
            command->param1 = 3;
            break;
            
        case KEY_OUTPUT_TOGGLE:
            /* 切换输出状态 */
            {
                SystemParams_t params;
                AD9910_Control_GetParams(&params);
                command->command = params.output_enabled ? CMD_DISABLE_OUTPUT : CMD_ENABLE_OUTPUT;
            }
            break;
            
        default:
            return PARSE_STATUS_INVALID;
    }
    
    return PARSE_STATUS_OK;
}

/**
 * @brief  解析双精度浮点参数
 * @param  param_string: 参数字符串
 * @retval 解析得到的双精度值
 */
static double ParseDoubleParameter(const char* param_string)
{
    if (param_string == NULL) return 0.0;
    
    return strtod(param_string, NULL);
}

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
