/**
  ******************************************************************************
  * @file    陶晶池串口屏测试程序.c
  * <AUTHOR> 第三问 陶晶池串口屏测试
  * @version V1.0
  * @date    2024
  * @brief   陶晶池串口屏控制功能测试程序
  ******************************************************************************
  * @attention
  * 
  * 本文件提供陶晶池串口屏控制功能的测试代码
  * 可以独立运行，用于验证陶晶池串口屏功能是否正常
  * 
  ******************************************************************************
  */

/* 测试说明：
 * 1. 将此代码添加到main.c中的适当位置进行测试
 * 2. 或者创建独立的测试函数调用
 * 3. 通过陶晶池串口屏观察显示效果和交互响应
 */

#include "main.h"
#include "../Modules/Control/external_control.h"
#include "../Modules/Core/systick.h"

/**
 * @brief  陶晶池串口屏基础通信测试
 * @param  None
 * @retval None
 */
void TJC_BasicCommunicationTest(void)
{
    // 测试文本发送
    TJC_SendText("t_title", "TJC_TEST");
    Delay_ms(1000);
    
    // 测试数值发送
    TJC_SendValue("n_amp", 123);
    Delay_ms(1000);
    
    // 测试浮点数发送
    TJC_SendFloat("n_amp", 1.25, 2);  // 发送125
    Delay_ms(1000);
    
    // 测试状态显示
    TJC_SendText("t_status", "COMM_OK");
    Delay_ms(1000);
}

/**
 * @brief  陶晶池串口屏参数设置测试
 * @param  None
 * @retval None
 */
void TJC_ParameterTest(void)
{
    float test_frequencies[] = {1.0, 5.0, 10.0, 25.0, 50.0, 100.0};
    float test_amplitudes[] = {0.5, 1.0, 1.5, 2.0, 2.5, 3.0};
    
    TJC_SendText("t_status", "FREQ_TEST");
    
    // 测试频率设置
    for (int i = 0; i < 6; i++) {
        if (TJC_SetFrequency(test_frequencies[i]) == 0) {
            TJC_SendText("t_status", "FREQ_OK");
        } else {
            TJC_SendText("t_status", "FREQ_ERR");
        }
        Delay_ms(2000);
    }
    
    TJC_SendText("t_status", "AMP_TEST");
    
    // 测试幅度设置
    for (int i = 0; i < 6; i++) {
        if (TJC_SetAmplitude(test_amplitudes[i]) == 0) {
            TJC_SendText("t_status", "AMP_OK");
        } else {
            TJC_SendText("t_status", "AMP_ERR");
        }
        Delay_ms(2000);
    }
}

/**
 * @brief  陶晶池串口屏输出控制测试
 * @param  None
 * @retval None
 */
void TJC_OutputControlTest(void)
{
    TJC_SendText("t_status", "OUT_TEST");
    
    // 测试输出开关
    for (int i = 0; i < 10; i++) {
        TJC_SetOutput(i % 2);  // 交替开关
        Delay_ms(1000);
    }
    
    TJC_SendText("t_status", "OUT_OK");
}

/**
 * @brief  陶晶池串口屏页面控制测试
 * @param  None
 * @retval None
 */
void TJC_PageControlTest(void)
{
    TJC_SendText("t_status", "PAGE_TEST");
    
    // 测试页面跳转
    for (int page = 0; page < 3; page++) {
        TJC_GotoPage(page);
        Delay_ms(3000);
    }
    
    // 回到主页面
    TJC_GotoPage(TJC_PAGE_MAIN);
    TJC_SendText("t_status", "PAGE_OK");
}

/**
 * @brief  陶晶池串口屏控件可见性测试
 * @param  None
 * @retval None
 */
void TJC_VisibilityTest(void)
{
    TJC_SendText("t_status", "VIS_TEST");
    
    // 测试控件隐藏和显示
    const char* test_objects[] = {"t_freq", "n_amp", "sw_output"};
    
    for (int i = 0; i < 3; i++) {
        // 隐藏控件
        TJC_SetVisible(test_objects[i], 0);
        Delay_ms(1000);
        
        // 显示控件
        TJC_SetVisible(test_objects[i], 1);
        Delay_ms(1000);
    }
    
    TJC_SendText("t_status", "VIS_OK");
}

/**
 * @brief  陶晶池串口屏系统命令测试
 * @param  None
 * @retval None
 */
void TJC_SystemCommandTest(void)
{
    TJC_SendText("t_status", "SYS_TEST");
    
    // 测试获取属性
    TJC_GetAttribute("sw_output", "val");
    Delay_ms(1000);
    
    TJC_GetAttribute("n_amp", "val");
    Delay_ms(1000);
    
    // 测试系统命令 (注意：某些命令可能会重启屏幕)
    // TJC_SendSystemCommand("rest");  // 重启命令，谨慎使用
    
    TJC_SendText("t_status", "SYS_OK");
}

/**
 * @brief  陶晶池串口屏动态显示测试
 * @param  None
 * @retval None
 */
void TJC_DynamicDisplayTest(void)
{
    TJC_SendText("t_status", "DYN_TEST");
    
    // 动态频率显示
    for (float freq = 1.0; freq <= 10.0; freq += 0.5) {
        char freq_str[16];
        snprintf(freq_str, sizeof(freq_str), "%.1f", freq);
        TJC_SendText("t_freq", freq_str);
        Delay_ms(200);
    }
    
    // 动态幅度显示
    for (int amp = 50; amp <= 300; amp += 10) {
        TJC_SendValue("n_amp", amp);
        Delay_ms(100);
    }
    
    TJC_SendText("t_status", "DYN_OK");
}

/**
 * @brief  陶晶池串口屏综合功能演示
 * @param  None
 * @retval None
 */
void TJC_ComprehensiveDemo(void)
{
    // 显示演示开始
    TJC_SendText("t_title", "TJC_DEMO");
    TJC_SendText("t_status", "DEMO_START");
    Delay_ms(2000);
    
    // 模拟实际使用场景
    struct {
        float freq;
        float amp;
        uint8_t output;
        const char* status;
    } demo_scenarios[] = {
        {5.0, 1.0, 1, "SCENARIO_1"},
        {10.0, 2.0, 1, "SCENARIO_2"},
        {25.0, 1.5, 0, "SCENARIO_3"},
        {50.0, 0.8, 1, "SCENARIO_4"},
        {100.0, 3.0, 1, "SCENARIO_5"}
    };
    
    for (int i = 0; i < 5; i++) {
        TJC_SetFrequency(demo_scenarios[i].freq);
        TJC_SetAmplitude(demo_scenarios[i].amp);
        TJC_SetOutput(demo_scenarios[i].output);
        TJC_SendText("t_status", demo_scenarios[i].status);
        Delay_ms(3000);
    }
    
    TJC_SendText("t_status", "DEMO_END");
}

/**
 * @brief  陶晶池串口屏测试主函数
 * @param  None
 * @retval None
 * @note   在main函数中调用此函数进行测试
 */
void TJC_MainTest(void)
{
    // 初始化陶晶池串口屏
    if (TJC_Screen_Init() != 0) {
        // 初始化失败，LED快速闪烁
        while (1) {
            GPIO_ToggleBits(GPIOE, GPIO_Pin_6);
            Delay_ms(100);
        }
    }
    
    // LED慢闪表示初始化成功
    for (int i = 0; i < 6; i++) {
        GPIO_ToggleBits(GPIOE, GPIO_Pin_6);
        Delay_ms(300);
    }
    
    // 发送欢迎信息
    TJC_SendText("t_title", "TJC_READY");
    TJC_SendText("t_status", "TEST_START");
    TJC_SendText("t_freq", "0.000");
    TJC_SendValue("n_amp", 0);
    TJC_SetOutput(0);
    
    Delay_ms(3000);
    
    // 执行各项测试
    
    // 1. 基础通信测试
    TJC_BasicCommunicationTest();
    Delay_ms(2000);
    
    // 2. 参数设置测试
    TJC_ParameterTest();
    Delay_ms(2000);
    
    // 3. 输出控制测试
    TJC_OutputControlTest();
    Delay_ms(2000);
    
    // 4. 页面控制测试
    TJC_PageControlTest();
    Delay_ms(2000);
    
    // 5. 控件可见性测试
    TJC_VisibilityTest();
    Delay_ms(2000);
    
    // 6. 系统命令测试
    TJC_SystemCommandTest();
    Delay_ms(2000);
    
    // 7. 动态显示测试
    TJC_DynamicDisplayTest();
    Delay_ms(2000);
    
    // 8. 综合功能演示
    TJC_ComprehensiveDemo();
    Delay_ms(2000);
    
    // 发送测试完成信息
    TJC_SendText("t_title", "ALL_TESTS");
    TJC_SendText("t_status", "COMPLETED");
    
    // 进入正常工作模式
    while (1) {
        // 处理陶晶池串口屏数据
        ExternalControl_Process();
        
        // 定期更新显示
        static uint32_t update_counter = 0;
        update_counter++;
        
        if (update_counter % 1000000 == 0) {
            TJC_UpdateAllDisplay();
        }
        
        Delay_ms(1);
    }
}

/* 使用方法：
 * 
 * 方法1：在main函数中直接调用测试
 * int main(void) {
 *     SystemClock_Config();
 *     SysTick_Init();
 *     BSP_Init();
 *     
 *     TJC_MainTest();  // 调用测试函数
 * }
 * 
 * 方法2：在现有main函数中添加测试代码
 * 将各个测试函数添加到适当的位置进行测试
 * 
 * 方法3：陶晶池编辑器配合测试
 * 1. 使用陶晶池编辑器创建界面工程
 * 2. 添加相应的控件 (t_title, t_freq, n_amp, sw_output, t_status等)
 * 3. 连接陶晶池串口屏到STM32
 * 4. 运行测试程序观察效果
 * 
 * 测试步骤：
 * 1. 确认陶晶池串口屏正常启动
 * 2. 观察各项测试的显示效果
 * 3. 检查参数设置是否正确
 * 4. 验证页面跳转功能
 * 5. 测试触摸交互响应
 * 
 * 注意事项：
 * 1. 确保波特率设置为115200
 * 2. 控件名称必须与陶晶池工程中的名称一致
 * 3. 某些系统命令可能会重启屏幕，使用时要谨慎
 * 4. 建议先用串口助手测试通信是否正常
 */
