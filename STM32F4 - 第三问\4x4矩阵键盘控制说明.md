# STM32F4 AD9854 4x4矩阵键盘控制说明

## 📋 概述

成功集成4x4矩阵键盘到STM32F4 AD9854 DDS信号发生器项目中，实现了通过矩阵键盘对频率和幅度的精确控制。键盘采用低电平导通设计，支持数字输入、小数点输入和功能控制。

## 🎯 键盘布局

```
+-----+-----+-----+-----+
|  1  |  2  |  3  |  A  |  <- 第0行 (PC0)
+-----+-----+-----+-----+
|  4  |  5  |  6  |  B  |  <- 第1行 (PC1)
+-----+-----+-----+-----+
|  7  |  8  |  9  |  C  |  <- 第2行 (PC2)
+-----+-----+-----+-----+
|  *  |  0  |  #  |  D  |  <- 第3行 (PC3)
+-----+-----+-----+-----+
  ^     ^     ^     ^
 列0   列1   列2   列3
(PC4) (PC5) (PC6) (PC7)
```

## 🔧 硬件连接

### 矩阵键盘连接 (GPIOC)
```
STM32F407        4x4矩阵键盘
PC0 (行0)   →    Row 0
PC1 (行1)   →    Row 1
PC2 (行2)   →    Row 2
PC3 (行3)   →    Row 3
PC4 (列0)   ←    Col 0
PC5 (列1)   ←    Col 1
PC6 (列2)   ←    Col 2
PC7 (列3)   ←    Col 3
```

### 电气特性
- **工作电压**: 3.3V
- **导通方式**: 低电平导通
- **行引脚**: 输出模式，推挽输出
- **列引脚**: 输入模式，内部上拉
- **消抖时间**: 50ms

## 🎮 按键功能定义

### 数字键 (0-9)
- **功能**: 输入数值
- **用途**: 频率和幅度的数字输入
- **范围**: 支持整数和小数输入

### 功能键
| 按键 | 功能 | 说明 |
|------|------|------|
| **A** | 频率设置模式 | 进入频率输入模式 |
| **B** | 幅度设置模式 | 进入幅度输入模式 |
| **C** | 确认/应用 | 确认输入并应用参数 |
| **D** | 输出开关 | 切换AD9854输出开关 |
| **\*** | 清除/取消 | 清除当前输入 |
| **#** | 小数点 | 添加小数点 |

## 🚀 操作流程

### 1. 频率设置流程
```
1. 按 [A] 键 → 进入频率设置模式
2. 输入数字 → 例如: [5][.][0] = 5.0
3. 按 [C] 键 → 确认并应用 (设置为5.0MHz)
4. 串口屏显示 → "5.000MHz" 和 "FREQ_SET"
```

### 2. 幅度设置流程
```
1. 按 [B] 键 → 进入幅度设置模式
2. 输入数字 → 例如: [1][.][2] = 1.2
3. 按 [C] 键 → 确认并应用 (设置为1.2V)
4. 串口屏显示 → 幅度值更新和 "AMP_SET"
```

### 3. 输出控制
```
按 [D] 键 → 切换输出开关
串口屏显示 → "OUTPUT_ON" 或 "OUTPUT_OFF"
```

### 4. 输入纠错
```
按 [*] 键 → 清除当前输入，返回空闲模式
串口屏显示 → "CLEARED"
```

## 📊 参数范围

### 频率范围
- **最小值**: 0.1 MHz
- **最大值**: 150.0 MHz
- **精度**: 0.001 MHz (1 kHz)
- **示例**: 5.123 MHz

### 幅度范围
- **最小值**: 0.1 V
- **最大值**: 3.3 V
- **精度**: 0.01 V (10 mV)
- **示例**: 1.25 V

## 🔍 状态指示

### 串口屏显示
- **频率模式**: HMI_ERROR_TEXT 显示 "FREQ_MODE"
- **幅度模式**: HMI_ERROR_TEXT 显示 "AMP_MODE"
- **输入过程**: 实时显示输入内容 "F:5.0MHz" 或 "A:1.2V"
- **设置成功**: 显示 "FREQ_SET" 或 "AMP_SET"
- **设置错误**: 显示 "FREQ_ERR" 或 "AMP_ERR"
- **输出状态**: 显示 "OUTPUT_ON" 或 "OUTPUT_OFF"

### LED指示 (可选)
- 可通过不同的LED闪烁模式表示当前状态
- 预留接口，可根据需要扩展

## ⚙️ 技术实现

### 扫描算法
- **扫描方式**: 逐行扫描
- **扫描频率**: 受消抖时间限制 (50ms)
- **检测原理**: 行输出低电平，检测列输入状态

### 消抖处理
- **硬件消抖**: 无
- **软件消抖**: 50ms时间间隔限制
- **重复按键**: 自动过滤相同按键

### 数据处理
- **输入缓冲**: 16字符缓冲区
- **数据转换**: atof() 函数转换字符串为浮点数
- **范围检查**: 自动检查参数范围合法性

## 🎯 使用示例

### 示例1: 设置频率为10.5MHz
```
操作序列: [A] → [1] → [0] → [.] → [5] → [C]
结果: AD9854输出10.5MHz信号
显示: "10.500MHz" 和 "FREQ_SET"
```

### 示例2: 设置幅度为2.0V
```
操作序列: [B] → [2] → [.] → [0] → [C]
结果: AD9854输出2.0V峰峰值信号
显示: 幅度值200 (2.0*100) 和 "AMP_SET"
```

### 示例3: 关闭输出
```
操作序列: [D]
结果: AD9854停止输出
显示: "OUTPUT_OFF"
```

## ⚠️ 注意事项

### 硬件注意事项
1. **连接正确**: 确保行列引脚连接正确
2. **电平匹配**: 键盘必须支持3.3V电平
3. **上拉电阻**: 列引脚已配置内部上拉
4. **信号完整性**: 注意连线长度和干扰

### 软件注意事项
1. **参数范围**: 输入参数必须在有效范围内
2. **小数点**: 每个数值只能有一个小数点
3. **缓冲区**: 输入长度限制为15个字符
4. **模式切换**: 必须先进入相应模式才能输入数值

### 操作注意事项
1. **按键顺序**: 必须按正确顺序操作
2. **确认操作**: 输入完成后必须按[C]确认
3. **错误处理**: 输入错误时按[*]清除重新输入
4. **状态观察**: 注意观察串口屏的状态提示

## 🔧 调试功能

### 调试函数
- `MatrixKeypad_GetKeyChar()`: 获取按键对应字符
- 可用于调试按键扫描是否正常

### 状态查询
- 通过串口屏实时显示当前状态
- 支持输入过程的实时反馈

## 📈 性能特点

- **响应速度**: 50ms消抖时间，响应迅速
- **精度高**: 支持小数点输入，精度可达0.001MHz
- **操作简单**: 直观的按键布局和操作流程
- **错误处理**: 完善的参数检查和错误提示
- **实时反馈**: 串口屏实时显示操作状态

## 🎉 集成状态

- ✅ 硬件初始化完成
- ✅ 扫描算法实现
- ✅ 按键处理逻辑完成
- ✅ 参数设置功能完成
- ✅ 串口屏显示集成
- ✅ 编译测试通过
- ✅ 功能完整可用

**4x4矩阵键盘控制模块已成功集成，可立即投入使用！**
