/*********************************************************************************************/
【*】程序简介

-工程名称：AD9910-STM32-DDS驱动板-Vx.x
-实验平台: 康威DDS驱动板
-MDK版本：KEIL 5.14.0
-ST固件库版本：3.5.0
	工程文件位于"\AD9910-STM32-DDS驱动板-Vx.x\USER"目录下，用keil5软件打开VirtualCOMPort.uvprojx即可

【 ！】功能简介：
控制AD9910模块输出SINC波


【*】注意事项：



【 ！】实验操作：
1、使用排线连接驱动板与AD9910模块，驱动板上排线方向唯一，驱动板以及DDS模块上，板子背面双排针，方形焊盘为双排针1脚，对应连接即可
2、使用5V电源连接AD9910模块，使用5V电源连接DDS驱动板，
3、AD9910模块侧面输出高频头，使用SMA屏蔽线，连接示波器，(两个输出头，任意接一个即可)
3、编译该代码，使用jlink下载器下载代码到驱动板
4、确保AD9910模块已上电。按下驱动板复位键(或驱动板重新上电)，调整示波器时间档位到2uS/DIV,即可观察到SINC波输出


/*********************************************************************************************/

【*】 引脚分配
	
STM32控制板		模块	芯片引脚名	功能
PC13		----->	PWR	EXT_PWR_DWN.	掉电控制脚。数字输入。置高(3.3V)芯片关机，不使用需接地。
PA5		<---->	SDIO	SDIO.		串行数据输入/输出。
PC1		----->	DPH	DRHOLD.		数字斜坡保持。数字输入。置高(3.3V)有效,不使用需接地。
PC2		<-----	DRO	DROVER		数字斜坡结束。数字输出(高电平有效)
PC3		----->	IOUP	I/O UPDATE	同步信号(LVDS),数字输入(上升沿有效)。写完多个寄存器后给一个高脉冲。
PC4		----->	PF0		
PC10		----->	PF1		
PC5		----->	PF2	PROFILE[2:0]	Profile选择引脚。数字输入（高电平有效）。
PA6		----->	RST	MASTER RESET	主机复位，数字输入（高电平有效）。
PA2		----->	SCK	SCLK		串行数据时钟。数字时钟(上升沿执行写操作，下降沿执行读操作)
PA4		----->	DRC	DRCTL		数字斜坡控制。数字输入.控制数字斜坡发生器的斜率极性,不使用需接地。
PC8		----->	OSK	OSK		输出振幅键控。数字输入。
PB10		----->	CSB	CS加I/O_RESET	数字输入。SPI通信CS控制脚及通信复位脚。
GND		<---->	GND			控制板与AD9910模块需共地。
浮空		------	其他			所有未说明但模块有留出管脚，未使用可直接浮空，功能请参考数据手册

						

/*********************************************************************************************/

【*】 版本

-程序版本：0.6
-更新日期：2023-05-xx



/*********************************************************************************************/

【*】 联系我们

-淘宝店铺    https://kvdz.taobao.com

/*********************************************************************************************/