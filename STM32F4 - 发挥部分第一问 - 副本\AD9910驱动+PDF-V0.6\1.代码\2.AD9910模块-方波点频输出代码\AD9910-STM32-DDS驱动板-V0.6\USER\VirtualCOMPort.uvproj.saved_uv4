<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_proj.xsd">

  <SchemaVersion>1.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>VirtualCOMPort</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <TargetOption>
        <TargetCommonOption>
          <Device>STM32F103VE</Device>
          <Vendor>STMicroelectronics</Vendor>
          <Cpu>IRAM(0x20000000-0x2000FFFF) IROM(0x8000000-0x807FFFF) CLOCK(8000000) CPUTYPE("Cortex-M3")</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile>"STARTUP\ST\STM32F10x.s" ("STM32 Startup Code")</StartupFile>
          <FlashDriverDll>UL2CM3(-O14 -S0 -C0 -N00("ARM Cortex-M3") -D00(1BA00477) -L00(4) -FO7 -********** -FC800 -FN1 -FF0STM32F10x_512 -********** -FL080000)</FlashDriverDll>
          <DeviceId>4225</DeviceId>
          <RegisterFile>stm32f10x_lib.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile></SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath>ST\STM32F10x\</RegisterFilePath>
          <DBRegisterFilePath>ST\STM32F10x\</DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>..\OBJ\</OutputDirectory>
          <OutputName>VirtualCOMPort</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>..\LIS\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>0</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments></SimDllArguments>
          <SimDlgDll>DARMSTM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pSTM32F103VE</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments></TargetDllArguments>
          <TargetDlgDll>TARMSTM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pSTM32F103VE</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
          <Simulator>
            <UseSimulator>0</UseSimulator>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>1</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <LimitSpeedToRealTime>0</LimitSpeedToRealTime>
            <RestoreSysVw>0</RestoreSysVw>
          </Simulator>
          <Target>
            <UseTarget>1</UseTarget>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>0</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <RestoreTracepoints>0</RestoreTracepoints>
            <RestoreSysVw>0</RestoreSysVw>
          </Target>
          <RunDebugAfterBuild>0</RunDebugAfterBuild>
          <TargetSelection>6</TargetSelection>
          <SimDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
          </SimDlls>
          <TargetDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
            <Driver>Segger\JL2CM3.dll</Driver>
          </TargetDlls>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4101</DriverSelection>
          </Flash1>
          <bUseTDR>0</bUseTDR>
          <Flash2>Segger\JL2CM3.dll</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M3"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>0</RvdsVP>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>0</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x10000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x80000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x80000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x10000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>1</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>0</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>0</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>0</uC99>
            <useXO>0</useXO>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>STM32F10X_HD,USE_STDPERIPH_DRIVER</Define>
              <Undefine></Undefine>
              <IncludePath>..\CMSIS\CM3;..\CMSIS\CM3\CORE;..\CMSIS\CM3\STARTUP;..\CMSIS\STM32LIB\inc;..\SYSTEM\delay;..\SYSTEM\sys;..\SYSTEM\usart;..\USER;..\USB\USBLIB\inc;..\USB\Mass_Storage\inc;..\HARDWARE;..\HARDWARE\LED;..\HARDWARE\tft</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x08000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>CMSIS</GroupName>
          <Files>
            <File>
              <FileName>core_cm3.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\CMSIS\CM3\CORE\core_cm3.c</FilePath>
            </File>
            <File>
              <FileName>startup_stm32f10x_hd.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\CMSIS\CM3\STARTUP\startup_stm32f10x_hd.s</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_it.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\CMSIS\CM3\stm32f10x_it.c</FilePath>
            </File>
            <File>
              <FileName>system_stm32f10x.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\CMSIS\CM3\system_stm32f10x.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>FWLIB</GroupName>
          <Files>
            <File>
              <FileName>misc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\CMSIS\STM32LIB\src\misc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_exti.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\CMSIS\STM32LIB\src\stm32f10x_exti.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_fsmc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\CMSIS\STM32LIB\src\stm32f10x_fsmc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\CMSIS\STM32LIB\src\stm32f10x_gpio.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_rcc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\CMSIS\STM32LIB\src\stm32f10x_rcc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\CMSIS\STM32LIB\src\stm32f10x_usart.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>SYSTEM</GroupName>
          <Files>
            <File>
              <FileName>delay.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SYSTEM\delay\delay.c</FilePath>
            </File>
            <File>
              <FileName>sys.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SYSTEM\sys\sys.c</FilePath>
            </File>
            <File>
              <FileName>usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\SYSTEM\usart\usart.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>USBLIB</GroupName>
          <Files>
            <File>
              <FileName>usb_core.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USB\USBLIB\src\usb_core.c</FilePath>
            </File>
            <File>
              <FileName>usb_init.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USB\USBLIB\src\usb_init.c</FilePath>
            </File>
            <File>
              <FileName>usb_int.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USB\USBLIB\src\usb_int.c</FilePath>
            </File>
            <File>
              <FileName>usb_mem.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USB\USBLIB\src\usb_mem.c</FilePath>
            </File>
            <File>
              <FileName>usb_regs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USB\USBLIB\src\usb_regs.c</FilePath>
            </File>
            <File>
              <FileName>usb_sil.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USB\USBLIB\src\usb_sil.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>USBConfig</GroupName>
          <Files>
            <File>
              <FileName>hw_config.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USB\Mass_Storage\src\hw_config.c</FilePath>
            </File>
            <File>
              <FileName>usb_desc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USB\Mass_Storage\src\usb_desc.c</FilePath>
            </File>
            <File>
              <FileName>usb_endp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USB\Mass_Storage\src\usb_endp.c</FilePath>
            </File>
            <File>
              <FileName>usb_istr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USB\Mass_Storage\src\usb_istr.c</FilePath>
            </File>
            <File>
              <FileName>usb_prop.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USB\Mass_Storage\src\usb_prop.c</FilePath>
            </File>
            <File>
              <FileName>usb_pwr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USB\Mass_Storage\src\usb_pwr.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>USER</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\main.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>HARDWARE</GroupName>
          <Files>
            <File>
              <FileName>led.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\LED\led.c</FilePath>
            </File>
            <File>
              <FileName>fontupd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\tft\fontupd.c</FilePath>
            </File>
            <File>
              <FileName>lcd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\tft\lcd.c</FilePath>
            </File>
            <File>
              <FileName>lcd_drive.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HARDWARE\tft\lcd_drive.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

</Project>
