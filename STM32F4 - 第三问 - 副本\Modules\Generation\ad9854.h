/**
  ******************************************************************************
  * @file    ad9854.h
  * <AUTHOR> 第三问 AD9854 DDS信号发生器
  * @version V1.0
  * @date    2024
  * @brief   AD9854 DDS模块驱动头文件 - 专为5MHz/0.5V输出优化
  ******************************************************************************
  * @attention
  * 
  * AD9854技术规格：
  * - 系统时钟：300MHz (20MHz外部晶振 × 15倍频)
  * - 频率分辨率：48位 (0.0018Hz精度)
  * - 频率范围：0Hz ~ 150MHz
  * - 幅度控制：12位 (4096级精度)
  * - 输出峰峰值：0~500mV (通过Shape参数控制)
  * 
  * 硬件连接 (STM32F4 -> AD9854):
  * PA6  -> RST    (复位信号)
  * PA4  -> UDCLK  (更新时钟)
  * PA5  -> WR     (写使能)
  * PA8  -> RD     (读使能)
  * PA2  -> OSK    (OSK控制)
  * PB10 -> FSK    (FSK/BPSK/HOLD控制)
  * PC0-7 -> D0-D7 (8位数据总线)
  * PC8-13 -> A0-A5 (6位地址总线)
  * 
  ******************************************************************************
  */

#ifndef __AD9854_H
#define __AD9854_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include "stm32f4xx_gpio.h"
#include "stm32f4xx_rcc.h"
#include <stdint.h>
#include <math.h>

/* Exported types ------------------------------------------------------------*/
typedef enum {
    AD9854_OK = 0,
    AD9854_ERROR = 1,
    AD9854_TIMEOUT = 2
} AD9854_StatusTypeDef;

/* Exported constants --------------------------------------------------------*/
// AD9854系统时钟配置 (20MHz外部晶振 × 15倍频 = 300MHz)
#define AD9854_SYSCLK_HZ        300000000UL
#define AD9854_CLK_MULTIPLIER   15

// 频率和幅度限制
#define AD9854_MAX_FREQUENCY    150000000UL  // 150MHz最大频率
#define AD9854_MIN_FREQUENCY    1UL          // 1Hz最小频率
#define AD9854_MAX_AMPLITUDE    4095         // 12位幅度控制最大值
#define AD9854_MIN_AMPLITUDE    0            // 最小幅度

// 预设配置 - 5MHz/0.5V输出
#define AD9854_TARGET_FREQ      5000000.0    // 5MHz目标频率
#define AD9854_TARGET_AMPLITUDE 4095         // 0.5V峰峰值对应的幅度值

// GPIO引脚定义 (适配嘉立创天空星STM32F407)
// 基于天空星开发板的实际引脚资源分配
#define AD9854_RST_PORT         GPIOE
#define AD9854_RST_PIN          GPIO_Pin_4
#define AD9854_UDCLK_PORT       GPIOE
#define AD9854_UDCLK_PIN        GPIO_Pin_5
#define AD9854_WR_PORT          GPIOE
#define AD9854_WR_PIN           GPIO_Pin_6
#define AD9854_RD_PORT          GPIOB
#define AD9854_RD_PIN           GPIO_Pin_8
#define AD9854_OSK_PORT         GPIOB
#define AD9854_OSK_PIN          GPIO_Pin_9
#define AD9854_FSK_PORT         GPIOD
#define AD9854_FSK_PIN          GPIO_Pin_12

// 数据总线和地址总线 (使用GPIOD和GPIOE的可用引脚)
// 数据总线 D0-D7 使用GPIOD的PD0-PD7
#define AD9854_DATA_PORT        GPIOD
#define AD9854_DATA_PINS        (GPIO_Pin_0 | GPIO_Pin_1 | GPIO_Pin_2 | GPIO_Pin_3 | \
                                GPIO_Pin_4 | GPIO_Pin_5 | GPIO_Pin_6 | GPIO_Pin_7)

// 地址总线 A0-A5 使用GPIOE的PE8-PE13
#define AD9854_ADDR_PORT        GPIOE
#define AD9854_ADDR_PINS        (GPIO_Pin_8 | GPIO_Pin_9 | GPIO_Pin_10 | GPIO_Pin_11 | \
                                GPIO_Pin_12 | GPIO_Pin_13)

// 控制信号宏定义
#define AD9854_RST_HIGH()       GPIO_SetBits(AD9854_RST_PORT, AD9854_RST_PIN)
#define AD9854_RST_LOW()        GPIO_ResetBits(AD9854_RST_PORT, AD9854_RST_PIN)
#define AD9854_UDCLK_HIGH()     GPIO_SetBits(AD9854_UDCLK_PORT, AD9854_UDCLK_PIN)
#define AD9854_UDCLK_LOW()      GPIO_ResetBits(AD9854_UDCLK_PORT, AD9854_UDCLK_PIN)
#define AD9854_WR_HIGH()        GPIO_SetBits(AD9854_WR_PORT, AD9854_WR_PIN)
#define AD9854_WR_LOW()         GPIO_ResetBits(AD9854_WR_PORT, AD9854_WR_PIN)
#define AD9854_RD_HIGH()        GPIO_SetBits(GPIOB, AD9854_RD_PIN)
#define AD9854_RD_LOW()         GPIO_ResetBits(GPIOB, AD9854_RD_PIN)
#define AD9854_OSK_HIGH()       GPIO_SetBits(GPIOB, AD9854_OSK_PIN)
#define AD9854_OSK_LOW()        GPIO_ResetBits(GPIOB, AD9854_OSK_PIN)

// AD9854寄存器地址
#define AD9854_REG_PHASE1       0x00    // 相位寄存器1
#define AD9854_REG_PHASE2       0x02    // 相位寄存器2
#define AD9854_REG_FREQ1        0x04    // 频率寄存器1
#define AD9854_REG_FREQ2        0x0A    // 频率寄存器2
#define AD9854_REG_DELTA_FREQ   0x10    // 增量频率寄存器
#define AD9854_REG_UPDATE_CLK   0x16    // 更新时钟寄存器
#define AD9854_REG_RAMP_RATE    0x1A    // 斜率时钟寄存器
#define AD9854_REG_CONTROL      0x1D    // 控制寄存器
#define AD9854_REG_CLK_MULT     0x1E    // 时钟倍频寄存器
#define AD9854_REG_MODE         0x1F    // 模式寄存器
#define AD9854_REG_QDAC         0x20    // QDAC寄存器
#define AD9854_REG_I_MULT       0x21    // I通道倍增器
#define AD9854_REG_Q_MULT       0x23    // Q通道倍增器
#define AD9854_REG_OSK_MULT     0x25    // OSK倍增器

/* Exported macro ------------------------------------------------------------*/
// 频率转换宏 (48位频率字计算)
#define AD9854_FREQ_TO_WORD(freq) ((uint64_t)((double)(freq) * 281474976710656.0 / (double)AD9854_SYSCLK_HZ))

// 幅度转换宏 (12位幅度控制)
#define AD9854_VOLTAGE_TO_AMPLITUDE(voltage_mv) ((uint16_t)((voltage_mv) * 4095 / 500))

/* Exported functions --------------------------------------------------------*/

/**
 * @brief  AD9854初始化
 * @param  None
 * @retval AD9854_StatusTypeDef 初始化状态
 */
AD9854_StatusTypeDef AD9854_Init(void);

/**
 * @brief  AD9854 GPIO初始化
 * @param  None
 * @retval None
 */
void AD9854_GPIO_Init(void);

/**
 * @brief  AD9854写寄存器
 * @param  addr: 寄存器地址
 * @param  data: 写入数据
 * @retval None
 */
void AD9854_WriteReg(uint8_t addr, uint8_t data);

/**
 * @brief  AD9854设置频率 (高精度double版本)
 * @param  frequency: 频率值 (Hz)
 * @retval AD9854_StatusTypeDef 设置状态
 */
AD9854_StatusTypeDef AD9854_SetFrequency(double frequency);

/**
 * @brief  AD9854设置幅度
 * @param  amplitude: 幅度值 (0-4095)
 * @retval AD9854_StatusTypeDef 设置状态
 */
AD9854_StatusTypeDef AD9854_SetAmplitude(uint16_t amplitude);

/**
 * @brief  AD9854设置正弦波输出 (频率+幅度)
 * @param  frequency: 频率值 (Hz)
 * @param  amplitude: 幅度值 (0-4095)
 * @retval AD9854_StatusTypeDef 设置状态
 */
AD9854_StatusTypeDef AD9854_SetSineWave(double frequency, uint16_t amplitude);

/**
 * @brief  AD9854配置为5MHz/0.5V输出 (预设配置)
 * @param  None
 * @retval AD9854_StatusTypeDef 配置状态
 */
AD9854_StatusTypeDef AD9854_Config_5MHz_500mV(void);

/**
 * @brief  AD9854更新输出
 * @param  None
 * @retval None
 */
void AD9854_Update(void);

/**
 * @brief  AD9854复位
 * @param  None
 * @retval None
 */
void AD9854_Reset(void);

/**
 * @brief  延时函数 (微秒级)
 * @param  us: 延时时间 (微秒)
 * @retval None
 */
void AD9854_Delay_us(uint32_t us);

#ifdef __cplusplus
}
#endif

#endif /* __AD9854_H */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
