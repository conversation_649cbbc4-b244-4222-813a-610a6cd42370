/**
  ******************************************************************************
  * @file    gain_calculator.h
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   增益计算算法头文件 - 电赛G题专用
  ******************************************************************************
  * @attention
  *
  * 本文件实现电赛G题后续电路的增益计算算法，包括：
  * - 运放增益计算
  * - 滤波器增益补偿
  * - 频率相关增益校正
  * - 温度漂移补偿
  * - 非线性失真校正
  *
  * 电路链路：
  * AD9910 -> 缓冲器 -> 可变增益放大器 -> 滤波器 -> 输出
  *
  * 增益公式：
  * 总增益 = 缓冲器增益 × 可变增益 × 滤波器增益 × 频率校正系数
  *
  ******************************************************************************
  */

#ifndef __GAIN_CALCULATOR_H
#define __GAIN_CALCULATOR_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include <stdint.h>
#include <stdbool.h>

/* Exported types ------------------------------------------------------------*/

/**
 * @brief  增益级联结构体
 */
typedef struct {
    double buffer_gain;        ///< 缓冲器增益 (通常为1.0)
    double variable_gain;      ///< 可变增益放大器增益
    double filter_gain;        ///< 滤波器增益 (可能<1.0)
    double freq_correction;    ///< 频率校正系数
    double temp_correction;    ///< 温度校正系数
    double nonlinear_correction; ///< 非线性校正系数
} GainStage_t;

/**
 * @brief  增益配置结构体
 */
typedef struct {
    double target_total_gain;  ///< 目标总增益
    uint32_t frequency_hz;     ///< 工作频率
    int16_t temperature_c;     ///< 工作温度
    uint16_t input_amplitude_mv; ///< 输入幅度
    bool enable_nonlinear_correction; ///< 是否启用非线性校正
} GainConfig_t;

/**
 * @brief  增益计算结果结构体
 */
typedef struct {
    double calculated_gain;    ///< 计算得到的总增益
    double ad9910_output_mv;   ///< AD9910应输出的幅度
    double final_output_mv;    ///< 最终输出幅度
    double gain_error_percent; ///< 增益误差百分比
    bool is_valid;            ///< 计算结果是否有效
} GainResult_t;

/* Exported constants --------------------------------------------------------*/

/* 电赛G题典型电路参数 */
#define GAIN_BUFFER_TYPICAL         1.0     ///< 典型缓冲器增益
#define GAIN_VARIABLE_MIN           0.1     ///< 可变增益最小值
#define GAIN_VARIABLE_MAX           10.0    ///< 可变增益最大值
#define GAIN_FILTER_TYPICAL         0.95    ///< 典型滤波器增益
#define GAIN_TOTAL_MIN              0.05    ///< 总增益最小值
#define GAIN_TOTAL_MAX              20.0    ///< 总增益最大值

/* 频率校正参数 */
#define FREQ_CORRECTION_REF_HZ      1000000 ///< 参考频率 1MHz
#define FREQ_CORRECTION_SLOPE       -1e-8   ///< 频率校正斜率 (-10ppm/MHz)

/* 温度校正参数 */
#define TEMP_CORRECTION_REF_C       25      ///< 参考温度 25°C
#define TEMP_CORRECTION_COEFF       50e-6   ///< 温度系数 (50ppm/°C)

/* 非线性校正参数 */
#define NONLINEAR_THRESHOLD_LOW_MV  100     ///< 低幅度非线性阈值
#define NONLINEAR_THRESHOLD_HIGH_MV 3000    ///< 高幅度非线性阈值
#define NONLINEAR_CORRECTION_LOW    1.02    ///< 低幅度校正系数
#define NONLINEAR_CORRECTION_HIGH   0.98    ///< 高幅度校正系数

/* Exported functions --------------------------------------------------------*/

/**
 * @brief  增益计算器初始化
 * @param  None
 * @retval None
 */
void GainCalculator_Init(void);

/**
 * @brief  计算总增益
 * @param  config: 增益配置
 * @param  result: 计算结果输出
 * @retval true-计算成功, false-计算失败
 */
bool GainCalculator_Calculate(const GainConfig_t* config, GainResult_t* result);

/**
 * @brief  计算AD9910所需输出幅度
 * @param  target_output_mv: 目标输出幅度 (mV)
 * @param  total_gain: 总增益
 * @retval AD9910应输出的幅度 (mV)
 */
uint16_t GainCalculator_GetAD9910Output(uint16_t target_output_mv, double total_gain);

/**
 * @brief  计算最终输出幅度
 * @param  ad9910_output_mv: AD9910输出幅度 (mV)
 * @param  total_gain: 总增益
 * @retval 最终输出幅度 (mV)
 */
uint16_t GainCalculator_GetFinalOutput(uint16_t ad9910_output_mv, double total_gain);

/**
 * @brief  设置可变增益放大器增益
 * @param  variable_gain: 可变增益值
 * @retval true-设置成功, false-设置失败
 */
bool GainCalculator_SetVariableGain(double variable_gain);

/**
 * @brief  获取当前增益级联信息
 * @param  stages: 增益级联结构体输出
 * @retval None
 */
void GainCalculator_GetStages(GainStage_t* stages);

/**
 * @brief  频率校正系数计算
 * @param  frequency_hz: 工作频率
 * @retval 频率校正系数
 */
double GainCalculator_FrequencyCorrection(uint32_t frequency_hz);

/**
 * @brief  温度校正系数计算
 * @param  temperature_c: 工作温度
 * @retval 温度校正系数
 */
double GainCalculator_TemperatureCorrection(int16_t temperature_c);

/**
 * @brief  非线性校正系数计算
 * @param  amplitude_mv: 信号幅度
 * @retval 非线性校正系数
 */
double GainCalculator_NonlinearCorrection(uint16_t amplitude_mv);

/**
 * @brief  验证增益参数有效性
 * @param  gain: 增益值
 * @retval true-有效, false-无效
 */
bool GainCalculator_ValidateGain(double gain);

/**
 * @brief  优化增益分配
 * @param  target_gain: 目标总增益
 * @param  stages: 优化后的增益级联输出
 * @retval true-优化成功, false-优化失败
 */
bool GainCalculator_OptimizeStages(double target_gain, GainStage_t* stages);

/* ==================== 电赛G题专用函数 ==================== */

/**
 * @brief  电赛G题标准增益配置
 * @param  output_range: 输出范围 (1=0.1-1V, 2=1-10V, 3=10-100V)
 * @param  stages: 配置后的增益级联输出
 * @retval true-配置成功, false-配置失败
 */
bool GainCalculator_ConfigForContest(uint8_t output_range, GainStage_t* stages);

/**
 * @brief  电赛G题精度优化
 * @param  target_accuracy_percent: 目标精度 (%)
 * @param  config: 优化配置输出
 * @retval true-优化成功, false-优化失败
 */
bool GainCalculator_OptimizeAccuracy(double target_accuracy_percent, GainConfig_t* config);

/**
 * @brief  电赛G题动态范围计算
 * @param  min_output_mv: 最小输出 (mV)
 * @param  max_output_mv: 最大输出 (mV)
 * @retval 动态范围 (dB)
 */
double GainCalculator_CalculateDynamicRange(uint16_t min_output_mv, uint16_t max_output_mv);

/* ==================== 调试和测试函数 ==================== */

/**
 * @brief  增益计算自测试
 * @param  None
 * @retval true-测试通过, false-测试失败
 */
bool GainCalculator_SelfTest(void);

/**
 * @brief  打印增益计算详情 (调试用)
 * @param  config: 增益配置
 * @param  result: 计算结果
 * @retval None
 */
void GainCalculator_PrintDetails(const GainConfig_t* config, const GainResult_t* result);

#ifdef __cplusplus
}
#endif

#endif /* __GAIN_CALCULATOR_H */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
