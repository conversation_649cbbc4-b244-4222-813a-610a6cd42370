/**
  ******************************************************************************
  * @file    tjc_hmi.h
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   陶晶池串口屏驱动头文件
  ******************************************************************************
  * @attention
  *
  * 本文件实现陶晶池串口屏的通信功能，包括：
  * - 串口通信协议
  * - 控件数据更新
  * - 触摸事件处理
  * - AD9910参数显示
  *
  * 陶晶池串口屏特点:
  * - 指令格式: 指令内容 + 0xFF 0xFF 0xFF
  * - 波特率: 115200 (默认)
  * - 数据位: 8位，停止位: 1位，校验位: 无
  * - 支持触摸返回事件
  *
  * 硬件连接:
  * PA9  -> TJC_TX (串口屏RX)
  * PA10 -> TJC_RX (串口屏TX)
  *
  ******************************************************************************
  */

#ifndef __TJC_HMI_H
#define __TJC_HMI_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include <stdint.h>
#include <stdbool.h>

/* Exported types ------------------------------------------------------------*/

/**
 * @brief  陶晶池串口屏事件类型
 */
typedef enum {
    TJC_EVENT_NONE = 0,        ///< 无事件
    TJC_EVENT_TOUCH_PRESS,     ///< 触摸按下
    TJC_EVENT_TOUCH_RELEASE,   ///< 触摸释放
    TJC_EVENT_PAGE_CHANGE,     ///< 页面切换
    TJC_EVENT_SLIDER_CHANGE,   ///< 滑块变化
    TJC_EVENT_TEXT_INPUT       ///< 文本输入
} TJC_EventType_t;

/**
 * @brief  陶晶池串口屏事件结构体
 */
typedef struct {
    TJC_EventType_t type;      ///< 事件类型
    uint8_t page_id;           ///< 页面ID
    uint8_t component_id;      ///< 控件ID
    uint32_t value;            ///< 事件值
    char text[32];             ///< 文本内容 (文本输入事件)
} TJC_Event_t;

/**
 * @brief  陶晶池串口屏回调函数类型
 */
typedef void (*TJC_EventCallback_t)(TJC_Event_t* event);

/* Exported constants --------------------------------------------------------*/

/* 串口配置 */
#define TJC_USART                   USART1
#define TJC_USART_RCC               RCC_APB2Periph_USART1
#define TJC_USART_TX_RCC            RCC_AHB1Periph_GPIOA
#define TJC_USART_RX_RCC            RCC_AHB1Periph_GPIOA

#define TJC_USART_TX_PORT           GPIOA
#define TJC_USART_TX_PIN            GPIO_Pin_9
#define TJC_USART_RX_PORT           GPIOA
#define TJC_USART_RX_PIN            GPIO_Pin_10
#define TJC_USART_AF                GPIO_AF_USART1
#define TJC_USART_TX_AF_PIN         GPIO_PinSource9
#define TJC_USART_RX_AF_PIN         GPIO_PinSource10

/* 通信配置 */
#define TJC_BAUD_RATE               115200      ///< 波特率
#define TJC_RX_BUFFER_SIZE          256         ///< 接收缓冲区大小
#define TJC_CMD_END_BYTES           {0xFF, 0xFF, 0xFF}  ///< 命令结束符

/* 页面定义 */
#define TJC_PAGE_MAIN               0           ///< 主页面
#define TJC_PAGE_SETTINGS           1           ///< 设置页面
#define TJC_PAGE_ABOUT              2           ///< 关于页面

/* 主页面控件ID */
#define TJC_MAIN_FREQ_TEXT          1           ///< 频率显示文本
#define TJC_MAIN_FREQ_UNIT          2           ///< 频率单位文本
#define TJC_MAIN_AMP_TEXT           3           ///< 幅度显示文本
#define TJC_MAIN_AMP_UNIT           4           ///< 幅度单位文本
#define TJC_MAIN_GAIN_TEXT          5           ///< 增益显示文本
#define TJC_MAIN_STATUS_TEXT        6           ///< 状态显示文本
#define TJC_MAIN_ERROR_TEXT         7           ///< 错误显示文本
#define TJC_MAIN_OUTPUT_LED         8           ///< 输出状态LED
#define TJC_MAIN_FREQ_SLIDER        9           ///< 频率调节滑块
#define TJC_MAIN_AMP_SLIDER         10          ///< 幅度调节滑块

/* 按钮控件ID */
#define TJC_BTN_OUTPUT_TOGGLE       20          ///< 输出开关按钮
#define TJC_BTN_FREQ_UP             21          ///< 频率增加按钮
#define TJC_BTN_FREQ_DOWN           22          ///< 频率减少按钮
#define TJC_BTN_AMP_UP              23          ///< 幅度增加按钮
#define TJC_BTN_AMP_DOWN            24          ///< 幅度减少按钮
#define TJC_BTN_PRESET_1            25          ///< 预设1按钮
#define TJC_BTN_PRESET_2            26          ///< 预设2按钮
#define TJC_BTN_PRESET_3            27          ///< 预设3按钮
#define TJC_BTN_RESET               28          ///< 复位按钮

/* Exported functions --------------------------------------------------------*/

/**
 * @brief  陶晶池串口屏初始化
 * @param  None
 * @retval None
 */
void TJC_HMI_Init(void);

/**
 * @brief  陶晶池串口屏处理 (主循环调用)
 * @param  None
 * @retval None
 */
void TJC_HMI_Process(void);

/**
 * @brief  发送命令到陶晶池串口屏
 * @param  cmd: 命令字符串
 * @retval None
 */
void TJC_HMI_SendCommand(const char* cmd);

/**
 * @brief  设置文本控件内容
 * @param  obj_name: 控件名称
 * @param  text: 文本内容
 * @retval None
 */
void TJC_HMI_SetText(const char* obj_name, const char* text);

/**
 * @brief  设置数值控件内容
 * @param  obj_name: 控件名称
 * @param  value: 数值
 * @retval None
 */
void TJC_HMI_SetValue(const char* obj_name, int32_t value);

/**
 * @brief  设置进度条/滑块值
 * @param  obj_name: 控件名称
 * @param  value: 进度值 (0-100)
 * @retval None
 */
void TJC_HMI_SetProgress(const char* obj_name, uint8_t value);

/**
 * @brief  切换页面
 * @param  page_id: 页面ID
 * @retval None
 */
void TJC_HMI_ChangePage(uint8_t page_id);

/**
 * @brief  设置控件可见性
 * @param  obj_name: 控件名称
 * @param  visible: true-可见, false-隐藏
 * @retval None
 */
void TJC_HMI_SetVisible(const char* obj_name, bool visible);

/**
 * @brief  设置控件颜色
 * @param  obj_name: 控件名称
 * @param  color: 颜色值 (RGB565)
 * @retval None
 */
void TJC_HMI_SetColor(const char* obj_name, uint16_t color);

/**
 * @brief  注册事件回调函数
 * @param  callback: 回调函数指针
 * @retval None
 */
void TJC_HMI_RegisterCallback(TJC_EventCallback_t callback);

/* ==================== AD9910专用显示函数 ==================== */

/**
 * @brief  显示欢迎界面
 * @param  None
 * @retval None
 */
void TJC_HMI_ShowWelcome(void);

/**
 * @brief  设置频率显示
 * @param  frequency_hz: 频率值 (Hz)
 * @retval None
 */
void TJC_HMI_SetFrequency(uint32_t frequency_hz);

/**
 * @brief  设置幅度显示
 * @param  amplitude_mv: 幅度值 (mV)
 * @retval None
 */
void TJC_HMI_SetAmplitude(uint16_t amplitude_mv);

/**
 * @brief  设置增益显示
 * @param  gain: 增益值
 * @retval None
 */
void TJC_HMI_SetGain(double gain);

/**
 * @brief  设置输出状态显示
 * @param  enabled: 输出状态
 * @retval None
 */
void TJC_HMI_SetOutputStatus(bool enabled);

/**
 * @brief  设置系统状态显示
 * @param  status: 状态文本
 * @retval None
 */
void TJC_HMI_SetStatus(const char* status);

/**
 * @brief  设置错误信息显示
 * @param  error: 错误文本
 * @retval None
 */
void TJC_HMI_SetError(const char* error);

/**
 * @brief  更新所有AD9910参数显示
 * @param  None
 * @retval None
 */
void TJC_HMI_UpdateAllParameters(void);

/**
 * @brief  串口中断处理函数
 * @param  None
 * @retval None
 */
void TJC_HMI_UART_IRQHandler(void);

/**
 * @brief  获取是否有新事件
 * @param  event: 事件输出
 * @retval true-有事件, false-无事件
 */
bool TJC_HMI_HasEvent(TJC_Event_t* event);

/**
 * @brief  清空事件队列
 * @param  None
 * @retval None
 */
void TJC_HMI_ClearEvents(void);

#ifdef __cplusplus
}
#endif

#endif /* __TJC_HMI_H */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
