/**
  ******************************************************************************
  * @file    tjc_hmi.h
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   陶晶池串口屏驱动头文件
  ******************************************************************************
  * @attention
  *
  * 本文件实现陶晶池串口屏的通信功能，包括：
  * - 串口通信协议
  * - 控件数据更新
  * - 触摸事件处理
  * - AD9910参数显示
  *
  * 陶晶池串口屏特点:
  * - 指令格式: 指令内容 + 0xFF 0xFF 0xFF
  * - 波特率: 115200 (默认)
  * - 数据位: 8位，停止位: 1位，校验位: 无
  * - 支持触摸返回事件
  *
  * 硬件连接:
  * PA9  -> TJC_TX (串口屏RX)
  * PA10 -> TJC_RX (串口屏TX)
  *
  ******************************************************************************
  */

#ifndef __TJC_HMI_H
#define __TJC_HMI_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include <stdint.h>
#include <stdbool.h>

/* Exported types ------------------------------------------------------------*/

/**
 * @brief  陶晶池串口屏事件类型
 */
typedef enum {
    TJC_EVENT_NONE = 0,        ///< 无事件
    TJC_EVENT_TOUCH_PRESS,     ///< 触摸按下
    TJC_EVENT_TOUCH_RELEASE,   ///< 触摸释放
    TJC_EVENT_PAGE_CHANGE,     ///< 页面切换
    TJC_EVENT_SLIDER_CHANGE,   ///< 滑块变化
    TJC_EVENT_TEXT_INPUT       ///< 文本输入
} TJC_EventType_t;

/**
 * @brief  陶晶池串口屏事件结构体
 */
typedef struct {
    TJC_EventType_t type;      ///< 事件类型
    uint8_t page_id;           ///< 页面ID
    uint8_t component_id;      ///< 控件ID
    uint32_t value;            ///< 事件值
    char text[32];             ///< 文本内容 (文本输入事件)
} TJC_Event_t;

/**
 * @brief  陶晶池串口屏回调函数类型
 */
typedef void (*TJC_EventCallback_t)(TJC_Event_t* event);

/* Exported constants --------------------------------------------------------*/

/* 串口配置 - 适配嘉立创天空星STM32F407VGT6 */
#define TJC_USART                   USART2
#define TJC_USART_RCC               RCC_APB1Periph_USART2
#define TJC_USART_TX_RCC            RCC_AHB1Periph_GPIOA
#define TJC_USART_RX_RCC            RCC_AHB1Periph_GPIOA

#define TJC_USART_TX_PORT           GPIOA
#define TJC_USART_TX_PIN            GPIO_Pin_2     // PA2 (USART2_TX)
#define TJC_USART_RX_PORT           GPIOA
#define TJC_USART_RX_PIN            GPIO_Pin_3     // PA3 (USART2_RX)
#define TJC_USART_AF                GPIO_AF_USART2
#define TJC_USART_TX_AF_PIN         GPIO_PinSource2
#define TJC_USART_RX_AF_PIN         GPIO_PinSource3

/* 通信配置 */
#define TJC_BAUD_RATE               115200      ///< 波特率
#define TJC_RX_BUFFER_SIZE          256         ///< 接收缓冲区大小
#define TJC_CMD_END_BYTES           {0xFF, 0xFF, 0xFF}  ///< 命令结束符

/* 页面定义 */
#define TJC_PAGE_MAIN               0           ///< 主页面 (page0)
#define TJC_PAGE_MEASURE            1           ///< 测量页面 (page1)

/* Page0 控件定义 */
#define TJC_CTRL_T0                 "t0"        ///< 全键盘输入文本 (单位设置)
#define TJC_CTRL_T2                 "t2"        ///< 输出频率显示文本
#define TJC_CTRL_T4                 "t4"        ///< 电路电压输出峰峰值文本
#define TJC_CTRL_N0                 "n0"        ///< 数字键盘输入
#define TJC_CTRL_B0                 "b0"        ///< 测量/切换按钮

/* Page1 控件定义 */
#define TJC_CTRL_T5                 "t5"        ///< "未知电路滤波为："标签
#define TJC_CTRL_T6                 "t6"        ///< 滤波类型显示文本

/* 控件ID定义 (用于事件识别) */
#define TJC_ID_T0                   0           ///< t0控件ID
#define TJC_ID_T2                   2           ///< t2控件ID
#define TJC_ID_T4                   4           ///< t4控件ID
#define TJC_ID_N0                   10          ///< n0控件ID
#define TJC_ID_B0                   20          ///< b0控件ID
#define TJC_ID_T5                   5           ///< t5控件ID
#define TJC_ID_T6                   6           ///< t6控件ID

/* 特殊事件码 */
#define TJC_EVENT_MEASURE_START     31          ///< 测量开始事件码 (printh 31)

/* Exported functions --------------------------------------------------------*/

/**
 * @brief  陶晶池串口屏初始化
 * @param  None
 * @retval None
 */
void TJC_HMI_Init(void);

/**
 * @brief  陶晶池串口屏处理 (主循环调用)
 * @param  None
 * @retval None
 */
void TJC_HMI_Process(void);

/**
 * @brief  发送命令到陶晶池串口屏
 * @param  cmd: 命令字符串
 * @retval None
 */
void TJC_HMI_SendCommand(const char* cmd);

/**
 * @brief  设置文本控件内容
 * @param  obj_name: 控件名称
 * @param  text: 文本内容
 * @retval None
 */
void TJC_HMI_SetText(const char* obj_name, const char* text);

/**
 * @brief  设置数值控件内容
 * @param  obj_name: 控件名称
 * @param  value: 数值
 * @retval None
 */
void TJC_HMI_SetValue(const char* obj_name, int32_t value);

/**
 * @brief  设置进度条/滑块值
 * @param  obj_name: 控件名称
 * @param  value: 进度值 (0-100)
 * @retval None
 */
void TJC_HMI_SetProgress(const char* obj_name, uint8_t value);

/**
 * @brief  切换页面
 * @param  page_id: 页面ID
 * @retval None
 */
void TJC_HMI_ChangePage(uint8_t page_id);

/**
 * @brief  设置控件可见性
 * @param  obj_name: 控件名称
 * @param  visible: true-可见, false-隐藏
 * @retval None
 */
void TJC_HMI_SetVisible(const char* obj_name, bool visible);

/**
 * @brief  设置控件颜色
 * @param  obj_name: 控件名称
 * @param  color: 颜色值 (RGB565)
 * @retval None
 */
void TJC_HMI_SetColor(const char* obj_name, uint16_t color);

/**
 * @brief  注册事件回调函数
 * @param  callback: 回调函数指针
 * @retval None
 */
void TJC_HMI_RegisterCallback(TJC_EventCallback_t callback);

/* ==================== AD9910专用显示函数 ==================== */

/**
 * @brief  显示欢迎界面并初始化page0
 * @param  None
 * @retval None
 */
void TJC_HMI_ShowWelcome(void);

/**
 * @brief  设置输出频率显示 (t2控件，带单位)
 * @param  frequency_hz: 频率值 (Hz)
 * @retval None
 */
void TJC_HMI_SetOutputFrequency(uint32_t frequency_hz);

/**
 * @brief  设置电路电压输出峰峰值 (t4控件，考虑增益)
 * @param  voltage_mv: 电压峰峰值 (mV)
 * @retval None
 */
void TJC_HMI_SetCircuitVoltage(uint16_t voltage_mv);

/**
 * @brief  设置滤波类型显示 (t6控件)
 * @param  filter_type: 滤波类型字符串
 * @retval None
 */
void TJC_HMI_SetFilterType(const char* filter_type);

/**
 * @brief  处理数字键盘输入 (n0控件)
 * @param  value: 输入的数值
 * @retval None
 */
void TJC_HMI_HandleNumberInput(uint32_t value);

/**
 * @brief  处理全键盘输入 (t0控件，单位设置)
 * @param  unit_text: 输入的单位文本
 * @retval None
 */
void TJC_HMI_HandleUnitInput(const char* unit_text);

/**
 * @brief  处理测量按钮事件 (b0控件)
 * @param  current_page: 当前页面
 * @retval None
 */
void TJC_HMI_HandleMeasureButton(uint8_t current_page);

/**
 * @brief  开始测量未知电路
 * @param  None
 * @retval None
 */
void TJC_HMI_StartMeasurement(void);

/**
 * @brief  更新所有显示参数
 * @param  None
 * @retval None
 */
void TJC_HMI_UpdateAllParameters(void);

/**
 * @brief  串口中断处理函数
 * @param  None
 * @retval None
 */
void TJC_HMI_UART_IRQHandler(void);

/**
 * @brief  获取是否有新事件
 * @param  event: 事件输出
 * @retval true-有事件, false-无事件
 */
bool TJC_HMI_HasEvent(TJC_Event_t* event);

/**
 * @brief  清空事件队列
 * @param  None
 * @retval None
 */
void TJC_HMI_ClearEvents(void);

#ifdef __cplusplus
}
#endif

#endif /* __TJC_HMI_H */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
