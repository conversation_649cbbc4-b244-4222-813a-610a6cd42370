/**
  ******************************************************************************
  * @file    key.c
  * <AUTHOR>
  * @version V2.0
  * @date    2024
  * @brief   独立按键扫描模块实现 - 简化稳定版
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "key.h"
#include "systick.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/

/** @defgroup KEY_Private_Defines 按键私有定义
  * @{
  */

#define KEY_GPIO_PORT               GPIOE
#define KEY_GPIO_CLK                RCC_AHB1Periph_GPIOE
#define KEY0_PIN                    GPIO_Pin_4   ///< PE4
#define KEY1_PIN                    GPIO_Pin_3   ///< PE3

/**
  * @}
  */

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/** @defgroup KEY_Private_Variables 按键私有变量
  * @{
  */

volatile bool g_key_initialized = false;        ///< 按键初始化标志

// 按键状态记录
static Key_State_t s_key_states[3] = {KEY_STATE_RELEASED}; // [0]未使用, [1]KEY0, [2]KEY1
static uint32_t s_key_debounce_time[3] = {0};    // 消抖计时
static uint32_t s_last_scan_time = 0;            // 上次扫描时间

/**
  * @}
  */

/* Private function prototypes -----------------------------------------------*/
static void Key_GPIO_Config(void);
static bool Key_ReadRaw(Key_Number_t key_num);

/* Private functions ---------------------------------------------------------*/

/**
  * @brief  按键GPIO配置
  * @param  None
  * @retval None
  */
static void Key_GPIO_Config(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    
    // 使能GPIO时钟
    RCC_AHB1PeriphClockCmd(KEY_GPIO_CLK, ENABLE);
    
    // 配置按键引脚为上拉输入
    GPIO_InitStructure.GPIO_Pin = KEY0_PIN | KEY1_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_2MHz;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(KEY_GPIO_PORT, &GPIO_InitStructure);
}

/**
  * @brief  读取按键原始状态
  * @param  key_num: 按键编号
  * @retval true: 按下, false: 未按下
  */
static bool Key_ReadRaw(Key_Number_t key_num)
{
    switch (key_num) {
        case KEY_0:
            return KEY0_PRESSED();
        case KEY_1:
            return KEY1_PRESSED();
        default:
            return false;
    }
}

/* Exported functions --------------------------------------------------------*/

/**
  * @brief  按键初始化
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t Key_Init(void)
{
    // 配置GPIO
    Key_GPIO_Config();
    
    // 初始化状态
    for (uint8_t i = 0; i < 3; i++) {
        s_key_states[i] = KEY_STATE_RELEASED;
        s_key_debounce_time[i] = 0;
    }
    
    s_last_scan_time = SysTick_GetTick();
    g_key_initialized = true;
    
    return 0;
}

/**
  * @brief  按键反初始化
  * @param  None
  * @retval None
  */
void Key_DeInit(void)
{
    g_key_initialized = false;
}

/**
  * @brief  按键扫描
  * @param  None
  * @retval 按键编号(KEY_NONE/KEY_0/KEY_1)
  */
Key_Number_t Key_Scan(void)
{
    if (!g_key_initialized) return KEY_NONE;
    
    uint32_t current_time = SysTick_GetTick();
    
    // 控制扫描频率
    if (current_time - s_last_scan_time < KEY_SCAN_INTERVAL_MS) {
        return KEY_NONE;
    }
    s_last_scan_time = current_time;
    
    // 扫描所有按键
    for (Key_Number_t key = KEY_0; key <= KEY_1; key++) {
        bool key_pressed = Key_ReadRaw(key);
        
        switch (s_key_states[key]) {
            case KEY_STATE_RELEASED:
                if (key_pressed) {
                    s_key_states[key] = KEY_STATE_DEBOUNCE;
                    s_key_debounce_time[key] = current_time;
                }
                break;
                
            case KEY_STATE_DEBOUNCE:
                if (key_pressed) {
                    if (current_time - s_key_debounce_time[key] >= KEY_DEBOUNCE_TIME_MS) {
                        s_key_states[key] = KEY_STATE_PRESSED;
                        return key; // 返回按下的按键
                    }
                } else {
                    s_key_states[key] = KEY_STATE_RELEASED;
                }
                break;
                
            case KEY_STATE_PRESSED:
                if (!key_pressed) {
                    s_key_states[key] = KEY_STATE_RELEASED;
                }
                break;
        }
    }
    
    return KEY_NONE;
}

/**
  * @brief  获取按键状态
  * @param  key_num: 按键编号
  * @retval 按键状态
  */
Key_State_t Key_GetState(Key_Number_t key_num)
{
    if (!g_key_initialized || key_num < KEY_0 || key_num > KEY_1) {
        return KEY_STATE_RELEASED;
    }
    
    return s_key_states[key_num];
}

/**
  * @brief  检查按键是否按下
  * @param  key_num: 按键编号
  * @retval true: 按下, false: 未按下
  */
bool Key_IsPressed(Key_Number_t key_num)
{
    return (Key_GetState(key_num) == KEY_STATE_PRESSED);
}

/**
  * @brief  等待按键按下
  * @param  key_num: 按键编号
  * @param  timeout_ms: 超时时间(ms), 0表示无限等待
  * @retval true: 按键按下, false: 超时
  */
bool Key_WaitPress(Key_Number_t key_num, uint32_t timeout_ms)
{
    if (!g_key_initialized) return false;
    
    uint32_t start_time = SysTick_GetTick();
    
    while (1) {
        if (Key_Scan() == key_num) {
            return true;
        }
        
        if (timeout_ms > 0 && (SysTick_GetTick() - start_time) >= timeout_ms) {
            return false; // 超时
        }
        
        Delay_ms(1); // 短暂延时
    }
}

/**
  * @brief  等待按键释放
  * @param  key_num: 按键编号
  * @param  timeout_ms: 超时时间(ms), 0表示无限等待
  * @retval true: 按键释放, false: 超时
  */
bool Key_WaitRelease(Key_Number_t key_num, uint32_t timeout_ms)
{
    if (!g_key_initialized) return false;
    
    uint32_t start_time = SysTick_GetTick();
    
    while (Key_IsPressed(key_num)) {
        if (timeout_ms > 0 && (SysTick_GetTick() - start_time) >= timeout_ms) {
            return false; // 超时
        }
        
        Key_Scan(); // 更新状态
        Delay_ms(1); // 短暂延时
    }
    
    return true;
}

/**
  * @brief  检查按键是否就绪
  * @param  None
  * @retval true: 就绪, false: 未就绪
  */
bool Key_IsReady(void)
{
    return g_key_initialized;
}

/************************ (C) COPYRIGHT 嵌入式竞赛团队 *****END OF FILE****/
