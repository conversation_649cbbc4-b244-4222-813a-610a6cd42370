/**
  ******************************************************************************
  * @file    ad9910_hal.h
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   AD9910硬件抽象层头文件 - STM32F4平台适配
  ******************************************************************************
  * @attention
  *
  * 本文件实现AD9910 DDS芯片的底层硬件接口，包括：
  * - GPIO引脚配置和控制
  * - SPI通信接口
  * - 时序控制函数
  * - 硬件初始化
  *
  * 硬件连接：
  * PC13 -> PWR   (电源控制)
  * PA5  -> SDIO  (串行数据I/O)
  * PC1  -> DPH   (数据保持)
  * PC2  -> DRO   (数据溢出)
  * PC3  -> IOUP  (I/O更新)
  * PC4  -> PF0   (Profile选择0)
  * PC10 -> PF1   (Profile选择1)
  * PC5  -> PF2   (Profile选择2)
  * PA6  -> RST   (主复位)
  * PA2  -> SCK   (串行时钟)
  * PA4  -> DRC   (数据控制)
  * PC8  -> OSK   (输出移位键控)
  * PB10 -> CSB   (片选信号)
  *
  ******************************************************************************
  */

#ifndef __AD9910_HAL_H
#define __AD9910_HAL_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include "stm32f4xx_gpio.h"
#include "stm32f4xx_rcc.h"

/* Exported types ------------------------------------------------------------*/
typedef unsigned char  uchar;
typedef unsigned int   uint;
typedef unsigned long  ulong;

/* Exported constants --------------------------------------------------------*/

/* AD9910技术规格 */
#define AD9910_SYSTEM_CLOCK         1000000000UL  // 1GHz系统时钟
#define AD9910_MAX_FREQ_HZ          420000000UL   // 420MHz最大频率
#define AD9910_MIN_FREQ_HZ          1UL           // 1Hz最小频率
#define AD9910_FREQ_RESOLUTION_HZ   0.23f         // 频率分辨率 (1GHz/2^32)
#define AD9910_MAX_AMPLITUDE_MV     800           // 最大输出幅度 800mV
#define AD9910_AMPLITUDE_STEPS      16384         // 14位幅度控制 (2^14)

/* GPIO引脚定义 */
#define AD9910_PWR_PORT             GPIOC
#define AD9910_PWR_PIN              GPIO_Pin_13

#define AD9910_SDIO_PORT            GPIOA
#define AD9910_SDIO_PIN             GPIO_Pin_5

#define AD9910_DPH_PORT             GPIOC
#define AD9910_DPH_PIN              GPIO_Pin_1

#define AD9910_DRO_PORT             GPIOC
#define AD9910_DRO_PIN              GPIO_Pin_2

#define AD9910_IOUP_PORT            GPIOC
#define AD9910_IOUP_PIN             GPIO_Pin_3

#define AD9910_PF0_PORT             GPIOC
#define AD9910_PF0_PIN              GPIO_Pin_4

#define AD9910_PF1_PORT             GPIOC
#define AD9910_PF1_PIN              GPIO_Pin_10

#define AD9910_PF2_PORT             GPIOC
#define AD9910_PF2_PIN              GPIO_Pin_5

#define AD9910_RST_PORT             GPIOA
#define AD9910_RST_PIN              GPIO_Pin_6

#define AD9910_SCK_PORT             GPIOA
#define AD9910_SCK_PIN              GPIO_Pin_2

#define AD9910_DRC_PORT             GPIOA
#define AD9910_DRC_PIN              GPIO_Pin_4

#define AD9910_OSK_PORT             GPIOC
#define AD9910_OSK_PIN              GPIO_Pin_8

#define AD9910_CSB_PORT             GPIOB
#define AD9910_CSB_PIN              GPIO_Pin_10

/* GPIO控制宏定义 */
#define AD9910_PWR_HIGH()           GPIO_SetBits(AD9910_PWR_PORT, AD9910_PWR_PIN)
#define AD9910_PWR_LOW()            GPIO_ResetBits(AD9910_PWR_PORT, AD9910_PWR_PIN)

#define AD9910_SDIO_HIGH()          GPIO_SetBits(AD9910_SDIO_PORT, AD9910_SDIO_PIN)
#define AD9910_SDIO_LOW()           GPIO_ResetBits(AD9910_SDIO_PORT, AD9910_SDIO_PIN)

#define AD9910_DPH_HIGH()           GPIO_SetBits(AD9910_DPH_PORT, AD9910_DPH_PIN)
#define AD9910_DPH_LOW()            GPIO_ResetBits(AD9910_DPH_PORT, AD9910_DPH_PIN)

#define AD9910_DRO_READ()           GPIO_ReadInputDataBit(AD9910_DRO_PORT, AD9910_DRO_PIN)

#define AD9910_IOUP_HIGH()          GPIO_SetBits(AD9910_IOUP_PORT, AD9910_IOUP_PIN)
#define AD9910_IOUP_LOW()           GPIO_ResetBits(AD9910_IOUP_PORT, AD9910_IOUP_PIN)

#define AD9910_PF0_HIGH()           GPIO_SetBits(AD9910_PF0_PORT, AD9910_PF0_PIN)
#define AD9910_PF0_LOW()            GPIO_ResetBits(AD9910_PF0_PORT, AD9910_PF0_PIN)

#define AD9910_PF1_HIGH()           GPIO_SetBits(AD9910_PF1_PORT, AD9910_PF1_PIN)
#define AD9910_PF1_LOW()            GPIO_ResetBits(AD9910_PF1_PORT, AD9910_PF1_PIN)

#define AD9910_PF2_HIGH()           GPIO_SetBits(AD9910_PF2_PORT, AD9910_PF2_PIN)
#define AD9910_PF2_LOW()            GPIO_ResetBits(AD9910_PF2_PORT, AD9910_PF2_PIN)

#define AD9910_RST_HIGH()           GPIO_SetBits(AD9910_RST_PORT, AD9910_RST_PIN)
#define AD9910_RST_LOW()            GPIO_ResetBits(AD9910_RST_PORT, AD9910_RST_PIN)

#define AD9910_SCK_HIGH()           GPIO_SetBits(AD9910_SCK_PORT, AD9910_SCK_PIN)
#define AD9910_SCK_LOW()            GPIO_ResetBits(AD9910_SCK_PORT, AD9910_SCK_PIN)

#define AD9910_DRC_HIGH()           GPIO_SetBits(AD9910_DRC_PORT, AD9910_DRC_PIN)
#define AD9910_DRC_LOW()            GPIO_ResetBits(AD9910_DRC_PORT, AD9910_DRC_PIN)

#define AD9910_OSK_HIGH()           GPIO_SetBits(AD9910_OSK_PORT, AD9910_OSK_PIN)
#define AD9910_OSK_LOW()            GPIO_ResetBits(AD9910_OSK_PORT, AD9910_OSK_PIN)

#define AD9910_CSB_HIGH()           GPIO_SetBits(AD9910_CSB_PORT, AD9910_CSB_PIN)
#define AD9910_CSB_LOW()            GPIO_ResetBits(AD9910_CSB_PORT, AD9910_CSB_PIN)

/* Exported functions --------------------------------------------------------*/

/**
 * @brief  AD9910硬件初始化
 * @param  None
 * @retval None
 */
void AD9910_HAL_Init(void);

/**
 * @brief  AD9910发送8位数据
 * @param  data: 要发送的8位数据
 * @retval None
 */
void AD9910_HAL_Send8Bits(uchar data);

/**
 * @brief  AD9910硬件复位
 * @param  None
 * @retval None
 */
void AD9910_HAL_Reset(void);

/**
 * @brief  AD9910 I/O更新
 * @param  None
 * @retval None
 */
void AD9910_HAL_IOUpdate(void);

/**
 * @brief  AD9910电源控制
 * @param  enable: 1-使能电源，0-关闭电源
 * @retval None
 */
void AD9910_HAL_PowerControl(uint8_t enable);

/**
 * @brief  AD9910 Profile选择
 * @param  profile: Profile编号 (0-7)
 * @retval None
 */
void AD9910_HAL_SelectProfile(uint8_t profile);

/**
 * @brief  微秒级延时
 * @param  us: 延时微秒数
 * @retval None
 */
void AD9910_HAL_DelayUs(uint32_t us);

/**
 * @brief  毫秒级延时
 * @param  ms: 延时毫秒数
 * @retval None
 */
void AD9910_HAL_DelayMs(uint32_t ms);

#ifdef __cplusplus
}
#endif

#endif /* __AD9910_HAL_H */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
