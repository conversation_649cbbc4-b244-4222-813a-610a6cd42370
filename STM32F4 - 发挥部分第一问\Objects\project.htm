<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\project.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\project.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060528: Last Updated: Sun Aug 03 01:06:03 2025
<BR><P>
<H3>Maximum Stack Usage =        480 bytes + Unknown(Functions without stacksize, Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
ControlCallback &rArr; TJC_HMI_UpdateAllParameters &rArr; TJC_HMI_SetOutputFrequency &rArr; TJC_HMI_SetText &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
<P>
<H3>
Functions with no stack information
</H3><UL>
 <LI><a href="#[9c]">__user_initial_stackheap</a>
</UL>
</UL>
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1f]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1f]">ADC_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[1f]">ADC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[7]">BusFault_Handler</a> from stm32f4xx_it.o(i.BusFault_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[21]">CAN1_RX0_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[22]">CAN1_RX1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[23]">CAN1_SCE_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[20]">CAN1_TX_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4d]">CAN2_RX0_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4e]">CAN2_RX1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4f]">CAN2_SCE_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4c]">CAN2_TX_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5c]">CRYP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[63]">ControlCallback</a> from main.o(i.ControlCallback) referenced from main.o(i.main)
 <LI><a href="#[5b]">DCMI_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[18]">DMA1_Stream0_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[19]">DMA1_Stream1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1a]">DMA1_Stream2_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1b]">DMA1_Stream3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1c]">DMA1_Stream4_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1d]">DMA1_Stream5_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1e]">DMA1_Stream6_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3c]">DMA1_Stream7_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[45]">DMA2_Stream0_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[46]">DMA2_Stream1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[47]">DMA2_Stream2_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[48]">DMA2_Stream3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[49]">DMA2_Stream4_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[51]">DMA2_Stream5_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[52]">DMA2_Stream6_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[53]">DMA2_Stream7_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[a]">DebugMon_Handler</a> from stm32f4xx_it.o(i.DebugMon_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4a]">ETH_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4b]">ETH_WKUP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[13]">EXTI0_IRQHandler</a> from stm32f4xx_it.o(i.EXTI0_IRQHandler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[35]">EXTI15_10_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[14]">EXTI1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[15]">EXTI2_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[16]">EXTI3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[17]">EXTI4_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[24]">EXTI9_5_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[11]">FLASH_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5e]">FPU_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3d]">FSMC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5d]">HASH_RNG_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5]">HardFault_Handler</a> from stm32f4xx_it.o(i.HardFault_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2d]">I2C1_ER_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2c]">I2C1_EV_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2f]">I2C2_ER_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2e]">I2C2_EV_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[56]">I2C3_ER_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[55]">I2C3_EV_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[6]">MemManage_Handler</a> from stm32f4xx_it.o(i.MemManage_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4]">NMI_Handler</a> from stm32f4xx_it.o(i.NMI_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[50]">OTG_FS_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[37]">OTG_FS_WKUP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[58]">OTG_HS_EP1_IN_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[57]">OTG_HS_EP1_OUT_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5a]">OTG_HS_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[59]">OTG_HS_WKUP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[e]">PVD_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[b]">PendSV_Handler</a> from stm32f4xx_it.o(i.PendSV_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[12]">RCC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[36]">RTC_Alarm_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[10]">RTC_WKUP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3]">Reset_Handler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3e]">SDIO_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[30]">SPI1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[31]">SPI2_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[40]">SPI3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[9]">SVC_Handler</a> from stm32f4xx_it.o(i.SVC_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[c]">SysTick_Handler</a> from stm32f4xx_it.o(i.SysTick_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5f]">SystemInit</a> from system_stm32f4xx.o(i.SystemInit) referenced from startup_stm32f40_41xxx.o(.text)
 <LI><a href="#[f]">TAMP_STAMP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[25]">TIM1_BRK_TIM9_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[28]">TIM1_CC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[27]">TIM1_TRG_COM_TIM11_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[26]">TIM1_UP_TIM10_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[29]">TIM2_IRQHandler</a> from stm32f4xx_it.o(i.TIM2_IRQHandler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2a]">TIM3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2b]">TIM4_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3f]">TIM5_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[43]">TIM6_DAC_IRQHandler</a> from stm32f4xx_it.o(i.TIM6_DAC_IRQHandler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[44]">TIM7_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[38]">TIM8_BRK_TIM12_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3b]">TIM8_CC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3a]">TIM8_TRG_COM_TIM14_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[39]">TIM8_UP_TIM13_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[41]">UART4_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[42]">UART5_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[32]">USART1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[33]">USART2_IRQHandler</a> from stm32f4xx_it.o(i.USART2_IRQHandler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[34]">USART3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[54]">USART6_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[8]">UsageFault_Handler</a> from stm32f4xx_it.o(i.UsageFault_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[d]">WWDG_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[64]">__main</a> from __main.o(!!!main) referenced from startup_stm32f40_41xxx.o(.text)
 <LI><a href="#[62]">_printf_input_char</a> from _printf_char_common.o(.text) referenced from _printf_char_common.o(.text)
 <LI><a href="#[61]">_snputc</a> from _snputc.o(.text) referenced from __2snprintf.o(.text)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[64]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[65]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[67]"></a>__scatterload_rt2</STRONG> (Thumb, 44 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[13e]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[13f]"></a>__scatterload_null</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[68]"></a>__scatterload_copy</STRONG> (Thumb, 26 bytes, Stack size unknown bytes, __scatter_copy.o(!!handler_copy), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>

<P><STRONG><a name="[140]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[69]"></a>_printf_f</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_f.o(.ARM.Collect$$_printf_percent$$00000003))
<BR><BR>[Stack]<UL><LI>Max Depth = 324 + Unknown Stack Size
<LI>Call Chain = _printf_f &rArr; _printf_fp_dec &rArr; _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec
</UL>

<P><STRONG><a name="[87]"></a>_printf_percent</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[6b]"></a>_printf_d</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_d.o(.ARM.Collect$$_printf_percent$$00000009))
<BR><BR>[Stack]<UL><LI>Max Depth = 56 + Unknown Stack Size
<LI>Call Chain = _printf_d &rArr; _printf_int_dec &rArr; _printf_int_common
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[6d]"></a>_printf_u</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A))
<BR><BR>[Stack]<UL><LI>Max Depth = 56 + Unknown Stack Size
<LI>Call Chain = _printf_u &rArr; _printf_int_dec &rArr; _printf_int_common
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[6e]"></a>_printf_s</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_s.o(.ARM.Collect$$_printf_percent$$00000014))
<BR><BR>[Stack]<UL><LI>Max Depth = 24 + Unknown Stack Size
<LI>Call Chain = _printf_s &rArr; _printf_string &rArr; _printf_cs_common &rArr; _printf_str
</UL>
<BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_string
</UL>

<P><STRONG><a name="[141]"></a>_printf_percent_end</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017))

<P><STRONG><a name="[79]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[70]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000001))
<BR><BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_init
</UL>

<P><STRONG><a name="[142]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000A))

<P><STRONG><a name="[72]"></a>__rt_lib_init_lc_common</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000F))
<BR><BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_locale
</UL>

<P><STRONG><a name="[143]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[144]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[145]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[146]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000011))

<P><STRONG><a name="[147]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[148]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[74]"></a>__rt_lib_init_lc_numeric_2</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000016))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_lib_init_lc_numeric_2 &rArr; _get_lc_numeric
</UL>
<BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_numeric
</UL>

<P><STRONG><a name="[149]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[14a]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002C))

<P><STRONG><a name="[14b]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[14c]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[14d]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[14e]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[14f]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[150]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[151]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[152]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[153]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000033))

<P><STRONG><a name="[154]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[155]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[7e]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[156]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[157]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000007))

<P><STRONG><a name="[158]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F))

<P><STRONG><a name="[159]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000010))

<P><STRONG><a name="[15a]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A))

<P><STRONG><a name="[15b]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[15c]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[66]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
</UL>

<P><STRONG><a name="[15d]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[76]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[78]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[15e]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[7a]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 456 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; TJC_HMI_UpdateAllParameters &rArr; TJC_HMI_SetOutputFrequency &rArr; TJC_HMI_SetText &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[15f]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[9d]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[7d]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[160]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[7f]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[3]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>CAN2_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>CAN2_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>CAN2_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>CAN2_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>CRYP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>DCMI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>DMA1_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA1_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA1_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA1_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA1_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA1_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>DMA1_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>DMA1_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>DMA2_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA2_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>DMA2_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>DMA2_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>DMA2_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>DMA2_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>DMA2_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>DMA2_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>ETH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>ETH_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>FSMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>HASH_RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>OTG_FS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>OTG_FS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>OTG_HS_EP1_IN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>OTG_HS_EP1_OUT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>OTG_HS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>OTG_HS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TIM1_BRK_TIM9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIM1_TRG_COM_TIM11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIM1_UP_TIM10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIM8_BRK_TIM12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>TIM8_TRG_COM_TIM14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>TIM8_UP_TIM13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>USART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[9c]"></a>__user_initial_stackheap</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_stm32f40_41xxx.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[81]"></a>__2snprintf</STRONG> (Thumb, 50 bytes, Stack size 40 bytes, __2snprintf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 136 + Unknown Stack Size
<LI>Call Chain = __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_SetOutputFrequency
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_SetCircuitVoltage
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_SetText
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_ChangePage
</UL>

<P><STRONG><a name="[99]"></a>_printf_str</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, _printf_str.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _printf_str
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>

<P><STRONG><a name="[6c]"></a>_printf_int_dec</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, _printf_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _printf_int_dec &rArr; _printf_int_common
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_u
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_d
</UL>

<P><STRONG><a name="[85]"></a>__printf</STRONG> (Thumb, 352 bytes, Stack size 32 bytes, __printf_ss_wp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32 + Unknown Stack Size
<LI>Call Chain = __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_percent
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_is_digit
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>

<P><STRONG><a name="[120]"></a>strstr</STRONG> (Thumb, 36 bytes, Stack size 12 bytes, strstr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strstr
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_ParseUnitString
</UL>

<P><STRONG><a name="[b7]"></a>strcpy</STRONG> (Thumb, 72 bytes, Stack size 12 bytes, strcpy.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_Init
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_Init
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_ParseUnitString
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_HandleUnitInput
</UL>

<P><STRONG><a name="[11f]"></a>strlen</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, strlen.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_ParseUnitString
</UL>

<P><STRONG><a name="[11d]"></a>__aeabi_memcpy</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memcpy_v6.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_ParseTextInput
</UL>

<P><STRONG><a name="[88]"></a>__rt_memcpy</STRONG> (Thumb, 138 bytes, Stack size 0 bytes, rt_memcpy_v6.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>

<P><STRONG><a name="[161]"></a>_memcpy_lastbytes</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_v6.o(.text), UNUSED)

<P><STRONG><a name="[89]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_UpdateAllParameters
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_AddEvent
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_GetParams
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memcpy
</UL>

<P><STRONG><a name="[162]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[163]"></a>__rt_memcpy_w</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[164]"></a>_memcpy_lastbytes_aligned</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[da]"></a>__aeabi_memclr</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memclr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CommandParser_Init
</UL>

<P><STRONG><a name="[8a]"></a>__rt_memclr</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset_w
</UL>

<P><STRONG><a name="[165]"></a>_memset</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr.o(.text), UNUSED)

<P><STRONG><a name="[b6]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_Init
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_Init
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_ParseTouchEvent
</UL>

<P><STRONG><a name="[166]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[167]"></a>__rt_memclr_w</STRONG> (Thumb, 78 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[8b]"></a>_memset_w</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr_w.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memclr
</UL>

<P><STRONG><a name="[107]"></a>strcmp</STRONG> (Thumb, 128 bytes, Stack size 0 bytes, strcmpv7m.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_ConvertToHz
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_numeric
</UL>

<P><STRONG><a name="[168]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[169]"></a>__rt_heap_escrow$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[16a]"></a>__rt_heap_expand$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[8d]"></a>__aeabi_errno_addr</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__read_errno
</UL>

<P><STRONG><a name="[16b]"></a>__errno$intlibspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text), UNUSED)

<P><STRONG><a name="[16c]"></a>__rt_errno_addr$intlibspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text), UNUSED)

<P><STRONG><a name="[8c]"></a>__read_errno</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, _rserrno.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>

<P><STRONG><a name="[8e]"></a>__set_errno</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, _rserrno.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __set_errno
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>

<P><STRONG><a name="[84]"></a>_printf_int_common</STRONG> (Thumb, 178 bytes, Stack size 32 bytes, _printf_intcommon.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[16d]"></a>__lib_sel_fp_printf</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, _printf_fp_dec.o(.text), UNUSED)

<P><STRONG><a name="[95]"></a>_printf_fp_dec_real</STRONG> (Thumb, 620 bytes, Stack size 104 bytes, _printf_fp_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 324<LI>Call Chain = _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_infnan
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_locale
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec
</UL>

<P><STRONG><a name="[82]"></a>_printf_char_common</STRONG> (Thumb, 32 bytes, Stack size 64 bytes, _printf_char_common.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 96 + Unknown Stack Size
<LI>Call Chain = _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>

<P><STRONG><a name="[83]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _sputc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>

<P><STRONG><a name="[61]"></a>_snputc</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, _snputc.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> __2snprintf.o(.text)
</UL>
<P><STRONG><a name="[98]"></a>_printf_cs_common</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _printf_cs_common &rArr; _printf_str
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_str
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_string
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char
</UL>

<P><STRONG><a name="[9a]"></a>_printf_char</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, _printf_char.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>

<P><STRONG><a name="[6f]"></a>_printf_string</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _printf_string &rArr; _printf_cs_common &rArr; _printf_str
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>
<BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_s
</UL>

<P><STRONG><a name="[16e]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[9b]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[16f]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[73]"></a>__rt_locale</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, rt_locale_intlibspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_lc_common
</UL>

<P><STRONG><a name="[94]"></a>_ll_udiv10</STRONG> (Thumb, 138 bytes, Stack size 12 bytes, lludiv10.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = _ll_udiv10
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[97]"></a>_printf_fp_infnan</STRONG> (Thumb, 112 bytes, Stack size 24 bytes, _printf_fp_infnan.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _printf_fp_infnan
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[90]"></a>_btod_etento</STRONG> (Thumb, 224 bytes, Stack size 72 bytes, bigflt0.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[77]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[7c]"></a>exit</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[91]"></a>_btod_d2e</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, btod.o(CL$$btod_d2e))
<BR><BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2e_norm_op1
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[9f]"></a>_d2e_denorm_low</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, btod.o(CL$$btod_d2e_denorm_low))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2e_norm_op1
</UL>

<P><STRONG><a name="[9e]"></a>_d2e_norm_op1</STRONG> (Thumb, 96 bytes, Stack size 0 bytes, btod.o(CL$$btod_d2e_norm_op1))
<BR><BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2e_denorm_low
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_d2e
</UL>

<P><STRONG><a name="[a0]"></a>__btod_div_common</STRONG> (Thumb, 696 bytes, Stack size 24 bytes, btod.o(CL$$btod_div_common))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __btod_div_common
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
</UL>

<P><STRONG><a name="[a1]"></a>_e2e</STRONG> (Thumb, 220 bytes, Stack size 24 bytes, btod.o(CL$$btod_e2e))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _e2e
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
</UL>

<P><STRONG><a name="[92]"></a>_btod_ediv</STRONG> (Thumb, 42 bytes, Stack size 28 bytes, btod.o(CL$$btod_ediv))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = _btod_ediv &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_e2e
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__btod_div_common
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[93]"></a>_btod_emul</STRONG> (Thumb, 42 bytes, Stack size 28 bytes, btod.o(CL$$btod_emul))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__btod_mult_common
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_e2e
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[a2]"></a>__btod_mult_common</STRONG> (Thumb, 580 bytes, Stack size 16 bytes, btod.o(CL$$btod_mult_common))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __btod_mult_common
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
</UL>

<P><STRONG><a name="[ae]"></a>AD9910_Control_CalculateActualOutput</STRONG> (Thumb, 130 bytes, Stack size 32 bytes, ad9910_control.o(i.AD9910_Control_CalculateActualOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = AD9910_Control_CalculateActualOutput &rArr; __aeabi_cdcmple
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_SetTargetAmplitude
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_SetGainFactor
</UL>

<P><STRONG><a name="[af]"></a>AD9910_Control_CalculateTargetOutput</STRONG> (Thumb, 70 bytes, Stack size 32 bytes, ad9910_control.o(i.AD9910_Control_CalculateTargetOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = AD9910_Control_CalculateTargetOutput &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_SetGainFactor
</UL>

<P><STRONG><a name="[b0]"></a>AD9910_Control_EnableOutput</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, ad9910_control.o(i.AD9910_Control_EnableOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = AD9910_Control_EnableOutput &rArr; AD9910_EnableOutput &rArr; GPIO_ResetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateSystemTime
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_EnableOutput
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_DisableOutput
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b4]"></a>AD9910_Control_GetParams</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ad9910_control.o(i.AD9910_Control_GetParams))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = AD9910_Control_GetParams &rArr; __aeabi_memcpy4
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_UpdateAllParameters
</UL>

<P><STRONG><a name="[b5]"></a>AD9910_Control_Init</STRONG> (Thumb, 156 bytes, Stack size 8 bytes, ad9910_control.o(i.AD9910_Control_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = AD9910_Control_Init &rArr; AD9910_Set5MHz_HighQuality &rArr; AD9910_SetFrequency_Precision &rArr; AD9910_CalculateFrequencyWord_Precision &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Set5MHz_HighQuality
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Init
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[12e]"></a>AD9910_Control_RegisterCallback</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, ad9910_control.o(i.AD9910_Control_RegisterCallback))
<BR><BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ba]"></a>AD9910_Control_SetFrequency</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, ad9910_control.o(i.AD9910_Control_SetFrequency))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = AD9910_Control_SetFrequency &rArr; AD9910_SetFrequency_Precision &rArr; AD9910_CalculateFrequencyWord_Precision &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ValidateFrequency
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateSystemTime
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_SetFrequency_Precision
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_HandleNumberInput
</UL>

<P><STRONG><a name="[bd]"></a>AD9910_Control_SetGainFactor</STRONG> (Thumb, 120 bytes, Stack size 24 bytes, ad9910_control.o(i.AD9910_Control_SetGainFactor))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = AD9910_Control_SetGainFactor &rArr; AD9910_SetAmplitude_Precision &rArr; AD9910_CalculateAmplitudeWord_Precision &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_CalculateTargetOutput
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_CalculateActualOutput
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ValidateGainFactor
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateSystemTime
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_SetAmplitude_Precision
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c0]"></a>AD9910_Control_SetTargetAmplitude</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, ad9910_control.o(i.AD9910_Control_SetTargetAmplitude))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = AD9910_Control_SetTargetAmplitude &rArr; AD9910_SetAmplitude_Precision &rArr; AD9910_CalculateAmplitudeWord_Precision &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_CalculateActualOutput
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ValidateAmplitude
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateSystemTime
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_SetAmplitude_Precision
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[12f]"></a>AD9910_Control_Task</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, ad9910_control.o(i.AD9910_Control_Task))
<BR><BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b2]"></a>AD9910_DisableOutput</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, ad9910_waveform.o(i.AD9910_DisableOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = AD9910_DisableOutput &rArr; GPIO_SetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_EnableOutput
</UL>

<P><STRONG><a name="[c3]"></a>AD9910_EnableInverseSincFilter</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, ad9910_waveform.o(i.AD9910_EnableInverseSincFilter))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = AD9910_EnableInverseSincFilter &rArr; AD9910_WriteRegister &rArr; AD9910_HAL_Send8Bits &rArr; GPIO_SetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_WriteRegister
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_IOUpdate
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_OptimizeSmoothness
</UL>

<P><STRONG><a name="[b1]"></a>AD9910_EnableOutput</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, ad9910_waveform.o(i.AD9910_EnableOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = AD9910_EnableOutput &rArr; GPIO_ResetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_EnableOutput
</UL>

<P><STRONG><a name="[c7]"></a>AD9910_EnsurePhaseContinuity</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, ad9910_waveform.o(i.AD9910_EnsurePhaseContinuity))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = AD9910_EnsurePhaseContinuity &rArr; AD9910_WriteRegister &rArr; AD9910_HAL_Send8Bits &rArr; GPIO_SetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_WriteRegister
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_IOUpdate
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_OptimizeSmoothness
</UL>

<P><STRONG><a name="[c8]"></a>AD9910_HAL_DelayMs</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, ad9910_hal.o(i.AD9910_HAL_DelayMs))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = AD9910_HAL_DelayMs &rArr; Delay_ms
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Set5MHz_HighQuality
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_OptimizeSmoothness
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Init
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_Reset
</UL>

<P><STRONG><a name="[ca]"></a>AD9910_HAL_DelayUs</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ad9910_hal.o(i.AD9910_HAL_DelayUs))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = AD9910_HAL_DelayUs
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_WriteRegister
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Init
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_IOUpdate
</UL>

<P><STRONG><a name="[c5]"></a>AD9910_HAL_IOUpdate</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, ad9910_hal.o(i.AD9910_HAL_IOUpdate))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = AD9910_HAL_IOUpdate &rArr; GPIO_SetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_DelayUs
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Init
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_EnsurePhaseContinuity
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_EnableInverseSincFilter
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_WriteProfile
</UL>

<P><STRONG><a name="[cb]"></a>AD9910_HAL_Init</STRONG> (Thumb, 204 bytes, Stack size 16 bytes, ad9910_hal.o(i.AD9910_HAL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = AD9910_HAL_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Init
</UL>

<P><STRONG><a name="[ce]"></a>AD9910_HAL_PowerControl</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, ad9910_hal.o(i.AD9910_HAL_PowerControl))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = AD9910_HAL_PowerControl &rArr; GPIO_SetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Init
</UL>

<P><STRONG><a name="[cf]"></a>AD9910_HAL_Reset</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, ad9910_hal.o(i.AD9910_HAL_Reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = AD9910_HAL_Reset &rArr; GPIO_SetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_DelayMs
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Init
</UL>

<P><STRONG><a name="[d0]"></a>AD9910_HAL_SelectProfile</STRONG> (Thumb, 82 bytes, Stack size 8 bytes, ad9910_hal.o(i.AD9910_HAL_SelectProfile))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = AD9910_HAL_SelectProfile &rArr; GPIO_SetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Init
</UL>

<P><STRONG><a name="[d1]"></a>AD9910_HAL_Send8Bits</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, ad9910_hal.o(i.AD9910_HAL_Send8Bits))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = AD9910_HAL_Send8Bits &rArr; GPIO_SetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_WriteRegister
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Init
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_WriteProfile
</UL>

<P><STRONG><a name="[b8]"></a>AD9910_Init</STRONG> (Thumb, 210 bytes, Stack size 8 bytes, ad9910_waveform.o(i.AD9910_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = AD9910_Init &rArr; AD9910_HAL_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_Send8Bits
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_SelectProfile
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_Reset
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_PowerControl
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_Init
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_IOUpdate
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_DelayUs
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_DelayMs
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_Init
</UL>

<P><STRONG><a name="[d3]"></a>AD9910_OptimizeOutputFilter</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, ad9910_waveform.o(i.AD9910_OptimizeOutputFilter))
<BR><BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_OptimizeSmoothness
</UL>

<P><STRONG><a name="[d2]"></a>AD9910_OptimizeSmoothness</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, ad9910_waveform.o(i.AD9910_OptimizeSmoothness))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = AD9910_OptimizeSmoothness &rArr; AD9910_EnsurePhaseContinuity &rArr; AD9910_WriteRegister &rArr; AD9910_HAL_Send8Bits &rArr; GPIO_SetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_OptimizeOutputFilter
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_EnsurePhaseContinuity
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_EnableInverseSincFilter
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_DelayMs
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Set5MHz_HighQuality
</UL>

<P><STRONG><a name="[d4]"></a>AD9910_OptimizeWaveform</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, ad9910_waveform.o(i.AD9910_OptimizeWaveform))
<BR><BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Set5MHz_HighQuality
</UL>

<P><STRONG><a name="[b9]"></a>AD9910_Set5MHz_HighQuality</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, ad9910_waveform.o(i.AD9910_Set5MHz_HighQuality))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = AD9910_Set5MHz_HighQuality &rArr; AD9910_SetFrequency_Precision &rArr; AD9910_CalculateFrequencyWord_Precision &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_SetFrequency_Precision
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_SetAmplitude_Precision
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_OptimizeWaveform
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_OptimizeSmoothness
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_DelayMs
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_Init
</UL>

<P><STRONG><a name="[bf]"></a>AD9910_SetAmplitude_Precision</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, ad9910_waveform.o(i.AD9910_SetAmplitude_Precision))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = AD9910_SetAmplitude_Precision &rArr; AD9910_CalculateAmplitudeWord_Precision &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_WriteProfile
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_CalculateAmplitudeWord_Precision
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_SetTargetAmplitude
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_SetGainFactor
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Set5MHz_HighQuality
</UL>

<P><STRONG><a name="[bc]"></a>AD9910_SetFrequency_Precision</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, ad9910_waveform.o(i.AD9910_SetFrequency_Precision))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = AD9910_SetFrequency_Precision &rArr; AD9910_CalculateFrequencyWord_Precision &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_WriteProfile
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_CalculateFrequencyWord_Precision
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_SetFrequency
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Set5MHz_HighQuality
</UL>

<P><STRONG><a name="[c4]"></a>AD9910_WriteRegister</STRONG> (Thumb, 72 bytes, Stack size 24 bytes, ad9910_waveform.o(i.AD9910_WriteRegister))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = AD9910_WriteRegister &rArr; AD9910_HAL_Send8Bits &rArr; GPIO_SetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_Send8Bits
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_DelayUs
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_EnsurePhaseContinuity
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_EnableInverseSincFilter
</UL>

<P><STRONG><a name="[d6]"></a>BSP_Init</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, bsp.o(i.BSP_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = BSP_Init &rArr; RCC_APB2PeriphClockCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.BusFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[d9]"></a>CommandParser_Init</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, command_parser.o(i.CommandParser_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CommandParser_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[63]"></a>ControlCallback</STRONG> (Thumb, 96 bytes, Stack size 24 bytes, main.o(i.ControlCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 480 + Unknown Stack Size
<LI>Call Chain = ControlCallback &rArr; TJC_HMI_UpdateAllParameters &rArr; TJC_HMI_SetOutputFrequency &rArr; TJC_HMI_SetText &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_UpdateAllParameters
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_SetOutputFrequency
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_SetCircuitVoltage
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(i.main)
</UL>
<P><STRONG><a name="[fd]"></a>DWT_Init</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, systick.o(i.DWT_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DWT_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Init
</UL>

<P><STRONG><a name="[a]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[c9]"></a>Delay_ms</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, systick.o(i.Delay_ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Delay_ms
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_UpdateStats
</UL>
<BR>[Called By]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_ShowWelcome
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_Init
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_DelayMs
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_HandleMeasureButton
</UL>

<P><STRONG><a name="[13]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, stm32f4xx_it.o(i.EXTI0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI0_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI0_IRQHandler_Internal
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[df]"></a>EXTI0_IRQHandler_Internal</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, main.o(i.EXTI0_IRQHandler_Internal))
<BR><BR>[Called By]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI0_IRQHandler
</UL>

<P><STRONG><a name="[e0]"></a>FLASH_SetLatency</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, stm32f4xx_flash.o(i.FLASH_SetLatency))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = FLASH_SetLatency
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[cd]"></a>GPIO_Init</STRONG> (Thumb, 352 bytes, Stack size 24 bytes, stm32f4xx_gpio.o(i.GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_UART_Init
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_Init
</UL>

<P><STRONG><a name="[e2]"></a>GPIO_PinAFConfig</STRONG> (Thumb, 352 bytes, Stack size 24 bytes, stm32f4xx_gpio.o(i.GPIO_PinAFConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = GPIO_PinAFConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_UART_Init
</UL>

<P><STRONG><a name="[c6]"></a>GPIO_ResetBits</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, stm32f4xx_gpio.o(i.GPIO_ResetBits))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = GPIO_ResetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_WriteRegister
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Init
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_EnableOutput
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_WriteProfile
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_Send8Bits
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_SelectProfile
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_Reset
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_PowerControl
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_Init
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_IOUpdate
</UL>

<P><STRONG><a name="[c2]"></a>GPIO_SetBits</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, stm32f4xx_gpio.o(i.GPIO_SetBits))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = GPIO_SetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_WriteRegister
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Init
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_DisableOutput
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_WriteProfile
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_Send8Bits
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_SelectProfile
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_Reset
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_PowerControl
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_Init
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_IOUpdate
</UL>

<P><STRONG><a name="[123]"></a>GPIO_StructInit</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_gpio.o(i.GPIO_StructInit))
<BR><BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_UART_Init
</UL>

<P><STRONG><a name="[e3]"></a>GainCalculator_Calculate</STRONG> (Thumb, 384 bytes, Stack size 40 bytes, gain_calculator.o(i.GainCalculator_Calculate))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = GainCalculator_Calculate &rArr; GainCalculator_TransferFunctionGain &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_TransferFunctionGain
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_TemperatureCorrection
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_NonlinearCorrection
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_FrequencyCorrection
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ValidateGainConfig
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CalculateTotalGain
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_UpdateAllParameters
</UL>

<P><STRONG><a name="[e5]"></a>GainCalculator_FrequencyCorrection</STRONG> (Thumb, 144 bytes, Stack size 32 bytes, gain_calculator.o(i.GainCalculator_FrequencyCorrection))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = GainCalculator_FrequencyCorrection &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_Calculate
</UL>

<P><STRONG><a name="[12d]"></a>GainCalculator_Init</STRONG> (Thumb, 66 bytes, Stack size 0 bytes, gain_calculator.o(i.GainCalculator_Init))
<BR><BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e8]"></a>GainCalculator_NonlinearCorrection</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, gain_calculator.o(i.GainCalculator_NonlinearCorrection))
<BR><BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_Calculate
</UL>

<P><STRONG><a name="[e6]"></a>GainCalculator_TemperatureCorrection</STRONG> (Thumb, 144 bytes, Stack size 32 bytes, gain_calculator.o(i.GainCalculator_TemperatureCorrection))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = GainCalculator_TemperatureCorrection &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_Calculate
</UL>

<P><STRONG><a name="[e7]"></a>GainCalculator_TransferFunctionGain</STRONG> (Thumb, 204 bytes, Stack size 80 bytes, gain_calculator.o(i.GainCalculator_TransferFunctionGain))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = GainCalculator_TransferFunctionGain &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_Calculate
</UL>

<P><STRONG><a name="[ec]"></a>GainCalculator_ValidateGain</STRONG> (Thumb, 124 bytes, Stack size 24 bytes, gain_calculator.o(i.GainCalculator_ValidateGain))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = GainCalculator_ValidateGain &rArr; __aeabi_cdcmple
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ValidateGainConfig
</UL>

<P><STRONG><a name="[5]"></a>HardFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.HardFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[6]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.MemManage_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[ed]"></a>NVIC_Init</STRONG> (Thumb, 164 bytes, Stack size 24 bytes, misc.o(i.NVIC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = NVIC_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_UART_Init
</UL>

<P><STRONG><a name="[b]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[cc]"></a>RCC_AHB1PeriphClockCmd</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = RCC_AHB1PeriphClockCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_Init
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_UART_Init
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_Init
</UL>

<P><STRONG><a name="[ee]"></a>RCC_APB1PeriphResetCmd</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = RCC_APB1PeriphResetCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DeInit
</UL>

<P><STRONG><a name="[d7]"></a>RCC_APB2PeriphClockCmd</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = RCC_APB2PeriphClockCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_Init
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_UART_Init
</UL>

<P><STRONG><a name="[ef]"></a>RCC_APB2PeriphResetCmd</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = RCC_APB2PeriphResetCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DeInit
</UL>

<P><STRONG><a name="[100]"></a>RCC_DeInit</STRONG> (Thumb, 82 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(i.RCC_DeInit))
<BR><BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[12a]"></a>RCC_GetClocksFreq</STRONG> (Thumb, 214 bytes, Stack size 20 bytes, stm32f4xx_rcc.o(i.RCC_GetClocksFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
</UL>

<P><STRONG><a name="[f0]"></a>RCC_GetFlagStatus</STRONG> (Thumb, 134 bytes, Stack size 24 bytes, stm32f4xx_rcc.o(i.RCC_GetFlagStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = RCC_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_WaitForHSEStartUp
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[101]"></a>RCC_GetSYSCLKSource</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(i.RCC_GetSYSCLKSource))
<BR><BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[f1]"></a>RCC_HCLKConfig</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(i.RCC_HCLKConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = RCC_HCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[f2]"></a>RCC_HSEConfig</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(i.RCC_HSEConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = RCC_HSEConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[f3]"></a>RCC_PCLK1Config</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(i.RCC_PCLK1Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = RCC_PCLK1Config
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[f4]"></a>RCC_PCLK2Config</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(i.RCC_PCLK2Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = RCC_PCLK2Config
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[f5]"></a>RCC_PLLCmd</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(i.RCC_PLLCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = RCC_PLLCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[f6]"></a>RCC_PLLConfig</STRONG> (Thumb, 154 bytes, Stack size 24 bytes, stm32f4xx_rcc.o(i.RCC_PLLConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = RCC_PLLConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[f7]"></a>RCC_SYSCLKConfig</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(i.RCC_SYSCLKConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = RCC_SYSCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[f8]"></a>RCC_WaitForHSEStartUp</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = RCC_WaitForHSEStartUp &rArr; RCC_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[9]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[12b]"></a>SysTick_GetTick</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, systick.o(i.SysTick_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateSystemTime
</UL>

<P><STRONG><a name="[c]"></a>SysTick_Handler</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, stm32f4xx_it.o(i.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SysTick_Handler
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler_Internal
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimingDelay_Decrement
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[fa]"></a>SysTick_Handler_Internal</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, systick.o(i.SysTick_Handler_Internal))
<BR><BR>[Called By]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[fb]"></a>SysTick_Init</STRONG> (Thumb, 114 bytes, Stack size 8 bytes, systick.o(i.SysTick_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SysTick_Init &rArr; DWT_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_ResetStats
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DWT_Init
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[fe]"></a>SysTick_ResetStats</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, systick.o(i.SysTick_ResetStats))
<BR><BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Init
</UL>

<P><STRONG><a name="[ff]"></a>SystemClock_Config</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, main.o(i.SystemClock_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SystemClock_Config &rArr; RCC_WaitForHSEStartUp &rArr; RCC_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_WaitForHSEStartUp
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_SYSCLKConfig
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PLLConfig
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PLLCmd
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PCLK2Config
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PCLK1Config
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_HSEConfig
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_HCLKConfig
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetSYSCLKSource
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetFlagStatus
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_DeInit
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_SetLatency
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[5f]"></a>SystemInit</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, system_stm32f4xx.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = SystemInit &rArr; SetSysClock
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSysClock
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(.text)
</UL>
<P><STRONG><a name="[29]"></a>TIM2_IRQHandler</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, stm32f4xx_it.o(i.TIM2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = TIM2_IRQHandler &rArr; TIM_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_GetITStatus
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearITPendingBit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.TIM6_DAC_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[104]"></a>TIM_ClearITPendingBit</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, stm32f4xx_tim.o(i.TIM_ClearITPendingBit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_ClearITPendingBit
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM2_IRQHandler
</UL>

<P><STRONG><a name="[103]"></a>TIM_GetITStatus</STRONG> (Thumb, 186 bytes, Stack size 24 bytes, stm32f4xx_tim.o(i.TIM_GetITStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIM_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM2_IRQHandler
</UL>

<P><STRONG><a name="[108]"></a>TJC_HMI_ChangePage</STRONG> (Thumb, 28 bytes, Stack size 40 bytes, tjc_hmi.o(i.TJC_HMI_ChangePage))
<BR><BR>[Stack]<UL><LI>Max Depth = 176 + Unknown Stack Size
<LI>Call Chain = TJC_HMI_ChangePage &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_ShowWelcome
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_HandleMeasureButton
</UL>

<P><STRONG><a name="[10a]"></a>TJC_HMI_HandleMeasureButton</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, tjc_hmi.o(i.TJC_HMI_HandleMeasureButton))
<BR><BR>[Stack]<UL><LI>Max Depth = 296 + Unknown Stack Size
<LI>Call Chain = TJC_HMI_HandleMeasureButton &rArr; TJC_HMI_SetFilterType &rArr; TJC_HMI_SetText &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_SetFilterType
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_SendCommand
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_ChangePage
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_ParseTouchEvent
</UL>

<P><STRONG><a name="[10c]"></a>TJC_HMI_HandleNumberInput</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, tjc_hmi.o(i.TJC_HMI_HandleNumberInput))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = TJC_HMI_HandleNumberInput &rArr; AD9910_Control_SetFrequency &rArr; AD9910_SetFrequency_Precision &rArr; AD9910_CalculateFrequencyWord_Precision &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_SetFrequency
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_ConvertToHz
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_ParseNumberInput
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_HandleUnitInput
</UL>

<P><STRONG><a name="[10d]"></a>TJC_HMI_HandleUnitInput</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, tjc_hmi.o(i.TJC_HMI_HandleUnitInput))
<BR><BR>[Stack]<UL><LI>Max Depth = 304 + Unknown Stack Size
<LI>Call Chain = TJC_HMI_HandleUnitInput &rArr; TJC_HMI_SetText &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_ParseUnitString
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_SetText
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_HandleNumberInput
</UL>
<BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_ParseTextInput
</UL>

<P><STRONG><a name="[110]"></a>TJC_HMI_Init</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, tjc_hmi.o(i.TJC_HMI_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = TJC_HMI_Init &rArr; TJC_UART_Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_UART_Init
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[112]"></a>TJC_HMI_Process</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, tjc_hmi.o(i.TJC_HMI_Process))
<BR><BR>[Stack]<UL><LI>Max Depth = 368 + Unknown Stack Size
<LI>Call Chain = TJC_HMI_Process &rArr; TJC_ProcessReceivedData &rArr; TJC_ParseTouchEvent &rArr; TJC_HMI_HandleMeasureButton &rArr; TJC_HMI_SetFilterType &rArr; TJC_HMI_SetText &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_ProcessReceivedData
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[109]"></a>TJC_HMI_SendCommand</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, tjc_hmi.o(i.TJC_HMI_SendCommand))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = TJC_HMI_SendCommand &rArr; TJC_SendString &rArr; TJC_SendByte &rArr; USART_SendData
</UL>
<BR>[Calls]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SendString
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SendByte
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_Init
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_SetText
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_HandleMeasureButton
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_ChangePage
</UL>

<P><STRONG><a name="[dc]"></a>TJC_HMI_SetCircuitVoltage</STRONG> (Thumb, 90 bytes, Stack size 64 bytes, tjc_hmi.o(i.TJC_HMI_SetCircuitVoltage))
<BR><BR>[Stack]<UL><LI>Max Depth = 344 + Unknown Stack Size
<LI>Call Chain = TJC_HMI_SetCircuitVoltage &rArr; TJC_HMI_SetText &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_SetText
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_UpdateAllParameters
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_ShowWelcome
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ControlCallback
</UL>

<P><STRONG><a name="[10b]"></a>TJC_HMI_SetFilterType</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, tjc_hmi.o(i.TJC_HMI_SetFilterType))
<BR><BR>[Stack]<UL><LI>Max Depth = 288 + Unknown Stack Size
<LI>Call Chain = TJC_HMI_SetFilterType &rArr; TJC_HMI_SetText &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_SetText
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_StartMeasurement
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_HandleMeasureButton
</UL>

<P><STRONG><a name="[db]"></a>TJC_HMI_SetOutputFrequency</STRONG> (Thumb, 138 bytes, Stack size 64 bytes, tjc_hmi.o(i.TJC_HMI_SetOutputFrequency))
<BR><BR>[Stack]<UL><LI>Max Depth = 344 + Unknown Stack Size
<LI>Call Chain = TJC_HMI_SetOutputFrequency &rArr; TJC_HMI_SetText &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_SetText
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_UpdateAllParameters
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_ShowWelcome
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ControlCallback
</UL>

<P><STRONG><a name="[10f]"></a>TJC_HMI_SetText</STRONG> (Thumb, 32 bytes, Stack size 144 bytes, tjc_hmi.o(i.TJC_HMI_SetText))
<BR><BR>[Stack]<UL><LI>Max Depth = 280 + Unknown Stack Size
<LI>Call Chain = TJC_HMI_SetText &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_SendCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_ShowWelcome
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_SetOutputFrequency
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_SetCircuitVoltage
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_SetFilterType
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_HandleUnitInput
</UL>

<P><STRONG><a name="[116]"></a>TJC_HMI_ShowWelcome</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, tjc_hmi.o(i.TJC_HMI_ShowWelcome))
<BR><BR>[Stack]<UL><LI>Max Depth = 352 + Unknown Stack Size
<LI>Call Chain = TJC_HMI_ShowWelcome &rArr; TJC_HMI_SetOutputFrequency &rArr; TJC_HMI_SetText &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_SetOutputFrequency
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_SetCircuitVoltage
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_SetText
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_ChangePage
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[117]"></a>TJC_HMI_StartMeasurement</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, tjc_hmi.o(i.TJC_HMI_StartMeasurement))
<BR><BR>[Stack]<UL><LI>Max Depth = 296 + Unknown Stack Size
<LI>Call Chain = TJC_HMI_StartMeasurement &rArr; TJC_HMI_SetFilterType &rArr; TJC_HMI_SetText &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_SetFilterType
</UL>
<BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_ProcessReceivedData
</UL>

<P><STRONG><a name="[118]"></a>TJC_HMI_UART_IRQHandler</STRONG> (Thumb, 110 bytes, Stack size 8 bytes, tjc_hmi.o(i.TJC_HMI_UART_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = TJC_HMI_UART_IRQHandler &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiveData
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[dd]"></a>TJC_HMI_UpdateAllParameters</STRONG> (Thumb, 120 bytes, Stack size 112 bytes, tjc_hmi.o(i.TJC_HMI_UpdateAllParameters))
<BR><BR>[Stack]<UL><LI>Max Depth = 456 + Unknown Stack Size
<LI>Call Chain = TJC_HMI_UpdateAllParameters &rArr; TJC_HMI_SetOutputFrequency &rArr; TJC_HMI_SetText &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_SetOutputFrequency
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_SetCircuitVoltage
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_Calculate
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_GetParams
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ControlCallback
</UL>

<P><STRONG><a name="[f9]"></a>TimingDelay_Decrement</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, main.o(i.TimingDelay_Decrement))
<BR><BR>[Called By]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[33]"></a>USART2_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, stm32f4xx_it.o(i.USART2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = USART2_IRQHandler &rArr; TJC_HMI_UART_IRQHandler &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_UART_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[127]"></a>USART_ClearFlag</STRONG> (Thumb, 152 bytes, Stack size 16 bytes, stm32f4xx_usart.o(i.USART_ClearFlag))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USART_ClearFlag
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_UART_Init
</UL>

<P><STRONG><a name="[128]"></a>USART_Cmd</STRONG> (Thumb, 120 bytes, Stack size 16 bytes, stm32f4xx_usart.o(i.USART_Cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USART_Cmd
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_UART_Init
</UL>

<P><STRONG><a name="[124]"></a>USART_DeInit</STRONG> (Thumb, 276 bytes, Stack size 8 bytes, stm32f4xx_usart.o(i.USART_DeInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USART_DeInit &rArr; RCC_APB1PeriphResetCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphResetCmd
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphResetCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_UART_Init
</UL>

<P><STRONG><a name="[122]"></a>USART_GetFlagStatus</STRONG> (Thumb, 194 bytes, Stack size 16 bytes, stm32f4xx_usart.o(i.USART_GetFlagStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SendByte
</UL>

<P><STRONG><a name="[119]"></a>USART_GetITStatus</STRONG> (Thumb, 302 bytes, Stack size 32 bytes, stm32f4xx_usart.o(i.USART_GetITStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_UART_IRQHandler
</UL>

<P><STRONG><a name="[129]"></a>USART_ITConfig</STRONG> (Thumb, 284 bytes, Stack size 32 bytes, stm32f4xx_usart.o(i.USART_ITConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = USART_ITConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_UART_Init
</UL>

<P><STRONG><a name="[126]"></a>USART_Init</STRONG> (Thumb, 482 bytes, Stack size 48 bytes, stm32f4xx_usart.o(i.USART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_UART_Init
</UL>

<P><STRONG><a name="[11a]"></a>USART_ReceiveData</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, stm32f4xx_usart.o(i.USART_ReceiveData))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USART_ReceiveData
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_UART_IRQHandler
</UL>

<P><STRONG><a name="[121]"></a>USART_SendData</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, stm32f4xx_usart.o(i.USART_SendData))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USART_SendData
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SendByte
</UL>

<P><STRONG><a name="[125]"></a>USART_StructInit</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_usart.o(i.USART_StructInit))
<BR><BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_UART_Init
</UL>

<P><STRONG><a name="[8]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.UsageFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[96]"></a>__ARM_fpclassify</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, fpclassify.o(i.__ARM_fpclassify))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __ARM_fpclassify
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[ea]"></a>__hardfp_fabs</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, fabs.o(i.__hardfp_fabs))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_Calculate
</UL>

<P><STRONG><a name="[eb]"></a>__hardfp_sqrt</STRONG> (Thumb, 122 bytes, Stack size 32 bytes, sqrt.o(i.__hardfp_sqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_TransferFunctionGain
</UL>

<P><STRONG><a name="[86]"></a>_is_digit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, __printf_wp.o(i._is_digit))
<BR><BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[80]"></a>_sys_exit</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, tjc_hmi.o(i._sys_exit))
<BR><BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[e1]"></a>assert_failed</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, main.o(i.assert_failed))
<BR><BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_GetITStatus
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearITPendingBit
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_SYSCLKConfig
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PLLConfig
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PLLCmd
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PCLK2Config
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PCLK1Config
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_HSEConfig
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_HCLKConfig
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetFlagStatus
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_SetLatency
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphResetCmd
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphResetCmd
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiveData
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DeInit
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearFlag
</UL>

<P><STRONG><a name="[7b]"></a>main</STRONG> (Thumb, 88 bytes, Stack size 0 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 456 + Unknown Stack Size
<LI>Call Chain = main &rArr; TJC_HMI_UpdateAllParameters &rArr; TJC_HMI_SetOutputFrequency &rArr; TJC_HMI_SetText &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_UpdateAllParameters
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_ShowWelcome
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_Process
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_Init
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Init
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_Init
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CommandParser_Init
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_Init
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_Task
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_SetTargetAmplitude
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_SetGainFactor
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_SetFrequency
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_RegisterCallback
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_Init
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_EnableOutput
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[75]"></a>_get_lc_numeric</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, lc_numeric_c.o(locale$$code))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _get_lc_numeric
</UL>
<BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_lc_numeric_2
</UL>

<P><STRONG><a name="[aa]"></a>__aeabi_dadd</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, daddsub_clz.o(x$fpl$dadd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_TransferFunctionGain
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_TemperatureCorrection
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_FrequencyCorrection
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_CalculateTargetOutput
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_CalculateActualOutput
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_CalculateFrequencyWord_Precision
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_CalculateAmplitudeWord_Precision
</UL>

<P><STRONG><a name="[130]"></a>_dadd</STRONG> (Thumb, 332 bytes, Stack size 16 bytes, daddsub_clz.o(x$fpl$dadd), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub1
</UL>

<P><STRONG><a name="[137]"></a>__fpl_dcmp_Inf</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, dcmpi.o(x$fpl$dcmpinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmple
</UL>

<P><STRONG><a name="[a9]"></a>__aeabi_ddiv</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, ddiv.o(x$fpl$ddiv))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_SetOutputFrequency
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_SetCircuitVoltage
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_TransferFunctionGain
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_Calculate
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_CalculateActualOutput
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_CalculateFrequencyWord_Precision
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_CalculateAmplitudeWord_Precision
</UL>

<P><STRONG><a name="[134]"></a>_ddiv</STRONG> (Thumb, 552 bytes, Stack size 32 bytes, ddiv.o(x$fpl$ddiv), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[ab]"></a>__aeabi_d2uiz</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, dfixu.o(x$fpl$dfixu))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_d2uiz
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_UpdateAllParameters
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_CalculateTargetOutput
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_CalculateActualOutput
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_CalculateFrequencyWord_Precision
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_CalculateAmplitudeWord_Precision
</UL>

<P><STRONG><a name="[135]"></a>_dfixu</STRONG> (Thumb, 90 bytes, Stack size 32 bytes, dfixu.o(x$fpl$dfixu), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[ad]"></a>__aeabi_i2d</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, dflt_clz.o(x$fpl$dflt))
<BR><BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_TemperatureCorrection
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_CalculateFrequencyWord_Precision
</UL>

<P><STRONG><a name="[170]"></a>_dflt</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, dflt_clz.o(x$fpl$dflt), UNUSED)

<P><STRONG><a name="[a4]"></a>__aeabi_ui2d</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, dflt_clz.o(x$fpl$dfltu))
<BR><BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_UpdateAllParameters
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_SetOutputFrequency
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_SetCircuitVoltage
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_TransferFunctionGain
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_FrequencyCorrection
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_Calculate
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_CalculateTargetOutput
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_CalculateActualOutput
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_CalculateFrequencyWord_Precision
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_CalculateAmplitudeWord_Precision
</UL>

<P><STRONG><a name="[171]"></a>_dfltu</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, dflt_clz.o(x$fpl$dfltu), UNUSED)

<P><STRONG><a name="[a6]"></a>__aeabi_cdcmple</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, dleqf.o(x$fpl$dleqf))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_cdcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_ValidateGain
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_TemperatureCorrection
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_FrequencyCorrection
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_Calculate
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_CalculateActualOutput
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ValidateGainFactor
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_CalculateAmplitudeWord_Precision
</UL>

<P><STRONG><a name="[136]"></a>_dcmple</STRONG> (Thumb, 120 bytes, Stack size 32 bytes, dleqf.o(x$fpl$dleqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcmp_Inf
</UL>

<P><STRONG><a name="[13a]"></a>__fpl_dcmple_InfNaN</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, dleqf.o(x$fpl$dleqf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_drcmple
</UL>

<P><STRONG><a name="[a5]"></a>__aeabi_dmul</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, dmul.o(x$fpl$dmul))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_UpdateAllParameters
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_TransferFunctionGain
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_TemperatureCorrection
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_FrequencyCorrection
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_Calculate
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CalculateTotalGain
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_CalculateTargetOutput
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_CalculateFrequencyWord_Precision
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_CalculateAmplitudeWord_Precision
</UL>

<P><STRONG><a name="[138]"></a>_dmul</STRONG> (Thumb, 332 bytes, Stack size 32 bytes, dmul.o(x$fpl$dmul), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[133]"></a>__fpl_dnaninf</STRONG> (Thumb, 156 bytes, Stack size 16 bytes, dnaninf.o(x$fpl$dnaninf))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __fpl_dnaninf
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dmul
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmple
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dfixu
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ddiv
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd
</UL>

<P><STRONG><a name="[132]"></a>__fpl_dretinf</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, dretinf.o(x$fpl$dretinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dmul
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ddiv
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd
</UL>

<P><STRONG><a name="[a7]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, drleqf.o(x$fpl$drleqf))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_ValidateGain
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_TemperatureCorrection
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_FrequencyCorrection
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_Calculate
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_CalculateActualOutput
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ValidateGainFactor
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_CalculateAmplitudeWord_Precision
</UL>

<P><STRONG><a name="[139]"></a>_drcmple</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, drleqf.o(x$fpl$drleqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcmple_InfNaN
</UL>

<P><STRONG><a name="[a8]"></a>__aeabi_drsub</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, daddsub_clz.o(x$fpl$drsb))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_drsub
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_TransferFunctionGain
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_CalculateAmplitudeWord_Precision
</UL>

<P><STRONG><a name="[13b]"></a>_drsb</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, daddsub_clz.o(x$fpl$drsb), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub1
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd1
</UL>

<P><STRONG><a name="[12c]"></a>_dsqrt</STRONG> (Thumb, 404 bytes, Stack size 24 bytes, dsqrt_umaal.o(x$fpl$dsqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>

<P><STRONG><a name="[e9]"></a>__aeabi_dsub</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, daddsub_clz.o(x$fpl$dsub))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_dsub
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_TemperatureCorrection
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_FrequencyCorrection
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_Calculate
</UL>

<P><STRONG><a name="[13d]"></a>_dsub</STRONG> (Thumb, 464 bytes, Stack size 32 bytes, daddsub_clz.o(x$fpl$dsub), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd1
</UL>

<P><STRONG><a name="[71]"></a>_fp_init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, fpinit.o(x$fpl$fpinit))
<BR><BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_fp_1
</UL>

<P><STRONG><a name="[172]"></a>__fplib_config_fpu_vfp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[173]"></a>__fplib_config_pureend_doubles</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[6a]"></a>_printf_fp_dec</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, printf1.o(x$fpl$printf1))
<BR><BR>[Stack]<UL><LI>Max Depth = 324<LI>Call Chain = _printf_fp_dec &rArr; _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_f
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[102]"></a>SetSysClock</STRONG> (Thumb, 220 bytes, Stack size 12 bytes, system_stm32f4xx.o(i.SetSysClock))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = SetSysClock
</UL>
<BR>[Called By]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[fc]"></a>NVIC_SetPriority</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, systick.o(i.NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Init
</UL>

<P><STRONG><a name="[de]"></a>SysTick_UpdateStats</STRONG> (Thumb, 80 bytes, Stack size 0 bytes, systick.o(i.SysTick_UpdateStats))
<BR><BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
</UL>

<P><STRONG><a name="[a3]"></a>AD9910_CalculateAmplitudeWord_Precision</STRONG> (Thumb, 274 bytes, Stack size 56 bytes, ad9910_waveform.o(i.AD9910_CalculateAmplitudeWord_Precision))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = AD9910_CalculateAmplitudeWord_Precision &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_SetAmplitude_Precision
</UL>

<P><STRONG><a name="[ac]"></a>AD9910_CalculateFrequencyWord_Precision</STRONG> (Thumb, 172 bytes, Stack size 56 bytes, ad9910_waveform.o(i.AD9910_CalculateFrequencyWord_Precision))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = AD9910_CalculateFrequencyWord_Precision &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_SetFrequency_Precision
</UL>

<P><STRONG><a name="[d5]"></a>AD9910_WriteProfile</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, ad9910_waveform.o(i.AD9910_WriteProfile))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = AD9910_WriteProfile &rArr; AD9910_HAL_Send8Bits &rArr; GPIO_SetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_Send8Bits
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_HAL_IOUpdate
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_SetFrequency_Precision
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_SetAmplitude_Precision
</UL>

<P><STRONG><a name="[b3]"></a>UpdateSystemTime</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, ad9910_control.o(i.UpdateSystemTime))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UpdateSystemTime
</UL>
<BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_SetTargetAmplitude
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_SetGainFactor
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_SetFrequency
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_EnableOutput
</UL>

<P><STRONG><a name="[c1]"></a>ValidateAmplitude</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, ad9910_control.o(i.ValidateAmplitude))
<BR><BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_SetTargetAmplitude
</UL>

<P><STRONG><a name="[bb]"></a>ValidateFrequency</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, ad9910_control.o(i.ValidateFrequency))
<BR><BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_SetFrequency
</UL>

<P><STRONG><a name="[be]"></a>ValidateGainFactor</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, ad9910_control.o(i.ValidateGainFactor))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = ValidateGainFactor &rArr; __aeabi_cdcmple
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9910_Control_SetGainFactor
</UL>

<P><STRONG><a name="[d8]"></a>CalculateTotalGain</STRONG> (Thumb, 182 bytes, Stack size 48 bytes, gain_calculator.o(i.CalculateTotalGain))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = CalculateTotalGain &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_Calculate
</UL>

<P><STRONG><a name="[e4]"></a>ValidateGainConfig</STRONG> (Thumb, 82 bytes, Stack size 8 bytes, gain_calculator.o(i.ValidateGainConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = ValidateGainConfig &rArr; GainCalculator_ValidateGain &rArr; __aeabi_cdcmple
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_ValidateGain
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GainCalculator_Calculate
</UL>

<P><STRONG><a name="[105]"></a>TJC_AddEvent</STRONG> (Thumb, 86 bytes, Stack size 8 bytes, tjc_hmi.o(i.TJC_AddEvent))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TJC_AddEvent &rArr; __aeabi_memcpy4
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_ParseTouchEvent
</UL>

<P><STRONG><a name="[106]"></a>TJC_ConvertToHz</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, tjc_hmi.o(i.TJC_ConvertToHz))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TJC_ConvertToHz
</UL>
<BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_HandleNumberInput
</UL>

<P><STRONG><a name="[11b]"></a>TJC_ParseNumberInput</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, tjc_hmi.o(i.TJC_ParseNumberInput))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = TJC_ParseNumberInput &rArr; TJC_HMI_HandleNumberInput &rArr; AD9910_Control_SetFrequency &rArr; AD9910_SetFrequency_Precision &rArr; AD9910_CalculateFrequencyWord_Precision &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_HandleNumberInput
</UL>
<BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_ProcessReceivedData
</UL>

<P><STRONG><a name="[11c]"></a>TJC_ParseTextInput</STRONG> (Thumb, 52 bytes, Stack size 48 bytes, tjc_hmi.o(i.TJC_ParseTextInput))
<BR><BR>[Stack]<UL><LI>Max Depth = 352 + Unknown Stack Size
<LI>Call Chain = TJC_ParseTextInput &rArr; TJC_HMI_HandleUnitInput &rArr; TJC_HMI_SetText &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_HandleUnitInput
</UL>
<BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_ProcessReceivedData
</UL>

<P><STRONG><a name="[11e]"></a>TJC_ParseTouchEvent</STRONG> (Thumb, 88 bytes, Stack size 56 bytes, tjc_hmi.o(i.TJC_ParseTouchEvent))
<BR><BR>[Stack]<UL><LI>Max Depth = 352 + Unknown Stack Size
<LI>Call Chain = TJC_ParseTouchEvent &rArr; TJC_HMI_HandleMeasureButton &rArr; TJC_HMI_SetFilterType &rArr; TJC_HMI_SetText &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_AddEvent
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_HandleMeasureButton
</UL>
<BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_ProcessReceivedData
</UL>

<P><STRONG><a name="[10e]"></a>TJC_ParseUnitString</STRONG> (Thumb, 196 bytes, Stack size 32 bytes, tjc_hmi.o(i.TJC_ParseUnitString))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = TJC_ParseUnitString &rArr; strcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_HandleUnitInput
</UL>

<P><STRONG><a name="[113]"></a>TJC_ProcessReceivedData</STRONG> (Thumb, 104 bytes, Stack size 8 bytes, tjc_hmi.o(i.TJC_ProcessReceivedData))
<BR><BR>[Stack]<UL><LI>Max Depth = 360 + Unknown Stack Size
<LI>Call Chain = TJC_ProcessReceivedData &rArr; TJC_ParseTouchEvent &rArr; TJC_HMI_HandleMeasureButton &rArr; TJC_HMI_SetFilterType &rArr; TJC_HMI_SetText &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_ParseTouchEvent
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_ParseTextInput
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_ParseNumberInput
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_StartMeasurement
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_Process
</UL>

<P><STRONG><a name="[115]"></a>TJC_SendByte</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, tjc_hmi.o(i.TJC_SendByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TJC_SendByte &rArr; USART_SendData
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SendString
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_SendCommand
</UL>

<P><STRONG><a name="[114]"></a>TJC_SendString</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, tjc_hmi.o(i.TJC_SendString))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = TJC_SendString &rArr; TJC_SendByte &rArr; USART_SendData
</UL>
<BR>[Calls]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SendByte
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_SendCommand
</UL>

<P><STRONG><a name="[111]"></a>TJC_UART_Init</STRONG> (Thumb, 204 bytes, Stack size 32 bytes, tjc_hmi.o(i.TJC_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = TJC_UART_Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_StructInit
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_StructInit
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DeInit
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearFlag
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_HMI_Init
</UL>

<P><STRONG><a name="[13c]"></a>_dadd1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, daddsub_clz.o(x$fpl$dadd), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_drsb
</UL>

<P><STRONG><a name="[131]"></a>_dsub1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, daddsub_clz.o(x$fpl$dsub), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_drsb
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd
</UL>

<P><STRONG><a name="[8f]"></a>_fp_digits</STRONG> (Thumb, 432 bytes, Stack size 96 bytes, _printf_fp_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 220<LI>Call Chain = _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_d2e
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ll_udiv10
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[62]"></a>_printf_input_char</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _printf_char_common.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> _printf_char_common.o(.text)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
