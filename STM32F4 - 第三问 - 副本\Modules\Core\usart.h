/**
  ******************************************************************************
  * @file    usart.h
  * <AUTHOR>
  * @version V1.0
  * @date    2024
  * @brief   高性能串口调试模块 - 竞赛级实现
  *          支持DMA发送、环形缓冲区、printf重定向等高级功能
  ******************************************************************************
  * @attention
  * 
  * 本模块特性：
  * 1. DMA发送，避免阻塞主程序
  * 2. 环形缓冲区管理，支持大数据量传输
  * 3. printf重定向支持，方便调试
  * 4. 二进制数据传输支持
  * 5. 流控制和错误处理
  * 6. 高速数据传输优化
  * 
  * 硬件连接：
  * - USART1_TX: PA9
  * - USART1_RX: PA10
  * - DMA2_Stream7 (USART1_TX)
  * - DMA2_Stream2 (USART1_RX)
  *
  ******************************************************************************
  */

#ifndef __USART_H
#define __USART_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include <stdint.h>
#include <stdbool.h>
#include <stdio.h>

/* Exported types ------------------------------------------------------------*/

/**
  * @brief  串口配置结构体
  */
typedef struct {
    uint32_t baudrate;           ///< 波特率
    uint16_t word_length;        ///< 数据位长度
    uint16_t stop_bits;          ///< 停止位
    uint16_t parity;             ///< 校验位
    uint16_t flow_control;       ///< 流控制
    bool dma_tx_enable;          ///< DMA发送使能
    bool dma_rx_enable;          ///< DMA接收使能
} USART_Config_t;

/**
  * @brief  环形缓冲区结构体
  */
typedef struct {
    uint8_t* buffer;             ///< 缓冲区指针
    uint32_t size;               ///< 缓冲区大小
    volatile uint32_t head;      ///< 头指针
    volatile uint32_t tail;      ///< 尾指针
    volatile uint32_t count;     ///< 数据计数
    volatile bool overflow;      ///< 溢出标志
} RingBuffer_t;

/**
  * @brief  串口统计信息结构体
  */
typedef struct {
    uint32_t tx_bytes;           ///< 发送字节数
    uint32_t rx_bytes;           ///< 接收字节数
    uint32_t tx_errors;          ///< 发送错误数
    uint32_t rx_errors;          ///< 接收错误数
    uint32_t dma_tx_complete;    ///< DMA发送完成次数
    uint32_t dma_rx_complete;    ///< DMA接收完成次数
    uint32_t buffer_overflows;   ///< 缓冲区溢出次数
} USART_Stats_t;

/**
  * @brief  串口句柄结构体
  */
typedef struct {
    USART_TypeDef* instance;     ///< USART实例
    USART_Config_t config;       ///< 配置参数
    RingBuffer_t tx_buffer;      ///< 发送缓冲区
    RingBuffer_t rx_buffer;      ///< 接收缓冲区
    USART_Stats_t stats;         ///< 统计信息
    volatile bool tx_busy;       ///< 发送忙标志
    volatile bool rx_busy;       ///< 接收忙标志
} USART_Handle_t;

/* Exported constants --------------------------------------------------------*/

/** @defgroup USART_Exported_Constants USART导出常量
  * @{
  */

#define USART_TX_BUFFER_SIZE        2048U    ///< 发送缓冲区大小
#define USART_RX_BUFFER_SIZE        1024U    ///< 接收缓冲区大小
#define USART_DMA_TIMEOUT_MS        1000U    ///< DMA超时时间(ms)

#define USART_DEFAULT_BAUDRATE      115200U  ///< 默认波特率
#define USART_MAX_BAUDRATE          2000000U ///< 最大波特率

// 常用波特率定义
#define USART_BAUDRATE_9600         9600U
#define USART_BAUDRATE_19200        19200U
#define USART_BAUDRATE_38400        38400U
#define USART_BAUDRATE_57600        57600U
#define USART_BAUDRATE_115200       115200U
#define USART_BAUDRATE_230400       230400U
#define USART_BAUDRATE_460800       460800U
#define USART_BAUDRATE_921600       921600U

/**
  * @}
  */

/* Exported macro ------------------------------------------------------------*/

/** @defgroup USART_Exported_Macros USART导出宏
  * @{
  */

/**
  * @brief  检查环形缓冲区是否为空
  */
#define RING_BUFFER_IS_EMPTY(rb)    ((rb)->count == 0)

/**
  * @brief  检查环形缓冲区是否已满
  */
#define RING_BUFFER_IS_FULL(rb)     ((rb)->count >= (rb)->size)

/**
  * @brief  获取环形缓冲区可用空间
  */
#define RING_BUFFER_FREE_SPACE(rb)  ((rb)->size - (rb)->count)

/**
  * @brief  获取环形缓冲区数据长度
  */
#define RING_BUFFER_DATA_LENGTH(rb) ((rb)->count)

/**
  * @brief  调试输出宏
  */
#ifdef DEBUG
    #define USART_DEBUG(fmt, ...)   USART_Printf("[DEBUG] " fmt "\r\n", ##__VA_ARGS__)
    #define USART_INFO(fmt, ...)    USART_Printf("[INFO] " fmt "\r\n", ##__VA_ARGS__)
    #define USART_WARN(fmt, ...)    USART_Printf("[WARN] " fmt "\r\n", ##__VA_ARGS__)
    #define USART_ERROR(fmt, ...)   USART_Printf("[ERROR] " fmt "\r\n", ##__VA_ARGS__)
#else
    #define USART_DEBUG(fmt, ...)
    #define USART_INFO(fmt, ...)
    #define USART_WARN(fmt, ...)
    #define USART_ERROR(fmt, ...)
#endif

/**
  * @}
  */

/* Exported variables --------------------------------------------------------*/

/** @defgroup USART_Exported_Variables USART导出变量
  * @{
  */

extern USART_Handle_t g_usart1_handle;          ///< USART1句柄
extern volatile bool g_usart_tx_complete_flag;  ///< 发送完成标志
extern volatile bool g_usart_rx_complete_flag;  ///< 接收完成标志

/**
  * @}
  */

/* Exported functions --------------------------------------------------------*/

/** @defgroup USART_Exported_Functions USART导出函数
  * @{
  */

/**
  * @brief  USART1初始化
  * @param  baudrate: 波特率
  * @retval 0: 成功, -1: 失败
  */
int8_t USART1_Init(uint32_t baudrate);

/**
  * @brief  USART高级初始化
  * @param  handle: USART句柄
  * @param  config: 配置参数
  * @retval 0: 成功, -1: 失败
  */
int8_t USART_Module_Init(USART_Handle_t* handle, USART_Config_t* config);

/**
  * @brief  USART反初始化
  * @param  handle: USART句柄
  * @retval None
  */
void USART_Module_DeInit(USART_Handle_t* handle);

/**
  * @brief  发送单个字节
  * @param  data: 要发送的字节
  * @retval 0: 成功, -1: 失败
  */
int8_t USART_SendByte(uint8_t data);

/**
  * @brief  发送字符串
  * @param  str: 要发送的字符串
  * @retval 发送的字节数
  */
uint32_t USART_SendString(const char* str);

/**
  * @brief  发送数据缓冲区
  * @param  data: 数据缓冲区
  * @param  length: 数据长度
  * @retval 发送的字节数
  */
uint32_t USART_Module_SendData(const uint8_t* data, uint32_t length);

/**
  * @brief  DMA发送数据
  * @param  data: 数据缓冲区
  * @param  length: 数据长度
  * @retval 0: 成功, -1: 失败
  */
int8_t USART_SendData_DMA(const uint8_t* data, uint32_t length);

/**
  * @brief  接收单个字节
  * @param  data: 接收数据指针
  * @param  timeout_ms: 超时时间(ms)
  * @retval 0: 成功, -1: 超时或失败
  */
int8_t USART_ReceiveByte(uint8_t* data, uint32_t timeout_ms);

/**
  * @brief  接收数据到缓冲区
  * @param  buffer: 接收缓冲区
  * @param  length: 期望接收长度
  * @param  timeout_ms: 超时时间(ms)
  * @retval 实际接收的字节数
  */
uint32_t USART_Module_ReceiveData(uint8_t* buffer, uint32_t length, uint32_t timeout_ms);

/**
  * @brief  格式化输出(printf重定向)
  * @param  format: 格式字符串
  * @param  ...: 可变参数
  * @retval 输出的字符数
  */
int USART_Printf(const char* format, ...);

/**
  * @brief  发送十六进制数据
  * @param  data: 数据缓冲区
  * @param  length: 数据长度
  * @retval None
  */
void USART_SendHex(const uint8_t* data, uint32_t length);

/**
  * @brief  获取统计信息
  * @param  stats: 统计信息结构体指针
  * @retval None
  */
void USART_GetStats(USART_Stats_t* stats);

/**
  * @brief  重置统计信息
  * @param  None
  * @retval None
  */
void USART_ResetStats(void);

/**
  * @brief  检查发送是否完成
  * @param  None
  * @retval true: 完成, false: 未完成
  */
bool USART_IsTxComplete(void);

/**
  * @brief  等待发送完成
  * @param  timeout_ms: 超时时间(ms)
  * @retval 0: 成功, -1: 超时
  */
int8_t USART_WaitTxComplete(uint32_t timeout_ms);

/**
  * @brief  USART1中断处理函数
  * @param  None
  * @retval None
  */
void USART1_IRQHandler(void);

/**
  * @brief  DMA2_Stream7中断处理函数(USART1_TX)
  * @param  None
  * @retval None
  */
void DMA2_Stream7_IRQHandler(void);

/**
  * @brief  DMA2_Stream2中断处理函数(USART1_RX)
  * @param  None
  * @retval None
  */
void DMA2_Stream2_IRQHandler(void);

/**
  * @}
  */

#ifdef __cplusplus
}
#endif

#endif /* __USART_H */

/************************ (C) COPYRIGHT 嵌入式竞赛团队 *****END OF FILE****/
