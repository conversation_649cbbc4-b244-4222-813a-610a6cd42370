/**
  ******************************************************************************
  * @file    parallel_adc.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024
  * @brief   高速并行ADC接口模块实现
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "parallel_adc.h"
#include "systick.h"
#include <string.h>

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/

/** @defgroup ParallelADC_Private_Defines 并行ADC私有定义
  * @{
  */

#define DATA_GPIO_PORT              GPIOC
#define DATA_GPIO_CLK               RCC_AHB1Periph_GPIOC
#define CLOCK_GPIO_PORT             GPIOA
#define CLOCK_GPIO_CLK              RCC_AHB1Periph_GPIOA

#define CLOCK_PIN                   GPIO_Pin_0   ///< PA0 - EXTI0
#define DATA_VALID_PIN              GPIO_Pin_1   ///< PA1
#define OVERFLOW_PIN                GPIO_Pin_2   ///< PA2

#define DATA_PINS_ALL               (GPIO_Pin_0 | GPIO_Pin_1 | GPIO_Pin_2 | GPIO_Pin_3 | \
                                     GPIO_Pin_4 | GPIO_Pin_5 | GPIO_Pin_6 | GPIO_Pin_7 | \
                                     GPIO_Pin_8 | GPIO_Pin_9 | GPIO_Pin_10 | GPIO_Pin_11 | \
                                     GPIO_Pin_12 | GPIO_Pin_13)  ///< PC0-PC13 (14位数据)

#define EXTI_LINE                   EXTI_Line0
#define EXTI_PORT_SOURCE            EXTI_PortSourceGPIOA
#define EXTI_PIN_SOURCE             EXTI_PinSource0

/**
  * @}
  */

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/** @defgroup ParallelADC_Private_Variables 并行ADC私有变量
  * @{
  */

ParallelADC_Handle_t g_parallel_adc_handle;     ///< 并行ADC句柄
volatile bool g_parallel_adc_data_ready = false; ///< 数据就绪标志
volatile bool g_parallel_adc_overflow = false;  ///< 溢出标志

// 静态缓冲区 - 黄金参考全局变量
volatile uint16_t g_adc_buffer[PARALLEL_ADC_BUFFER_SIZE];
volatile uint32_t g_adc_buffer_index = 0;
volatile uint8_t g_adc_capture_finished = 0;

// 缺失的静态变量声明
static uint16_t s_parallel_adc_buffer[PARALLEL_ADC_BUFFER_SIZE]; ///< 静态数据缓冲区
static uint32_t s_last_time = 0;                                ///< 上次统计时间
static uint32_t s_sample_count = 0;                             ///< 采样计数器

/**
  * @}
  */

/* Private function prototypes -----------------------------------------------*/
static void ParallelADC_GPIO_Config(void);
static void ParallelADC_EXTI_Config(uint8_t trigger_edge);
static void ParallelADC_NVIC_Config(void);
static void ParallelADC_Buffer_Init(void);
static int8_t ParallelADC_Buffer_Write(uint16_t data) __attribute__((unused));
static int8_t ParallelADC_Buffer_Read(uint16_t* data);
static void ParallelADC_UpdateStats(uint16_t data) __attribute__((unused));

/* Private functions ---------------------------------------------------------*/

/**
  * @brief  并行ADC GPIO配置
  * @param  None
  * @retval None
  */
static void ParallelADC_GPIO_Config(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    
    // 使能GPIO时钟
    RCC_AHB1PeriphClockCmd(DATA_GPIO_CLK | CLOCK_GPIO_CLK, ENABLE);
    
    // 配置数据引脚PC0-PC13为浮空输入 (14位LTC2246)
    GPIO_InitStructure.GPIO_Pin = DATA_PINS_ALL;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(DATA_GPIO_PORT, &GPIO_InitStructure);
    
    // 配置时钟引脚PA0为浮空输入
    GPIO_InitStructure.GPIO_Pin = CLOCK_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(CLOCK_GPIO_PORT, &GPIO_InitStructure);
    
    // 配置数据有效和溢出检测引脚
    if (g_parallel_adc_handle.config.enable_data_valid || 
        g_parallel_adc_handle.config.enable_overflow_detect) {
        GPIO_InitStructure.GPIO_Pin = DATA_VALID_PIN | OVERFLOW_PIN;
        GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN;
        GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
        GPIO_Init(CLOCK_GPIO_PORT, &GPIO_InitStructure);
    }
}

/**
  * @brief  并行ADC外部中断配置
  * @param  trigger_edge: 触发边沿
  * @retval None
  */
static void ParallelADC_EXTI_Config(uint8_t trigger_edge)
{
    EXTI_InitTypeDef EXTI_InitStructure;
    
    // 使能SYSCFG时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_SYSCFG, ENABLE);
    
    // 连接EXTI线到GPIO引脚
    SYSCFG_EXTILineConfig(EXTI_PORT_SOURCE, EXTI_PIN_SOURCE);
    
    // 配置EXTI线
    EXTI_InitStructure.EXTI_Line = EXTI_LINE;
    EXTI_InitStructure.EXTI_Mode = EXTI_Mode_Interrupt;
    EXTI_InitStructure.EXTI_Trigger = (trigger_edge == PARALLEL_ADC_TRIGGER_RISING) ? 
                                      EXTI_Trigger_Rising : EXTI_Trigger_Falling;
    EXTI_InitStructure.EXTI_LineCmd = ENABLE;
    EXTI_Init(&EXTI_InitStructure);
}

/**
  * @brief  并行ADC NVIC配置
  * @param  None
  * @retval None
  */
static void ParallelADC_NVIC_Config(void)
{
    NVIC_InitTypeDef NVIC_InitStructure;
    
    // 配置EXTI0中断
    NVIC_InitStructure.NVIC_IRQChannel = EXTI0_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0; // 最高优先级
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);
}

/**
  * @brief  并行ADC缓冲区初始化
  * @param  None
  * @retval None
  */
static void ParallelADC_Buffer_Init(void)
{
    g_parallel_adc_handle.buffer.buffer = s_parallel_adc_buffer;
    g_parallel_adc_handle.buffer.size = PARALLEL_ADC_BUFFER_SIZE;
    g_parallel_adc_handle.buffer.head = 0;
    g_parallel_adc_handle.buffer.tail = 0;
    g_parallel_adc_handle.buffer.count = 0;
    g_parallel_adc_handle.buffer.full = false;
    g_parallel_adc_handle.buffer.overflow = false;
}

/**
  * @brief  向缓冲区写入数据
  * @param  data: 要写入的数据
  * @retval 0: 成功, -1: 缓冲区满
  */
static int8_t ParallelADC_Buffer_Write(uint16_t data)
{
    if (g_parallel_adc_handle.buffer.full) {
        g_parallel_adc_handle.buffer.overflow = true;
        g_parallel_adc_handle.stats.overflow_count++;
        return -1;
    }
    
    g_parallel_adc_handle.buffer.buffer[g_parallel_adc_handle.buffer.head] = data;
    g_parallel_adc_handle.buffer.head = (g_parallel_adc_handle.buffer.head + 1) % 
                                        g_parallel_adc_handle.buffer.size;
    g_parallel_adc_handle.buffer.count++;
    
    if (g_parallel_adc_handle.buffer.count >= g_parallel_adc_handle.buffer.size) {
        g_parallel_adc_handle.buffer.full = true;
        g_parallel_adc_handle.state = PARALLEL_ADC_STATE_FULL;
    }
    
    return 0;
}

/**
  * @brief  从缓冲区读取数据
  * @param  data: 数据指针
  * @retval 0: 成功, -1: 缓冲区空
  */
static int8_t ParallelADC_Buffer_Read(uint16_t* data)
{
    if (g_parallel_adc_handle.buffer.count == 0) {
        return -1;
    }
    
    *data = g_parallel_adc_handle.buffer.buffer[g_parallel_adc_handle.buffer.tail];
    g_parallel_adc_handle.buffer.tail = (g_parallel_adc_handle.buffer.tail + 1) % 
                                        g_parallel_adc_handle.buffer.size;
    g_parallel_adc_handle.buffer.count--;
    
    if (g_parallel_adc_handle.buffer.full) {
        g_parallel_adc_handle.buffer.full = false;
        if (g_parallel_adc_handle.state == PARALLEL_ADC_STATE_FULL) {
            g_parallel_adc_handle.state = PARALLEL_ADC_STATE_RUNNING;
        }
    }
    
    return 0;
}

/**
  * @brief  更新统计信息
  * @param  data: 数据值
  * @retval None
  */
static void ParallelADC_UpdateStats(uint16_t data)
{
    g_parallel_adc_handle.stats.total_samples++;
    
    // 更新最大最小值
    if (data > g_parallel_adc_handle.stats.max_value) {
        g_parallel_adc_handle.stats.max_value = data;
    }
    if (data < g_parallel_adc_handle.stats.min_value) {
        g_parallel_adc_handle.stats.min_value = data;
    }
    
    // 更新平均值(移动平均)
    g_parallel_adc_handle.stats.avg_value = 
        (g_parallel_adc_handle.stats.avg_value * 15 + data) / 16;
    
    // 计算采样率
    uint32_t current_time = SysTick_GetTick();
    if (current_time > s_last_time + 1000) { // 每秒更新一次
        uint32_t samples_diff = g_parallel_adc_handle.stats.total_samples - s_sample_count;
        uint32_t time_diff = current_time - s_last_time;
        g_parallel_adc_handle.stats.actual_sample_rate = 
            (float)samples_diff * 1000.0f / time_diff;
        
        // 更新峰值采样率
        if (g_parallel_adc_handle.stats.actual_sample_rate > 
            g_parallel_adc_handle.stats.max_sample_rate) {
            g_parallel_adc_handle.stats.max_sample_rate = 
                (uint32_t)g_parallel_adc_handle.stats.actual_sample_rate;
        }
        
        s_sample_count = g_parallel_adc_handle.stats.total_samples;
        s_last_time = current_time;
    }
}

/* Exported functions --------------------------------------------------------*/

/**
  * @brief  并行ADC初始化
  * @param  config: 配置参数指针
  * @retval 0: 成功, -1: 失败
  */
int8_t ParallelADC_Init(ParallelADC_Config_t* config)
{
    if (config == NULL) {
        return -1;
    }
    
    // 初始化句柄
    g_parallel_adc_handle.data_port = DATA_GPIO_PORT;
    g_parallel_adc_handle.clock_port = CLOCK_GPIO_PORT;
    g_parallel_adc_handle.clock_pin = CLOCK_PIN;
    g_parallel_adc_handle.config = *config;
    g_parallel_adc_handle.state = PARALLEL_ADC_STATE_IDLE;
    g_parallel_adc_handle.last_sample_time = 0;
    
    // 初始化缓冲区
    ParallelADC_Buffer_Init();
    
    // 配置硬件
    ParallelADC_GPIO_Config();
    ParallelADC_EXTI_Config(config->trigger_edge);
    ParallelADC_NVIC_Config();
    
    // 重置统计信息
    ParallelADC_ResetStats();
    
    return 0;
}

/**
  * @brief  开始数据采集
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t ParallelADC_Start(void)
{
    if (g_parallel_adc_handle.state == PARALLEL_ADC_STATE_RUNNING) {
        return -1; // 已经在运行
    }
    
    // 清除标志
    g_parallel_adc_data_ready = false;
    g_parallel_adc_overflow = false;
    
    // 清空缓冲区
    ParallelADC_ClearBuffer();
    
    // 使能外部中断
    EXTI_ClearITPendingBit(EXTI_LINE);
    
    g_parallel_adc_handle.state = PARALLEL_ADC_STATE_RUNNING;
    
    return 0;
}

/**
  * @brief  停止数据采集
  * @param  None
  * @retval None
  */
void ParallelADC_Stop(void)
{
    g_parallel_adc_handle.state = PARALLEL_ADC_STATE_IDLE;
}

/**
  * @brief  读取单个数据
  * @param  data: 数据指针
  * @retval 0: 成功, -1: 无数据
  */
int8_t ParallelADC_ReadSingle(uint16_t* data)
{
    if (data == NULL) {
        return -1;
    }

    return ParallelADC_Buffer_Read(data);
}

/**
  * @brief  读取多个数据
  * @param  buffer: 数据缓冲区
  * @param  length: 期望读取长度
  * @retval 实际读取的数据长度
  */
uint32_t ParallelADC_ReadMultiple(uint16_t* buffer, uint32_t length)
{
    if (buffer == NULL || length == 0) {
        return 0;
    }

    uint32_t read_count = 0;

    for (uint32_t i = 0; i < length; i++) {
        if (ParallelADC_Buffer_Read(&buffer[i]) == 0) {
            read_count++;
        } else {
            break; // 缓冲区空
        }
    }

    return read_count;
}

/**
  * @brief  获取缓冲区状态
  * @param  None
  * @retval 当前状态
  */
ParallelADC_State_t ParallelADC_GetState(void)
{
    return g_parallel_adc_handle.state;
}

/**
  * @brief  清空缓冲区
  * @param  None
  * @retval None
  */
void ParallelADC_ClearBuffer(void)
{
    __disable_irq();

    g_parallel_adc_handle.buffer.head = 0;
    g_parallel_adc_handle.buffer.tail = 0;
    g_parallel_adc_handle.buffer.count = 0;
    g_parallel_adc_handle.buffer.full = false;
    g_parallel_adc_handle.buffer.overflow = false;

    __enable_irq();
}

/**
  * @brief  获取统计信息
  * @param  stats: 统计信息结构体指针
  * @retval None
  */
void ParallelADC_GetStats(ParallelADC_Stats_t* stats)
{
    if (stats == NULL) {
        return;
    }

    *stats = g_parallel_adc_handle.stats;
}

/**
  * @brief  重置统计信息
  * @param  None
  * @retval None
  */
void ParallelADC_ResetStats(void)
{
    memset(&g_parallel_adc_handle.stats, 0, sizeof(ParallelADC_Stats_t));
    g_parallel_adc_handle.stats.min_value = 0xFFFF;
    s_sample_count = 0;
    s_last_time = SysTick_GetTick();
}

/**
  * @brief  设置触发模式
  * @param  edge: 触发边沿
  * @retval 0: 成功, -1: 失败
  */
int8_t ParallelADC_SetTriggerEdge(uint8_t edge)
{
    if (g_parallel_adc_handle.state == PARALLEL_ADC_STATE_RUNNING) {
        return -1; // 运行时不能修改
    }

    g_parallel_adc_handle.config.trigger_edge = edge;
    ParallelADC_EXTI_Config(edge);

    return 0;
}

/**
  * @brief  获取实时采样率
  * @param  None
  * @retval 当前采样率(Hz)
  */
float ParallelADC_GetSampleRate(void)
{
    return g_parallel_adc_handle.stats.actual_sample_rate;
}

/**
  * @brief  EXTI0中断处理函数内部逻辑 - 黄金参考实现
  * @param  None
  * @retval None
  * @note   极度精简的ISR，确保最高采样率性能
  */
void EXTI0_IRQHandler_Internal(void)
{
    // 1. 检查是否是EXTI Line 0的中断
    if (EXTI_GetITStatus(EXTI_LINE) != RESET) {

        // 2. **立即**读取数据，这是最关键的操作
        // 假设缓冲区未满
        if (g_adc_buffer_index < PARALLEL_ADC_BUFFER_SIZE) {
            g_adc_buffer[g_adc_buffer_index] = (uint16_t)(GPIOC->IDR & 0x3FFF); // 14位掩码
            g_adc_buffer_index++;

            // 如果采集满了，设置标志位
            if (g_adc_buffer_index == PARALLEL_ADC_BUFFER_SIZE) {
                g_adc_capture_finished = 1;
                // 注意：这里可以先不关闭中断，让其继续"空采"，防止丢失同步
            }
        }

        // 3. **最后**清除中断挂起位
        EXTI_ClearITPendingBit(EXTI_LINE);
    }
}

/************************ (C) COPYRIGHT 嵌入式竞赛团队 *****END OF FILE****/
