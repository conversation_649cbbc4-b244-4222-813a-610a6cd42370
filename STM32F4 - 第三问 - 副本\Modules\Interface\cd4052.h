#ifndef __CD4052_H
#define __CD4052_H

#include "stm32f4xx.h"
#include "bsp.h"

// CD4052 增益级别定义
#define CD4052_GAIN_LEVEL_0    0  // B=0, A=0
#define CD4052_GAIN_LEVEL_1    1  // B=0, A=1
#define CD4052_GAIN_LEVEL_2    2  // B=1, A=0
#define CD4052_GAIN_LEVEL_3    3  // B=1, A=1

// CD4052 错误代码定义
#define CD4052_OK              0
#define CD4052_PARAM_ERROR     1

// 函数声明
uint8_t CD4052_Init(void);
uint8_t CD4052_SetGain(uint8_t level);
uint8_t CD4052_GetGain(void);

#endif
