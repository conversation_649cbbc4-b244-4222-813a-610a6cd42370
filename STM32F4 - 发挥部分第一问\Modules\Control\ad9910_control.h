/**
  ******************************************************************************
  * @file    ad9910_control.h
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   AD9910外部控制接口头文件 - 支持多种外部设备控制
  ******************************************************************************
  * @attention
  *
  * 本文件实现AD9910的外部控制接口，支持：
  * - 串口屏控制 (UART通信)
  * - 4x4矩阵键盘控制 (GPIO扫描)
  * - 实时参数调节 (频率、峰峰值)
  * - 增益计算算法 (考虑后续电路增益)
  * - 状态监控和反馈
  *
  * 控制架构：
  * 外部设备 -> 控制接口 -> 参数管理 -> AD9910驱动 -> 信号输出
  *
  ******************************************************************************
  */

#ifndef __AD9910_CONTROL_H
#define __AD9910_CONTROL_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include "../Generation/ad9910_waveform.h"
#include <stdbool.h>
#include <stdint.h>

/* Exported types ------------------------------------------------------------*/

/**
 * @brief  控制命令类型枚举
 */
typedef enum {
    CMD_SET_FREQUENCY = 0x01,      ///< 设置频率
    CMD_SET_AMPLITUDE = 0x02,      ///< 设置峰峰值
    CMD_GET_STATUS = 0x03,         ///< 获取状态
    CMD_SET_GAIN = 0x04,           ///< 设置增益系数
    CMD_ENABLE_OUTPUT = 0x05,      ///< 使能输出
    CMD_DISABLE_OUTPUT = 0x06,     ///< 禁用输出
    CMD_RESET = 0x07,              ///< 复位
    CMD_CALIBRATE = 0x08,          ///< 校准
    CMD_GET_PARAMS = 0x09,         ///< 获取参数范围
    CMD_SET_PRESET = 0x0A          ///< 设置预设值
} ControlCommand_t;

/**
 * @brief  控制状态枚举
 */
typedef enum {
    CTRL_STATUS_OK = 0x00,         ///< 操作成功
    CTRL_STATUS_ERROR = 0x01,      ///< 操作失败
    CTRL_STATUS_INVALID_PARAM = 0x02, ///< 参数无效
    CTRL_STATUS_OUT_OF_RANGE = 0x03,  ///< 参数超出范围
    CTRL_STATUS_BUSY = 0x04,       ///< 设备忙
    CTRL_STATUS_NOT_READY = 0x05   ///< 设备未就绪
} ControlStatus_t;

/**
 * @brief  系统参数结构体
 */
typedef struct {
    uint32_t frequency_hz;         ///< 当前频率 (Hz)
    uint16_t amplitude_mv;         ///< 当前峰峰值 (mV)
    double gain_factor;            ///< 增益系数
    uint16_t target_amplitude_mv;  ///< 目标输出峰峰值 (考虑增益后)
    bool output_enabled;           ///< 输出使能状态
    uint32_t last_update_time;     ///< 最后更新时间
} SystemParams_t;

/**
 * @brief  参数范围结构体
 */
typedef struct {
    uint32_t min_frequency_hz;     ///< 最小频率
    uint32_t max_frequency_hz;     ///< 最大频率
    uint16_t min_amplitude_mv;     ///< 最小峰峰值
    uint16_t max_amplitude_mv;     ///< 最大峰峰值
    double min_gain_factor;        ///< 最小增益系数
    double max_gain_factor;        ///< 最大增益系数
} ParamRanges_t;

/**
 * @brief  预设参数结构体
 */
typedef struct {
    char name[16];                 ///< 预设名称
    uint32_t frequency_hz;         ///< 预设频率
    uint16_t target_amplitude_mv;  ///< 预设目标峰峰值
    double gain_factor;            ///< 预设增益系数
} PresetParams_t;

/**
 * @brief  控制回调函数类型
 */
typedef void (*ControlCallback_t)(ControlCommand_t cmd, ControlStatus_t status, void* data);

/* Exported constants --------------------------------------------------------*/

/* 默认参数范围 */
#define CTRL_MIN_FREQUENCY_HZ       1           ///< 最小频率 1Hz
#define CTRL_MAX_FREQUENCY_HZ       420000000   ///< 最大频率 420MHz
#define CTRL_MIN_AMPLITUDE_MV       10          ///< 最小峰峰值 10mV
#define CTRL_MAX_AMPLITUDE_MV       5000        ///< 最大峰峰值 5V (考虑增益)
#define CTRL_MIN_GAIN_FACTOR        0.1         ///< 最小增益系数
#define CTRL_MAX_GAIN_FACTOR        10.0        ///< 最大增益系数

/* 默认参数值 */
#define CTRL_DEFAULT_FREQUENCY      5000000     ///< 默认频率 5MHz
#define CTRL_DEFAULT_AMPLITUDE      500         ///< 默认峰峰值 0.5V
#define CTRL_DEFAULT_GAIN           1.0         ///< 默认增益系数

/* 预设参数数量 */
#define CTRL_MAX_PRESETS            8           ///< 最大预设数量

/* Exported functions --------------------------------------------------------*/

/**
 * @brief  控制系统初始化
 * @param  None
 * @retval ControlStatus_t 初始化状态
 */
ControlStatus_t AD9910_Control_Init(void);

/**
 * @brief  执行控制命令
 * @param  cmd: 控制命令
 * @param  param1: 参数1
 * @param  param2: 参数2
 * @retval ControlStatus_t 执行状态
 */
ControlStatus_t AD9910_Control_Execute(ControlCommand_t cmd, uint32_t param1, uint32_t param2);

/**
 * @brief  设置频率
 * @param  frequency_hz: 频率值 (Hz)
 * @retval ControlStatus_t 设置状态
 */
ControlStatus_t AD9910_Control_SetFrequency(uint32_t frequency_hz);

/**
 * @brief  设置目标峰峰值 (考虑增益)
 * @param  target_amplitude_mv: 目标峰峰值 (mV)
 * @retval ControlStatus_t 设置状态
 */
ControlStatus_t AD9910_Control_SetTargetAmplitude(uint16_t target_amplitude_mv);

/**
 * @brief  设置增益系数
 * @param  gain_factor: 增益系数
 * @retval ControlStatus_t 设置状态
 */
ControlStatus_t AD9910_Control_SetGainFactor(double gain_factor);

/**
 * @brief  使能/禁用输出
 * @param  enable: true-使能, false-禁用
 * @retval ControlStatus_t 设置状态
 */
ControlStatus_t AD9910_Control_EnableOutput(bool enable);

/**
 * @brief  获取当前系统参数
 * @param  params: 参数结构体指针
 * @retval ControlStatus_t 获取状态
 */
ControlStatus_t AD9910_Control_GetParams(SystemParams_t* params);

/**
 * @brief  获取参数范围
 * @param  ranges: 参数范围结构体指针
 * @retval ControlStatus_t 获取状态
 */
ControlStatus_t AD9910_Control_GetRanges(ParamRanges_t* ranges);

/**
 * @brief  设置预设参数
 * @param  preset_id: 预设编号 (0-7)
 * @param  preset: 预设参数指针
 * @retval ControlStatus_t 设置状态
 */
ControlStatus_t AD9910_Control_SetPreset(uint8_t preset_id, const PresetParams_t* preset);

/**
 * @brief  加载预设参数
 * @param  preset_id: 预设编号 (0-7)
 * @retval ControlStatus_t 加载状态
 */
ControlStatus_t AD9910_Control_LoadPreset(uint8_t preset_id);

/**
 * @brief  注册控制回调函数
 * @param  callback: 回调函数指针
 * @retval None
 */
void AD9910_Control_RegisterCallback(ControlCallback_t callback);

/**
 * @brief  控制系统任务处理 (在主循环中调用)
 * @param  None
 * @retval None
 */
void AD9910_Control_Task(void);

/* ==================== 增益计算相关函数 ==================== */

/**
 * @brief  计算AD9910实际输出值 (考虑增益)
 * @param  target_amplitude_mv: 目标输出峰峰值
 * @param  gain_factor: 增益系数
 * @retval 计算得到的AD9910输出值
 */
uint16_t AD9910_Control_CalculateActualOutput(uint16_t target_amplitude_mv, double gain_factor);

/**
 * @brief  计算目标输出值 (已知AD9910输出和增益)
 * @param  actual_output_mv: AD9910实际输出
 * @param  gain_factor: 增益系数
 * @retval 计算得到的目标输出值
 */
uint16_t AD9910_Control_CalculateTargetOutput(uint16_t actual_output_mv, double gain_factor);

/**
 * @brief  验证参数有效性
 * @param  frequency_hz: 频率值
 * @param  amplitude_mv: 峰峰值
 * @param  gain_factor: 增益系数
 * @retval true-有效, false-无效
 */
bool AD9910_Control_ValidateParams(uint32_t frequency_hz, uint16_t amplitude_mv, double gain_factor);

#ifdef __cplusplus
}
#endif

#endif /* __AD9910_CONTROL_H */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
