/**
  ******************************************************************************
  * @file    systick.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024
  * @brief   高精度SysTick延时模块实现
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "systick.h"
#include <stdio.h>  // 添加sprintf支持

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/

/** @defgroup SysTick_Private_Defines SysTick私有定义
  * @{
  */

#ifndef DWT_CTRL_CYCCNTENA_Msk
#define DWT_CTRL_CYCCNTENA_Msk      (1UL << 0)  ///< DWT CYCCNT使能位
#endif
#define SYSTICK_LOAD_RELOAD_Msk     (0xFFFFFFUL) ///< SysTick重装载值掩码

/**
  * @}
  */

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/** @defgroup SysTick_Private_Variables SysTick私有变量
  * @{
  */

volatile uint32_t g_systick_counter = 0;        ///< SysTick计数器
volatile uint32_t g_system_uptime_ms = 0;       ///< 系统运行时间(ms)
SysTick_Calibration_t g_systick_cal = {1.0f, 0, 0}; ///< 延时校准参数
SysTick_Stats_t g_systick_stats = {0};          ///< 系统时间统计

static volatile uint32_t s_delay_counter = 0;   ///< 延时计数器
static bool s_dwt_initialized = false;          ///< DWT初始化标志

/**
  * @}
  */

/* Private function prototypes -----------------------------------------------*/
static void SysTick_UpdateStats(uint32_t delay_us);
static uint32_t SysTick_GetCalibratedDelay(uint32_t delay_us);

/* Private functions ---------------------------------------------------------*/

/**
  * @brief  更新系统时间统计
  * @param  delay_us: 延时时间(微秒)
  * @retval None
  */
static void SysTick_UpdateStats(uint32_t delay_us)
{
    g_systick_stats.total_delay_us += delay_us;
    g_systick_stats.delay_call_count++;
    g_systick_stats.system_uptime_ms = g_system_uptime_ms;
    
    // 计算CPU使用率 (简化算法)
    if (g_system_uptime_ms > 0) {
        g_systick_stats.cpu_usage_percent = 
            (float)(g_systick_stats.total_delay_us / 1000.0f) / g_system_uptime_ms * 100.0f;
    }
}

/**
  * @brief  获取校准后的延时时间
  * @param  delay_us: 原始延时时间(微秒)
  * @retval 校准后的延时时间(微秒)
  */
static uint32_t SysTick_GetCalibratedDelay(uint32_t delay_us)
{
    float calibrated_delay = delay_us * g_systick_cal.calibration_factor;
    
    // 应用温度补偿
    calibrated_delay += (g_systick_cal.temperature_offset / 1000.0f);
    
    return (uint32_t)calibrated_delay;
}

/* Exported functions --------------------------------------------------------*/

/**
  * @brief  SysTick系统初始化
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t SysTick_Init(void)
{
    // 配置SysTick为1ms中断
    if (SysTick_Config(SystemCoreClock / SYSTICK_FREQUENCY_HZ) != 0) {
        return -1; // 配置失败
    }
    
    // 设置SysTick中断优先级
    NVIC_SetPriority(SysTick_IRQn, 0x0F); // 最低优先级
    
    // 初始化DWT
    if (DWT_Init() != 0) {
        return -1; // DWT初始化失败
    }
    
    // 初始化校准参数
    g_systick_cal.calibration_factor = 1.0f;
    g_systick_cal.temperature_offset = 0;
    g_systick_cal.last_calibration = 0;
    
    // 重置统计数据
    SysTick_ResetStats();
    
    return 0; // 成功
}

/**
  * @brief  DWT调试单元初始化
  * @param  None  
  * @retval 0: 成功, -1: 失败
  */
int8_t DWT_Init(void)
{
    // 使能DWT和ITM
    CoreDebug->DEMCR |= CoreDebug_DEMCR_TRCENA_Msk;
    
    // 重置DWT计数器
    DWT->CYCCNT = 0;
    
    // 使能DWT计数器
    DWT->CTRL |= DWT_CTRL_CYCCNTENA_Msk;
    
    // 检查DWT是否正常工作
    uint32_t start_count = DWT->CYCCNT;
    for (volatile int i = 0; i < 1000; i++); // 简单延时
    uint32_t end_count = DWT->CYCCNT;
    
    if (end_count > start_count) {
        s_dwt_initialized = true;
        return 0; // 成功
    }
    
    return -1; // 失败
}

/**
  * @brief  微秒级精确延时(阻塞)
  * @param  us: 延时时间(微秒), 范围: 1 - 1000000
  * @retval None
  * @note   使用DWT计数器实现，精度±1us
  */
void Delay_us(uint32_t us)
{
    if (!s_dwt_initialized || us == 0) {
        return;
    }
    
    // 限制最大延时时间
    if (us > SYSTICK_MAX_DELAY_US) {
        us = SYSTICK_MAX_DELAY_US;
    }
    
    // 获取校准后的延时时间
    uint32_t calibrated_us = SysTick_GetCalibratedDelay(us);
    
    // 计算需要的DWT周期数
    uint32_t cycles = US_TO_DWT_CYCLES(calibrated_us);
    
    // 记录开始时间
    uint32_t start_cycles = DWT->CYCCNT;
    
    // 等待指定周期数
    while ((DWT->CYCCNT - start_cycles) < cycles) {
        __NOP(); // 防止编译器优化
    }
    
    // 更新统计信息
    SysTick_UpdateStats(us);
}

/**
  * @brief  毫秒级延时(阻塞)
  * @param  ms: 延时时间(毫秒)
  * @retval None
  * @note   基于SysTick中断实现
  */
void Delay_ms(uint32_t ms)
{
    if (ms == 0) {
        return;
    }
    
    s_delay_counter = ms;
    
    while (s_delay_counter != 0) {
        __WFI(); // 等待中断，降低功耗
    }
    
    // 更新统计信息
    SysTick_UpdateStats(ms * 1000);
}

/**
  * @brief  秒级延时(阻塞)
  * @param  s: 延时时间(秒)
  * @retval None
  */
void Delay_s(uint32_t s)
{
    for (uint32_t i = 0; i < s; i++) {
        Delay_ms(1000);
    }
}

/**
  * @brief  非阻塞延时初始化
  * @param  handle: 非阻塞延时句柄
  * @param  delay_us: 延时时间(微秒)
  * @retval None
  */
void SysTick_NonBlocking_Init(SysTick_NonBlocking_t* handle, uint32_t delay_us)
{
    if (handle == NULL) {
        return;
    }
    
    handle->start_time = DWT->CYCCNT;
    handle->delay_time = US_TO_DWT_CYCLES(delay_us);
    handle->is_active = true;
    handle->is_completed = false;
}

/**
  * @brief  检查非阻塞延时是否完成
  * @param  handle: 非阻塞延时句柄
  * @retval true: 延时完成, false: 延时未完成
  */
bool SysTick_NonBlocking_IsCompleted(SysTick_NonBlocking_t* handle)
{
    if (handle == NULL || !handle->is_active) {
        return true;
    }
    
    uint32_t current_time = DWT->CYCCNT;
    
    if ((current_time - handle->start_time) >= handle->delay_time) {
        handle->is_completed = true;
        handle->is_active = false;
        return true;
    }
    
    return false;
}

/**
  * @brief  获取高精度时间戳(微秒)
  * @param  None
  * @retval 当前时间戳(微秒)
  */
uint64_t SysTick_GetTimestamp_us(void)
{
    uint64_t ms_part = (uint64_t)g_system_uptime_ms * 1000;
    uint32_t us_part = (DWT->CYCCNT % (DWT_CYCCNT_FREQUENCY_HZ / 1000)) / 
                       (DWT_CYCCNT_FREQUENCY_HZ / 1000000);
    
    return ms_part + us_part;
}

/**
  * @brief  获取系统运行时间(毫秒)
  * @param  None
  * @retval 系统运行时间(毫秒)
  */
uint32_t SysTick_GetUptime_ms(void)
{
    return g_system_uptime_ms;
}

/**
  * @brief  获取系统滴答计数
  * @param  None
  * @retval 当前滴答计数
  */
uint32_t SysTick_GetTick(void)
{
    return g_systick_counter;
}

/**
  * @brief  延时校准
  * @param  reference_delay_us: 参考延时时间(微秒)
  * @retval 校准因子
  */
float SysTick_Calibrate(uint32_t reference_delay_us)
{
    if (reference_delay_us == 0) {
        return g_systick_cal.calibration_factor;
    }

    // 测量实际延时时间
    uint32_t start_time = DWT->CYCCNT;
    Delay_us(reference_delay_us);
    uint32_t end_time = DWT->CYCCNT;

    // 计算实际延时时间(微秒)
    uint32_t actual_delay_us = (end_time - start_time) / (DWT_CYCCNT_FREQUENCY_HZ / 1000000);

    // 计算校准因子
    if (actual_delay_us > 0) {
        g_systick_cal.calibration_factor = (float)reference_delay_us / actual_delay_us;
        g_systick_cal.last_calibration = g_system_uptime_ms;
    }

    return g_systick_cal.calibration_factor;
}

/**
  * @brief  设置温度补偿
  * @param  temperature: 当前温度(°C * 10)
  * @retval None
  */
void SysTick_SetTemperatureCompensation(int16_t temperature)
{
    // 简化的温度补偿算法：每10°C补偿1us/ms
    // 基准温度：25°C (250)
    int16_t temp_diff = temperature - 250;
    g_systick_cal.temperature_offset = temp_diff / 100; // 每10°C补偿1us
}

/**
  * @brief  获取系统时间统计
  * @param  stats: 统计数据结构体指针
  * @retval None
  */
void SysTick_GetStats(SysTick_Stats_t* stats)
{
    if (stats == NULL) {
        return;
    }

    *stats = g_systick_stats;
}

/**
  * @brief  重置系统时间统计
  * @param  None
  * @retval None
  */
void SysTick_ResetStats(void)
{
    g_systick_stats.system_uptime_ms = 0;
    g_systick_stats.total_delay_us = 0;
    g_systick_stats.delay_call_count = 0;
    g_systick_stats.cpu_usage_percent = 0.0f;
}

/**
  * @brief  SysTick中断处理函数内部逻辑
  * @param  None
  * @retval None
  * @note   由stm32f4xx_it.c中的SysTick_Handler调用
  */
void SysTick_Handler_Internal(void)
{
    // 更新系统计数器
    g_systick_counter++;
    g_system_uptime_ms++;

    // 更新延时计数器
    if (s_delay_counter > 0) {
        s_delay_counter--;
    }

    // 定期自动校准
    if ((g_system_uptime_ms % SYSTICK_CALIBRATION_INTERVAL_MS) == 0) {
        // 可以在这里触发自动校准
        // SysTick_Calibrate(1000); // 使用1ms作为参考
    }
}

/************************ (C) COPYRIGHT 嵌入式竞赛团队 *****END OF FILE****/
