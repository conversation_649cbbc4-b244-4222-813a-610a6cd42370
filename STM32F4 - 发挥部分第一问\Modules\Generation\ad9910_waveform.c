/**
  ******************************************************************************
  * @file    ad9910_waveform.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   AD9910波形控制层实现文件 - 高级功能接口
  ******************************************************************************
  * @attention
  *
  * 本文件实现AD9910 DDS芯片的高级波形控制功能
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "ad9910_waveform.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/

/* AD9910寄存器配置数据 */
static const uint8_t cfr1_data[] = {0x00, 0x40, 0x00, 0x00};  ///< CFR1配置
static const uint8_t cfr2_data[] = {0x01, 0x00, 0x00, 0x00};  ///< CFR2配置
static const uint8_t cfr3_data[] = {0x05, 0x0F, 0x41, 0x32};  ///< CFR3配置 (40M晶振, 25倍频)

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
static AD9910_Config_t current_config;  ///< 当前配置
static uint8_t profile_data[8] = {0x3f, 0xff, 0x00, 0x00, 0x25, 0x09, 0x7b, 0x42}; ///< Profile数据

/* Private function prototypes -----------------------------------------------*/
static void AD9910_WriteProfile(void);
static uint32_t AD9910_CalculateFrequencyWord(uint32_t frequency_hz);
static uint16_t AD9910_CalculateAmplitudeWord(uint16_t amplitude_mv);

/* Private functions ---------------------------------------------------------*/

/**
 * @brief  AD9910初始化
 * @param  None
 * @retval None
 */
void AD9910_Init(void)
{
    uint8_t i;

    /* 硬件初始化 */
    AD9910_HAL_Init();
    
    /* 电源控制 */
    AD9910_HAL_PowerControl(1);  // 使能电源
    
    /* Profile选择 */
    AD9910_HAL_SelectProfile(0); // 选择Profile 0
    
    /* 硬件复位 */
    AD9910_HAL_Reset();
    
    /* 写入CFR1寄存器 */
    AD9910_CSB_LOW();
    AD9910_HAL_Send8Bits(AD9910_REG_CFR1);
    for (i = 0; i < 4; i++) {
        AD9910_HAL_Send8Bits(cfr1_data[i]);
    }
    AD9910_CSB_HIGH();
    AD9910_HAL_DelayUs(10);
    
    /* 写入CFR2寄存器 */
    AD9910_CSB_LOW();
    AD9910_HAL_Send8Bits(AD9910_REG_CFR2);
    for (i = 0; i < 4; i++) {
        AD9910_HAL_Send8Bits(cfr2_data[i]);
    }
    AD9910_CSB_HIGH();
    AD9910_HAL_DelayUs(10);
    
    /* 写入CFR3寄存器 */
    AD9910_CSB_LOW();
    AD9910_HAL_Send8Bits(AD9910_REG_CFR3);
    for (i = 0; i < 4; i++) {
        AD9910_HAL_Send8Bits(cfr3_data[i]);
    }
    AD9910_CSB_HIGH();
    AD9910_HAL_DelayUs(10);
    
    /* I/O更新 */
    AD9910_HAL_IOUpdate();
    AD9910_HAL_DelayMs(1);
    
    /* 设置默认配置 */
    current_config.frequency_hz = AD9910_DEFAULT_FREQUENCY;
    current_config.amplitude_mv = AD9910_DEFAULT_AMPLITUDE;
    current_config.phase_deg = AD9910_DEFAULT_PHASE;
    current_config.wave_type = AD9910_WAVE_SINE;
    current_config.profile = AD9910_DEFAULT_PROFILE;
}

/**
 * @brief  设置AD9910输出频率
 * @param  frequency_hz: 频率值 (1Hz - 420MHz)
 * @retval None
 */
void AD9910_SetFrequency(uint32_t frequency_hz)
{
    uint32_t freq_word;
    
    /* 参数检查 */
    if (frequency_hz < AD9910_MIN_FREQ_HZ) {
        frequency_hz = AD9910_MIN_FREQ_HZ;
    }
    if (frequency_hz > AD9910_MAX_FREQ_HZ) {
        frequency_hz = AD9910_MAX_FREQ_HZ;
    }
    
    /* 计算频率控制字 */
    freq_word = AD9910_CalculateFrequencyWord(frequency_hz);
    
    /* 更新Profile数据 */
    profile_data[7] = (uint8_t)(freq_word & 0xFF);
    profile_data[6] = (uint8_t)((freq_word >> 8) & 0xFF);
    profile_data[5] = (uint8_t)((freq_word >> 16) & 0xFF);
    profile_data[4] = (uint8_t)((freq_word >> 24) & 0xFF);
    
    /* 写入Profile寄存器 */
    AD9910_WriteProfile();
    
    /* 更新当前配置 */
    current_config.frequency_hz = frequency_hz;
}

/**
 * @brief  设置AD9910输出幅度
 * @param  amplitude_mv: 幅度值 (0 - 800mV峰峰值)
 * @retval None
 */
void AD9910_SetAmplitude(uint16_t amplitude_mv)
{
    uint16_t amp_word;
    
    /* 参数检查 */
    if (amplitude_mv > AD9910_MAX_AMPLITUDE_MV) {
        amplitude_mv = AD9910_MAX_AMPLITUDE_MV;
    }
    
    /* 计算幅度控制字 */
    amp_word = AD9910_CalculateAmplitudeWord(amplitude_mv);
    
    /* 更新Profile数据 */
    profile_data[0] = (uint8_t)((amp_word >> 8) & 0x3F);  // 高6位
    profile_data[1] = (uint8_t)(amp_word & 0xFF);         // 低8位
    
    /* 写入Profile寄存器 */
    AD9910_WriteProfile();
    
    /* 更新当前配置 */
    current_config.amplitude_mv = amplitude_mv;
}

/**
 * @brief  设置AD9910输出相位
 * @param  phase_deg: 相位值 (0 - 359度)
 * @retval None
 */
void AD9910_SetPhase(uint16_t phase_deg)
{
    uint16_t phase_word;
    
    /* 参数检查 */
    if (phase_deg >= 360) {
        phase_deg = phase_deg % 360;
    }
    
    /* 计算相位控制字 (16位) */
    phase_word = (uint16_t)((uint32_t)phase_deg * 65536 / 360);
    
    /* 更新Profile数据 */
    profile_data[2] = (uint8_t)((phase_word >> 8) & 0xFF);
    profile_data[3] = (uint8_t)(phase_word & 0xFF);
    
    /* 写入Profile寄存器 */
    AD9910_WriteProfile();
    
    /* 更新当前配置 */
    current_config.phase_deg = phase_deg;
}

/**
 * @brief  设置AD9910波形类型
 * @param  wave_type: 波形类型
 * @retval None
 */
void AD9910_SetWaveType(AD9910_WaveType_t wave_type)
{
    /* 更新当前配置 */
    current_config.wave_type = wave_type;
    
    /* 注意：AD9910主要输出正弦波，其他波形需要特殊配置 */
    /* 这里保持正弦波输出，实际项目中可根据需要扩展 */
}

/**
 * @brief  使能AD9910输出
 * @param  None
 * @retval None
 */
void AD9910_EnableOutput(void)
{
    /* 通过OSK引脚控制输出使能 */
    AD9910_OSK_LOW();  // 低电平使能输出
}

/**
 * @brief  禁用AD9910输出
 * @param  None
 * @retval None
 */
void AD9910_DisableOutput(void)
{
    /* 通过OSK引脚控制输出禁用 */
    AD9910_OSK_HIGH(); // 高电平禁用输出
}

/**
 * @brief  配置AD9910完整参数
 * @param  config: 配置结构体指针
 * @retval None
 */
void AD9910_Configure(const AD9910_Config_t* config)
{
    if (config == NULL) return;
    
    AD9910_SetFrequency(config->frequency_hz);
    AD9910_SetAmplitude(config->amplitude_mv);
    AD9910_SetPhase(config->phase_deg);
    AD9910_SetWaveType(config->wave_type);
    AD9910_HAL_SelectProfile(config->profile);
}

/**
 * @brief  写入Profile寄存器
 * @param  None
 * @retval None
 */
static void AD9910_WriteProfile(void)
{
    uint8_t i;
    
    AD9910_CSB_LOW();
    AD9910_HAL_Send8Bits(AD9910_REG_PROFILE0);  // 写入Profile 0
    for (i = 0; i < 8; i++) {
        AD9910_HAL_Send8Bits(profile_data[i]);
    }
    AD9910_CSB_HIGH();
    
    /* I/O更新 */
    AD9910_HAL_IOUpdate();
}

/**
 * @brief  计算频率控制字
 * @param  frequency_hz: 频率值
 * @retval 频率控制字
 */
static uint32_t AD9910_CalculateFrequencyWord(uint32_t frequency_hz)
{
    /* 频率控制字 = (目标频率 * 2^32) / 系统时钟频率 */
    /* 使用64位运算避免溢出 */
    uint64_t temp = (uint64_t)frequency_hz * 4294967296ULL;  // 2^32
    return (uint32_t)(temp / AD9910_SYSTEM_CLOCK);
}

/**
 * @brief  计算幅度控制字
 * @param  amplitude_mv: 幅度值 (mV)
 * @retval 幅度控制字
 */
static uint16_t AD9910_CalculateAmplitudeWord(uint16_t amplitude_mv)
{
    /* 幅度控制字 = (目标幅度 * 16383) / 最大幅度 */
    uint32_t temp = (uint32_t)amplitude_mv * AD9910_AMPLITUDE_STEPS;
    return (uint16_t)(temp / AD9910_MAX_AMPLITUDE_MV);
}

/**
 * @brief  写入AD9910寄存器
 * @param  reg_addr: 寄存器地址
 * @param  data: 数据指针
 * @param  length: 数据长度
 * @retval None
 */
void AD9910_WriteRegister(uint8_t reg_addr, const uint8_t* data, uint8_t length)
{
    uint8_t i;

    if (data == NULL || length == 0) return;

    AD9910_CSB_LOW();
    AD9910_HAL_Send8Bits(reg_addr);
    for (i = 0; i < length; i++) {
        AD9910_HAL_Send8Bits(data[i]);
    }
    AD9910_CSB_HIGH();
    AD9910_HAL_DelayUs(10);
}

/**
 * @brief  读取AD9910寄存器
 * @param  reg_addr: 寄存器地址
 * @param  data: 数据缓冲区指针
 * @param  length: 数据长度
 * @retval None
 */
void AD9910_ReadRegister(uint8_t reg_addr, uint8_t* data, uint8_t length)
{
    /* AD9910读取功能需要特殊的SPI配置，这里提供基本框架 */
    /* 实际实现需要根据具体需求和硬件配置调整 */
    (void)reg_addr;
    (void)data;
    (void)length;
}

/**
 * @brief  AD9910软件复位
 * @param  None
 * @retval None
 */
void AD9910_SoftwareReset(void)
{
    /* 通过硬件复位引脚实现复位 */
    AD9910_HAL_Reset();

    /* 重新初始化 */
    AD9910_Init();
}

/**
 * @brief  获取AD9910当前配置
 * @param  config: 配置结构体指针
 * @retval None
 */
void AD9910_GetConfiguration(AD9910_Config_t* config)
{
    if (config != NULL) {
        *config = current_config;
    }
}

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
