/**
  ******************************************************************************
  * @file    command_parser.h
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   外部控制命令解析器头文件
  ******************************************************************************
  * @attention
  *
  * 本文件实现外部设备控制命令的解析和处理，支持：
  * - 串口屏命令解析 (文本协议)
  * - 4x4矩阵键盘命令解析 (按键序列)
  * - 统一的命令格式和响应
  * - 错误处理和状态反馈
  *
  * 命令格式：
  * 串口屏: "SET_FREQ:5000000\r\n" "SET_AMP:500\r\n" "GET_STATUS\r\n"
  * 键盘: 按键序列转换为命令
  *
  ******************************************************************************
  */

#ifndef __COMMAND_PARSER_H
#define __COMMAND_PARSER_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "ad9910_control.h"
#include <stdint.h>
#include <stdbool.h>

/* Exported types ------------------------------------------------------------*/

/**
 * @brief  命令来源类型
 */
typedef enum {
    CMD_SOURCE_UART = 0,       ///< 串口屏
    CMD_SOURCE_KEYPAD,         ///< 4x4矩阵键盘
    CMD_SOURCE_UNKNOWN         ///< 未知来源
} CommandSource_t;

/**
 * @brief  解析状态枚举
 */
typedef enum {
    PARSE_STATUS_OK = 0,       ///< 解析成功
    PARSE_STATUS_INCOMPLETE,   ///< 命令不完整
    PARSE_STATUS_INVALID,      ///< 命令无效
    PARSE_STATUS_OVERFLOW,     ///< 缓冲区溢出
    PARSE_STATUS_CHECKSUM_ERROR ///< 校验错误
} ParseStatus_t;

/**
 * @brief  命令结构体
 */
typedef struct {
    ControlCommand_t command;  ///< 控制命令
    uint32_t param1;          ///< 参数1
    uint32_t param2;          ///< 参数2
    CommandSource_t source;   ///< 命令来源
    uint32_t timestamp;       ///< 时间戳
} ParsedCommand_t;

/**
 * @brief  响应结构体
 */
typedef struct {
    ControlStatus_t status;   ///< 执行状态
    char message[64];         ///< 响应消息
    uint32_t data[4];         ///< 响应数据
    uint8_t data_count;       ///< 数据数量
} CommandResponse_t;

/* Exported constants --------------------------------------------------------*/

/* 命令缓冲区大小 */
#define CMD_BUFFER_SIZE         128
#define CMD_MAX_PARAMS          8
#define CMD_RESPONSE_SIZE       256

/* 串口命令分隔符 */
#define CMD_DELIMITER           '\r'
#define CMD_PARAM_SEPARATOR     ':'
#define CMD_END_CHAR            '\n'

/* 键盘命令定义 */
#define KEY_FREQ_UP             0x01
#define KEY_FREQ_DOWN           0x02
#define KEY_AMP_UP              0x03
#define KEY_AMP_DOWN            0x04
#define KEY_PRESET_1            0x05
#define KEY_PRESET_2            0x06
#define KEY_PRESET_3            0x07
#define KEY_PRESET_4            0x08
#define KEY_OUTPUT_TOGGLE       0x09
#define KEY_ENTER               0x0A
#define KEY_ESCAPE              0x0B
#define KEY_MENU                0x0C

/* Exported functions --------------------------------------------------------*/

/**
 * @brief  命令解析器初始化
 * @param  None
 * @retval None
 */
void CommandParser_Init(void);

/**
 * @brief  解析串口命令
 * @param  uart_data: 串口接收数据
 * @param  length: 数据长度
 * @param  command: 解析结果输出
 * @retval ParseStatus_t 解析状态
 */
ParseStatus_t CommandParser_ParseUART(const uint8_t* uart_data, uint16_t length, ParsedCommand_t* command);

/**
 * @brief  解析键盘命令
 * @param  key_code: 按键代码
 * @param  command: 解析结果输出
 * @retval ParseStatus_t 解析状态
 */
ParseStatus_t CommandParser_ParseKeypad(uint8_t key_code, ParsedCommand_t* command);

/**
 * @brief  生成命令响应
 * @param  command: 原始命令
 * @param  status: 执行状态
 * @param  response: 响应结构体输出
 * @retval None
 */
void CommandParser_GenerateResponse(const ParsedCommand_t* command, ControlStatus_t status, CommandResponse_t* response);

/**
 * @brief  格式化响应为串口输出
 * @param  response: 响应结构体
 * @param  output_buffer: 输出缓冲区
 * @param  buffer_size: 缓冲区大小
 * @retval 实际输出长度
 */
uint16_t CommandParser_FormatUARTResponse(const CommandResponse_t* response, char* output_buffer, uint16_t buffer_size);

/**
 * @brief  格式化响应为键盘显示
 * @param  response: 响应结构体
 * @param  display_buffer: 显示缓冲区
 * @param  buffer_size: 缓冲区大小
 * @retval 实际输出长度
 */
uint16_t CommandParser_FormatKeypadResponse(const CommandResponse_t* response, char* display_buffer, uint16_t buffer_size);

/**
 * @brief  添加串口数据到解析缓冲区
 * @param  data: 接收数据
 * @param  length: 数据长度
 * @retval None
 */
void CommandParser_AddUARTData(const uint8_t* data, uint16_t length);

/**
 * @brief  处理待解析的串口命令
 * @param  command: 解析结果输出
 * @retval ParseStatus_t 解析状态
 */
ParseStatus_t CommandParser_ProcessPendingUART(ParsedCommand_t* command);

/**
 * @brief  清空解析缓冲区
 * @param  None
 * @retval None
 */
void CommandParser_ClearBuffer(void);

/* ==================== 串口命令格式定义 ==================== */

/*
 * 串口命令格式说明：
 * 
 * 1. 设置频率: "SET_FREQ:5000000\r\n"
 *    - 参数: 频率值(Hz)
 *    - 响应: "OK:FREQ_SET:5000000\r\n" 或 "ERROR:OUT_OF_RANGE\r\n"
 * 
 * 2. 设置峰峰值: "SET_AMP:500\r\n"
 *    - 参数: 峰峰值(mV)
 *    - 响应: "OK:AMP_SET:500\r\n" 或 "ERROR:OUT_OF_RANGE\r\n"
 * 
 * 3. 设置增益: "SET_GAIN:1.5\r\n"
 *    - 参数: 增益系数
 *    - 响应: "OK:GAIN_SET:1.5\r\n"
 * 
 * 4. 使能输出: "ENABLE_OUT\r\n"
 *    - 响应: "OK:OUTPUT_ENABLED\r\n"
 * 
 * 5. 禁用输出: "DISABLE_OUT\r\n"
 *    - 响应: "OK:OUTPUT_DISABLED\r\n"
 * 
 * 6. 获取状态: "GET_STATUS\r\n"
 *    - 响应: "STATUS:FREQ:5000000:AMP:500:GAIN:1.0:OUT:1\r\n"
 * 
 * 7. 加载预设: "LOAD_PRESET:0\r\n"
 *    - 参数: 预设编号(0-7)
 *    - 响应: "OK:PRESET_LOADED:0\r\n"
 * 
 * 8. 获取参数范围: "GET_RANGES\r\n"
 *    - 响应: "RANGES:FREQ:1:420000000:AMP:10:5000\r\n"
 */

/* ==================== 键盘操作定义 ==================== */

/*
 * 4x4矩阵键盘布局：
 * 
 * [1] [2] [3] [A]  ->  [FREQ+] [AMP+]  [PRESET1] [MENU]
 * [4] [5] [6] [B]  ->  [FREQ-] [AMP-]  [PRESET2] [ENTER]
 * [7] [8] [9] [C]  ->  [GAIN+] [GAIN-] [PRESET3] [ESC]
 * [*] [0] [#] [D]  ->  [OUT]   [RESET] [PRESET4] [INFO]
 * 
 * 操作说明：
 * - FREQ+/FREQ-: 频率步进调节
 * - AMP+/AMP-: 幅度步进调节
 * - GAIN+/GAIN-: 增益步进调节
 * - PRESET1-4: 加载预设参数
 * - OUT: 输出开关切换
 * - RESET: 系统复位
 * - MENU: 进入菜单模式
 * - ENTER: 确认操作
 * - ESC: 取消操作
 * - INFO: 显示当前状态
 */

#ifdef __cplusplus
}
#endif

#endif /* __COMMAND_PARSER_H */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
