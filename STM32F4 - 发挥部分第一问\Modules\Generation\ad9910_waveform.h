/**
  ******************************************************************************
  * @file    ad9910_waveform.h
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   AD9910波形控制层头文件 - 高级功能接口
  ******************************************************************************
  * @attention
  *
  * 本文件实现AD9910 DDS芯片的高级波形控制功能，包括：
  * - 频率设置和控制
  * - 幅度设置和控制
  * - 波形类型选择
  * - 相位控制
  * - 寄存器配置
  *
  * 技术规格：
  * - 频率范围：1Hz - 420MHz
  * - 频率分辨率：0.23Hz
  * - 幅度范围：0 - 800mV峰峰值
  * - 幅度分辨率：0.049mV/步
  * - 波形类型：正弦波、三角波、方波、SINC波
  *
  ******************************************************************************
  */

#ifndef __AD9910_WAVEFORM_H
#define __AD9910_WAVEFORM_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "ad9910_hal.h"

/* Exported types ------------------------------------------------------------*/

/**
 * @brief  AD9910波形类型枚举
 */
typedef enum {
    AD9910_WAVE_SINE = 0,      ///< 正弦波
    AD9910_WAVE_TRIANGLE,      ///< 三角波
    AD9910_WAVE_SQUARE,        ///< 方波
    AD9910_WAVE_SINC           ///< SINC波
} AD9910_WaveType_t;

/**
 * @brief  AD9910配置结构体
 */
typedef struct {
    uint32_t frequency_hz;     ///< 输出频率 (Hz)
    uint16_t amplitude_mv;     ///< 输出幅度 (mV峰峰值)
    uint16_t phase_deg;        ///< 相位 (度)
    AD9910_WaveType_t wave_type; ///< 波形类型
    uint8_t profile;           ///< Profile编号 (0-7)
} AD9910_Config_t;

/* Exported constants --------------------------------------------------------*/

/* AD9910寄存器地址定义 */
#define AD9910_REG_CFR1             0x00    ///< 控制功能寄存器1
#define AD9910_REG_CFR2             0x01    ///< 控制功能寄存器2
#define AD9910_REG_CFR3             0x02    ///< 控制功能寄存器3
#define AD9910_REG_AUX_DAC          0x03    ///< 辅助DAC控制
#define AD9910_REG_IO_UPDATE        0x04    ///< I/O更新速率
#define AD9910_REG_FTW              0x07    ///< 频率调谐字
#define AD9910_REG_POW              0x08    ///< 相位偏移字
#define AD9910_REG_ASF              0x09    ///< 幅度比例因子
#define AD9910_REG_MULTICHIP        0x0A    ///< 多芯片同步
#define AD9910_REG_DIGITAL_RAMP     0x0B    ///< 数字斜坡限制
#define AD9910_REG_DIGITAL_RAMP_STEP_SIZE 0x0C ///< 数字斜坡步长
#define AD9910_REG_DIGITAL_RAMP_RATE 0x0D   ///< 数字斜坡速率
#define AD9910_REG_PROFILE0         0x0E    ///< Profile 0
#define AD9910_REG_PROFILE1         0x0F    ///< Profile 1
#define AD9910_REG_PROFILE2         0x10    ///< Profile 2
#define AD9910_REG_PROFILE3         0x11    ///< Profile 3
#define AD9910_REG_PROFILE4         0x12    ///< Profile 4
#define AD9910_REG_PROFILE5         0x13    ///< Profile 5
#define AD9910_REG_PROFILE6         0x14    ///< Profile 6
#define AD9910_REG_PROFILE7         0x15    ///< Profile 7

/* AD9910控制寄存器位定义 */
#define AD9910_CFR1_RAM_ENABLE      (1 << 31)  ///< RAM使能
#define AD9910_CFR1_MANUAL_OSK      (1 << 23)  ///< 手动OSK
#define AD9910_CFR1_INVERSE_SINC    (1 << 22)  ///< 反SINC滤波器
#define AD9910_CFR1_OSK_ENABLE      (1 << 21)  ///< OSK使能
#define AD9910_CFR1_LOAD_LRR        (1 << 20)  ///< 加载LRR
#define AD9910_CFR1_AUTOCLEAR_DIG_RAMP (1 << 19) ///< 自动清除数字斜坡
#define AD9910_CFR1_AUTOCLEAR_PHASE (1 << 18)  ///< 自动清除相位累加器
#define AD9910_CFR1_CLEAR_DIG_RAMP  (1 << 17)  ///< 清除数字斜坡累加器
#define AD9910_CFR1_CLEAR_PHASE     (1 << 16)  ///< 清除相位累加器
#define AD9910_CFR1_SINE_OUTPUT     (0 << 14)  ///< 正弦波输出
#define AD9910_CFR1_COSINE_OUTPUT   (1 << 14)  ///< 余弦波输出
#define AD9910_CFR1_SELECT_DDS_SINE_OUTPUT (0 << 11) ///< 选择DDS正弦输出

/* 默认配置值 */
#define AD9910_DEFAULT_FREQUENCY    1000000     ///< 默认频率 1MHz
#define AD9910_DEFAULT_AMPLITUDE    400         ///< 默认幅度 400mV
#define AD9910_DEFAULT_PHASE        0           ///< 默认相位 0度
#define AD9910_DEFAULT_PROFILE      0           ///< 默认Profile 0

/* Exported functions --------------------------------------------------------*/

/**
 * @brief  AD9910初始化
 * @param  None
 * @retval None
 */
void AD9910_Init(void);

/**
 * @brief  设置AD9910输出频率
 * @param  frequency_hz: 频率值 (1Hz - 420MHz)
 * @retval None
 */
void AD9910_SetFrequency(uint32_t frequency_hz);

/**
 * @brief  设置AD9910输出幅度
 * @param  amplitude_mv: 幅度值 (0 - 800mV峰峰值)
 * @retval None
 */
void AD9910_SetAmplitude(uint16_t amplitude_mv);

/**
 * @brief  设置AD9910输出相位
 * @param  phase_deg: 相位值 (0 - 359度)
 * @retval None
 */
void AD9910_SetPhase(uint16_t phase_deg);

/**
 * @brief  设置AD9910波形类型
 * @param  wave_type: 波形类型
 * @retval None
 */
void AD9910_SetWaveType(AD9910_WaveType_t wave_type);

/**
 * @brief  使能AD9910输出
 * @param  None
 * @retval None
 */
void AD9910_EnableOutput(void);

/**
 * @brief  禁用AD9910输出
 * @param  None
 * @retval None
 */
void AD9910_DisableOutput(void);

/**
 * @brief  配置AD9910完整参数
 * @param  config: 配置结构体指针
 * @retval None
 */
void AD9910_Configure(const AD9910_Config_t* config);

/**
 * @brief  写入AD9910寄存器
 * @param  reg_addr: 寄存器地址
 * @param  data: 数据指针
 * @param  length: 数据长度
 * @retval None
 */
void AD9910_WriteRegister(uint8_t reg_addr, const uint8_t* data, uint8_t length);

/**
 * @brief  读取AD9910寄存器
 * @param  reg_addr: 寄存器地址
 * @param  data: 数据缓冲区指针
 * @param  length: 数据长度
 * @retval None
 */
void AD9910_ReadRegister(uint8_t reg_addr, uint8_t* data, uint8_t length);

/**
 * @brief  AD9910软件复位
 * @param  None
 * @retval None
 */
void AD9910_SoftwareReset(void);

/**
 * @brief  获取AD9910当前配置
 * @param  config: 配置结构体指针
 * @retval None
 */
void AD9910_GetConfiguration(AD9910_Config_t* config);

#ifdef __cplusplus
}
#endif

#endif /* __AD9910_WAVEFORM_H */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
