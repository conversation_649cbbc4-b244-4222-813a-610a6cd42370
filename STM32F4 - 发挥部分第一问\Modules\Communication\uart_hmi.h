/**
  ******************************************************************************
  * @file    uart_hmi.h
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   串口屏(HMI)通信模块头文件
  ******************************************************************************
  * @attention
  *
  * 本文件实现与串口屏的通信功能，包括：
  * - 串口初始化和配置
  * - HMI数据发送函数
  * - 串口接收中断处理
  * - 命令解析和响应
  *
  * 硬件连接：
  * PA9  -> HMI_TX (串口屏RX)
  * PA10 -> HMI_RX (串口屏TX)
  * 波特率: 9600
  *
  ******************************************************************************
  */

#ifndef __UART_HMI_H
#define __UART_HMI_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include <stdint.h>
#include <stdbool.h>

/* Exported types ------------------------------------------------------------*/

/**
 * @brief  串口接收状态枚举
 */
typedef enum {
    UART_RX_IDLE = 0,          ///< 空闲状态
    UART_RX_RECEIVING,         ///< 正在接收
    UART_RX_COMPLETE,          ///< 接收完成
    UART_RX_ERROR              ///< 接收错误
} UartRxStatus_t;

/**
 * @brief  HMI控件类型枚举
 */
typedef enum {
    HMI_TEXT = 0,              ///< 文本控件
    HMI_NUMBER,                ///< 数字控件
    HMI_PROGRESS,              ///< 进度条控件
    HMI_BUTTON,                ///< 按钮控件
    HMI_SLIDER                 ///< 滑块控件
} HmiControlType_t;

/* Exported constants --------------------------------------------------------*/

/* 串口配置参数 */
#define HMI_USART                   USART1
#define HMI_USART_RCC               RCC_APB2Periph_USART1
#define HMI_USART_TX_RCC            RCC_AHB1Periph_GPIOA
#define HMI_USART_RX_RCC            RCC_AHB1Periph_GPIOA

#define HMI_USART_TX_PORT           GPIOA
#define HMI_USART_TX_PIN            GPIO_Pin_9
#define HMI_USART_RX_PORT           GPIOA
#define HMI_USART_RX_PIN            GPIO_Pin_10
#define HMI_USART_AF                GPIO_AF_USART1
#define HMI_USART_TX_AF_PIN         GPIO_PinSource9
#define HMI_USART_RX_AF_PIN         GPIO_PinSource10

/* 接收缓冲区配置 */
#define HMI_RX_BUFFER_SIZE          200     ///< 接收缓冲区大小
#define HMI_CMD_END_CHAR1           0x0D    ///< 命令结束符1 (\r)
#define HMI_CMD_END_CHAR2           0x0A    ///< 命令结束符2 (\n)

/* HMI协议配置 */
#define HMI_CMD_SUFFIX              "\xff\xff\xff"  ///< HMI命令后缀
#define HMI_BAUD_RATE               9600    ///< 波特率

/* Exported functions --------------------------------------------------------*/

/**
 * @brief  HMI串口初始化
 * @param  baud_rate: 波特率
 * @retval None
 */
void HMI_UART_Init(uint32_t baud_rate);

/**
 * @brief  发送单个字符
 * @param  ch: 要发送的字符
 * @retval None
 */
void HMI_SendChar(uint8_t ch);

/**
 * @brief  发送字符串
 * @param  str: 要发送的字符串
 * @retval None
 */
void HMI_SendString(uint8_t *str);

/**
 * @brief  发送字符串到HMI文本控件
 * @param  control_name: 控件名称
 * @param  text: 要显示的文本
 * @retval None
 */
void HMI_SendText(const char* control_name, const char* text);

/**
 * @brief  发送数字到HMI数字控件
 * @param  control_name: 控件名称
 * @param  number: 要显示的数字
 * @retval None
 */
void HMI_SendNumber(const char* control_name, int32_t number);

/**
 * @brief  发送浮点数到HMI控件
 * @param  control_name: 控件名称
 * @param  value: 要显示的浮点数
 * @param  decimal_places: 小数位数
 * @retval None
 */
void HMI_SendFloat(const char* control_name, float value, uint8_t decimal_places);

/**
 * @brief  发送进度条值
 * @param  control_name: 进度条控件名称
 * @param  value: 进度值 (0-100)
 * @retval None
 */
void HMI_SendProgress(const char* control_name, uint8_t value);

/**
 * @brief  设置控件可见性
 * @param  control_name: 控件名称
 * @param  visible: true-可见, false-隐藏
 * @retval None
 */
void HMI_SetVisible(const char* control_name, bool visible);

/**
 * @brief  设置控件使能状态
 * @param  control_name: 控件名称
 * @param  enabled: true-使能, false-禁用
 * @retval None
 */
void HMI_SetEnabled(const char* control_name, bool enabled);

/**
 * @brief  获取接收缓冲区数据
 * @param  buffer: 数据缓冲区
 * @param  max_length: 最大长度
 * @retval 实际接收的数据长度
 */
uint16_t HMI_GetReceivedData(uint8_t* buffer, uint16_t max_length);

/**
 * @brief  检查是否有完整命令接收
 * @retval true-有完整命令, false-无完整命令
 */
bool HMI_HasCompleteCommand(void);

/**
 * @brief  清空接收缓冲区
 * @retval None
 */
void HMI_ClearReceiveBuffer(void);

/**
 * @brief  获取接收状态
 * @retval UartRxStatus_t 接收状态
 */
UartRxStatus_t HMI_GetReceiveStatus(void);

/* ==================== AD9910专用HMI函数 ==================== */

/**
 * @brief  显示AD9910频率
 * @param  frequency_hz: 频率值 (Hz)
 * @retval None
 */
void HMI_DisplayFrequency(uint32_t frequency_hz);

/**
 * @brief  显示AD9910幅度
 * @param  amplitude_mv: 幅度值 (mV)
 * @retval None
 */
void HMI_DisplayAmplitude(uint16_t amplitude_mv);

/**
 * @brief  显示增益系数
 * @param  gain: 增益值
 * @retval None
 */
void HMI_DisplayGain(double gain);

/**
 * @brief  显示输出状态
 * @param  enabled: 输出状态
 * @retval None
 */
void HMI_DisplayOutputStatus(bool enabled);

/**
 * @brief  显示系统状态
 * @param  status_text: 状态文本
 * @retval None
 */
void HMI_DisplaySystemStatus(const char* status_text);

/**
 * @brief  显示错误信息
 * @param  error_text: 错误文本
 * @retval None
 */
void HMI_DisplayError(const char* error_text);

/**
 * @brief  更新所有AD9910参数显示
 * @retval None
 */
void HMI_UpdateAllParameters(void);

/**
 * @brief  显示欢迎界面
 * @retval None
 */
void HMI_ShowWelcomeScreen(void);

#ifdef __cplusplus
}
#endif

#endif /* __UART_HMI_H */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
