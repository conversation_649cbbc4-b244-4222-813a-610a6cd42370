/**
  ******************************************************************************
  * @file    keypad_4x4.h
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   4x4矩阵键盘驱动头文件 - 电赛G题专用
  ******************************************************************************
  * @attention
  *
  * 本文件实现4x4矩阵键盘的驱动功能，专为电赛G题信号发生器设计
  *
  * 电赛G题要求:
  * - 频率步长: 100Hz
  * - 频率范围: 最高不低于1MHz
  * - 电压步长: 0.1V
  *
  * 硬件连接 (低电平导通):
  * 行线 (输出): PB0, PB1, PB2, PB3
  * 列线 (输入): PB4, PB5, PB6, PB7
  *
  * 按键布局 (符合电赛G题要求):
  * [100Hz] [1kHz]  [10kHz]  [FREQ+]
  * [100kHz][500kHz][1MHz]   [FREQ-]
  * [0.1V]  [0.5V]  [1.0V]   [VOLT+]
  * [OUT]   [RESET] [SAVE]   [VOLT-]
  *
  ******************************************************************************
  */

#ifndef __KEYPAD_4X4_H
#define __KEYPAD_4X4_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include <stdint.h>
#include <stdbool.h>

/* Exported types ------------------------------------------------------------*/

/**
 * @brief  按键值枚举 (电赛G题专用布局)
 */
typedef enum {
    KEY_NONE = 0,              ///< 无按键

    /* 第一行 - 低频预设 + 频率增加 */
    KEY_100HZ = 1,             ///< 按键1 (100Hz)
    KEY_1KHZ = 2,              ///< 按键2 (1kHz)
    KEY_10KHZ = 3,             ///< 按键3 (10kHz)
    KEY_FREQ_UP = 4,           ///< 按键A (FREQ+ 频率增加100Hz)

    /* 第二行 - 高频预设 + 频率减少 */
    KEY_100KHZ = 5,            ///< 按键4 (100kHz)
    KEY_500KHZ = 6,            ///< 按键5 (500kHz)
    KEY_1MHZ = 7,              ///< 按键6 (1MHz - 满足电赛要求)
    KEY_FREQ_DOWN = 8,         ///< 按键B (FREQ- 频率减少100Hz)

    /* 第三行 - 电压预设 + 电压增加 */
    KEY_0_1V = 9,              ///< 按键7 (0.1V)
    KEY_0_5V = 10,             ///< 按键8 (0.5V)
    KEY_1_0V = 11,             ///< 按键9 (1.0V)
    KEY_VOLT_UP = 12,          ///< 按键C (VOLT+ 电压增加0.1V)

    /* 第四行 - 系统控制 + 电压减少 */
    KEY_OUTPUT_TOGGLE = 13,    ///< 按键* (OUT 输出开关)
    KEY_RESET = 14,            ///< 按键0 (RESET 系统复位)
    KEY_SAVE = 15,             ///< 按键# (SAVE 保存预设)
    KEY_VOLT_DOWN = 16         ///< 按键D (VOLT- 电压减少0.1V)
} KeypadKey_t;

/**
 * @brief  按键状态枚举
 */
typedef enum {
    KEY_STATE_RELEASED = 0,    ///< 按键释放
    KEY_STATE_PRESSED,         ///< 按键按下
    KEY_STATE_LONG_PRESSED     ///< 按键长按
} KeypadState_t;

/**
 * @brief  按键事件结构体
 */
typedef struct {
    KeypadKey_t key;           ///< 按键值
    KeypadState_t state;       ///< 按键状态
    uint32_t timestamp;        ///< 时间戳
} KeypadEvent_t;

/**
 * @brief  按键回调函数类型
 */
typedef void (*KeypadCallback_t)(KeypadEvent_t* event);

/* Exported constants --------------------------------------------------------*/

/* GPIO配置 */
#define KEYPAD_ROW_PORT         GPIOB
#define KEYPAD_COL_PORT         GPIOB
#define KEYPAD_GPIO_RCC         RCC_AHB1Periph_GPIOB

#define KEYPAD_ROW0_PIN         GPIO_Pin_0
#define KEYPAD_ROW1_PIN         GPIO_Pin_1
#define KEYPAD_ROW2_PIN         GPIO_Pin_2
#define KEYPAD_ROW3_PIN         GPIO_Pin_3

#define KEYPAD_COL0_PIN         GPIO_Pin_4
#define KEYPAD_COL1_PIN         GPIO_Pin_5
#define KEYPAD_COL2_PIN         GPIO_Pin_6
#define KEYPAD_COL3_PIN         GPIO_Pin_7

#define KEYPAD_ALL_ROWS         (KEYPAD_ROW0_PIN | KEYPAD_ROW1_PIN | KEYPAD_ROW2_PIN | KEYPAD_ROW3_PIN)
#define KEYPAD_ALL_COLS         (KEYPAD_COL0_PIN | KEYPAD_COL1_PIN | KEYPAD_COL2_PIN | KEYPAD_COL3_PIN)

/* 扫描配置 */
#define KEYPAD_DEBOUNCE_TIME    20      ///< 防抖时间 (ms)
#define KEYPAD_LONG_PRESS_TIME  1000    ///< 长按时间 (ms)
#define KEYPAD_SCAN_INTERVAL    10      ///< 扫描间隔 (ms)

/* 电赛G题专用参数 (严格按照要求) */
#define FREQ_PRESET_100HZ       100         ///< 100Hz
#define FREQ_PRESET_1KHZ        1000        ///< 1kHz
#define FREQ_PRESET_10KHZ       10000       ///< 10kHz
#define FREQ_PRESET_100KHZ      100000      ///< 100kHz
#define FREQ_PRESET_500KHZ      500000      ///< 500kHz
#define FREQ_PRESET_1MHZ        1000000     ///< 1MHz (满足电赛要求)

#define VOLT_PRESET_0_1V        100         ///< 0.1V (100mV)
#define VOLT_PRESET_0_5V        500         ///< 0.5V (500mV)
#define VOLT_PRESET_1_0V        1000        ///< 1.0V (1000mV)

#define FREQ_STEP_HZ            100         ///< 频率步进 100Hz (电赛要求)
#define VOLT_STEP_MV            100         ///< 电压步进 0.1V = 100mV (电赛要求)

/* 电赛G题限制参数 */
#define FREQ_MIN_HZ             100         ///< 最小频率 100Hz
#define FREQ_MAX_HZ             1000000     ///< 最大频率 1MHz (满足电赛要求)
#define VOLT_MIN_MV             100         ///< 最小电压 0.1V
#define VOLT_MAX_MV             3000        ///< 最大电压 3.0V (典型上限)

/* Exported functions --------------------------------------------------------*/

/**
 * @brief  4x4矩阵键盘初始化
 * @param  None
 * @retval None
 */
void Keypad4x4_Init(void);

/**
 * @brief  键盘扫描 (在主循环中调用)
 * @param  None
 * @retval None
 */
void Keypad4x4_Scan(void);

/**
 * @brief  获取按键值 (非阻塞)
 * @param  None
 * @retval KeypadKey_t 按键值
 */
KeypadKey_t Keypad4x4_GetKey(void);

/**
 * @brief  检查是否有按键事件
 * @param  event: 按键事件输出
 * @retval true-有事件, false-无事件
 */
bool Keypad4x4_HasEvent(KeypadEvent_t* event);

/**
 * @brief  注册按键回调函数
 * @param  callback: 回调函数指针
 * @retval None
 */
void Keypad4x4_RegisterCallback(KeypadCallback_t callback);

/**
 * @brief  清空按键事件队列
 * @param  None
 * @retval None
 */
void Keypad4x4_ClearEvents(void);

/* ==================== AD9910控制专用函数 ==================== */

/**
 * @brief  处理频率预设按键
 * @param  key: 按键值
 * @retval true-处理成功, false-非频率预设键
 */
bool Keypad4x4_HandleFrequencyPreset(KeypadKey_t key);

/**
 * @brief  处理电压预设按键 (0.1V-1.0V)
 * @param  key: 按键值
 * @retval true-处理成功, false-非电压预设键
 */
bool Keypad4x4_HandleVoltagePreset(KeypadKey_t key);

/**
 * @brief  处理频率步进按键 (±100Hz)
 * @param  key: 按键值
 * @retval true-处理成功, false-非频率步进键
 */
bool Keypad4x4_HandleFrequencyStep(KeypadKey_t key);

/**
 * @brief  处理电压步进按键 (±0.1V)
 * @param  key: 按键值
 * @retval true-处理成功, false-非电压步进键
 */
bool Keypad4x4_HandleVoltageStep(KeypadKey_t key);

/**
 * @brief  处理系统控制按键
 * @param  key: 按键值
 * @retval true-处理成功, false-非系统控制键
 */
bool Keypad4x4_HandleSystemControl(KeypadKey_t key);

/**
 * @brief  处理所有按键 (统一入口)
 * @param  key: 按键值
 * @retval None
 */
void Keypad4x4_ProcessKey(KeypadKey_t key);

/**
 * @brief  获取按键名称字符串
 * @param  key: 按键值
 * @retval 按键名称字符串
 */
const char* Keypad4x4_GetKeyName(KeypadKey_t key);

/**
 * @brief  启用/禁用键盘扫描
 * @param  enable: true-启用, false-禁用
 * @retval None
 */
void Keypad4x4_Enable(bool enable);

/**
 * @brief  获取键盘状态
 * @retval true-启用, false-禁用
 */
bool Keypad4x4_IsEnabled(void);

#ifdef __cplusplus
}
#endif

#endif /* __KEYPAD_4X4_H */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
