/**
  ******************************************************************************
  * @file    keypad_4x4.h
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   4x4矩阵键盘驱动头文件
  ******************************************************************************
  * @attention
  *
  * 本文件实现4x4矩阵键盘的驱动功能，包括：
  * - GPIO配置和扫描
  * - 按键防抖处理
  * - 按键功能映射
  * - AD9910控制集成
  *
  * 硬件连接 (低电平导通):
  * 行线 (输出): PB0, PB1, PB2, PB3
  * 列线 (输入): PB4, PB5, PB6, PB7
  *
  * 按键布局:
  * [1kHz] [10kHz] [100kHz] [FREQ+]
  * [1MHz] [5MHz]  [10MHz]  [FREQ-]
  * [10mV] [100mV] [1V]     [AMP+]
  * [OUT]  [RESET] [PRESET] [AMP-]
  *
  ******************************************************************************
  */

#ifndef __KEYPAD_4X4_H
#define __KEYPAD_4X4_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include <stdint.h>
#include <stdbool.h>

/* Exported types ------------------------------------------------------------*/

/**
 * @brief  按键值枚举
 */
typedef enum {
    KEY_NONE = 0,              ///< 无按键
    KEY_1 = 1,                 ///< 按键1 (1kHz)
    KEY_2 = 2,                 ///< 按键2 (10kHz)
    KEY_3 = 3,                 ///< 按键3 (100kHz)
    KEY_A = 4,                 ///< 按键A (FREQ+)
    KEY_4 = 5,                 ///< 按键4 (1MHz)
    KEY_5 = 6,                 ///< 按键5 (5MHz)
    KEY_6 = 7,                 ///< 按键6 (10MHz)
    KEY_B = 8,                 ///< 按键B (FREQ-)
    KEY_7 = 9,                 ///< 按键7 (10mV)
    KEY_8 = 10,                ///< 按键8 (100mV)
    KEY_9 = 11,                ///< 按键9 (1V)
    KEY_C = 12,                ///< 按键C (AMP+)
    KEY_STAR = 13,             ///< 按键* (OUT)
    KEY_0 = 14,                ///< 按键0 (RESET)
    KEY_HASH = 15,             ///< 按键# (PRESET)
    KEY_D = 16                 ///< 按键D (AMP-)
} KeypadKey_t;

/**
 * @brief  按键状态枚举
 */
typedef enum {
    KEY_STATE_RELEASED = 0,    ///< 按键释放
    KEY_STATE_PRESSED,         ///< 按键按下
    KEY_STATE_LONG_PRESSED     ///< 按键长按
} KeypadState_t;

/**
 * @brief  按键事件结构体
 */
typedef struct {
    KeypadKey_t key;           ///< 按键值
    KeypadState_t state;       ///< 按键状态
    uint32_t timestamp;        ///< 时间戳
} KeypadEvent_t;

/**
 * @brief  按键回调函数类型
 */
typedef void (*KeypadCallback_t)(KeypadEvent_t* event);

/* Exported constants --------------------------------------------------------*/

/* GPIO配置 */
#define KEYPAD_ROW_PORT         GPIOB
#define KEYPAD_COL_PORT         GPIOB
#define KEYPAD_GPIO_RCC         RCC_AHB1Periph_GPIOB

#define KEYPAD_ROW0_PIN         GPIO_Pin_0
#define KEYPAD_ROW1_PIN         GPIO_Pin_1
#define KEYPAD_ROW2_PIN         GPIO_Pin_2
#define KEYPAD_ROW3_PIN         GPIO_Pin_3

#define KEYPAD_COL0_PIN         GPIO_Pin_4
#define KEYPAD_COL1_PIN         GPIO_Pin_5
#define KEYPAD_COL2_PIN         GPIO_Pin_6
#define KEYPAD_COL3_PIN         GPIO_Pin_7

#define KEYPAD_ALL_ROWS         (KEYPAD_ROW0_PIN | KEYPAD_ROW1_PIN | KEYPAD_ROW2_PIN | KEYPAD_ROW3_PIN)
#define KEYPAD_ALL_COLS         (KEYPAD_COL0_PIN | KEYPAD_COL1_PIN | KEYPAD_COL2_PIN | KEYPAD_COL3_PIN)

/* 扫描配置 */
#define KEYPAD_DEBOUNCE_TIME    20      ///< 防抖时间 (ms)
#define KEYPAD_LONG_PRESS_TIME  1000    ///< 长按时间 (ms)
#define KEYPAD_SCAN_INTERVAL    10      ///< 扫描间隔 (ms)

/* 频率预设值 */
#define FREQ_PRESET_1KHZ        1000        ///< 1kHz
#define FREQ_PRESET_10KHZ       10000       ///< 10kHz
#define FREQ_PRESET_100KHZ      100000      ///< 100kHz
#define FREQ_PRESET_1MHZ        1000000     ///< 1MHz
#define FREQ_PRESET_5MHZ        5000000     ///< 5MHz
#define FREQ_PRESET_10MHZ       10000000    ///< 10MHz

/* 幅度预设值 */
#define AMP_PRESET_10MV         10          ///< 10mV
#define AMP_PRESET_100MV        100         ///< 100mV
#define AMP_PRESET_1V           1000        ///< 1V

/* 步进值 */
#define FREQ_STEP_HZ            100         ///< 频率步进 100Hz
#define AMP_STEP_MV             10          ///< 幅度步进 10mV

/* Exported functions --------------------------------------------------------*/

/**
 * @brief  4x4矩阵键盘初始化
 * @param  None
 * @retval None
 */
void Keypad4x4_Init(void);

/**
 * @brief  键盘扫描 (在主循环中调用)
 * @param  None
 * @retval None
 */
void Keypad4x4_Scan(void);

/**
 * @brief  获取按键值 (非阻塞)
 * @param  None
 * @retval KeypadKey_t 按键值
 */
KeypadKey_t Keypad4x4_GetKey(void);

/**
 * @brief  检查是否有按键事件
 * @param  event: 按键事件输出
 * @retval true-有事件, false-无事件
 */
bool Keypad4x4_HasEvent(KeypadEvent_t* event);

/**
 * @brief  注册按键回调函数
 * @param  callback: 回调函数指针
 * @retval None
 */
void Keypad4x4_RegisterCallback(KeypadCallback_t callback);

/**
 * @brief  清空按键事件队列
 * @param  None
 * @retval None
 */
void Keypad4x4_ClearEvents(void);

/* ==================== AD9910控制专用函数 ==================== */

/**
 * @brief  处理频率预设按键
 * @param  key: 按键值
 * @retval true-处理成功, false-非频率预设键
 */
bool Keypad4x4_HandleFrequencyPreset(KeypadKey_t key);

/**
 * @brief  处理幅度预设按键
 * @param  key: 按键值
 * @retval true-处理成功, false-非幅度预设键
 */
bool Keypad4x4_HandleAmplitudePreset(KeypadKey_t key);

/**
 * @brief  处理频率步进按键
 * @param  key: 按键值
 * @retval true-处理成功, false-非频率步进键
 */
bool Keypad4x4_HandleFrequencyStep(KeypadKey_t key);

/**
 * @brief  处理幅度步进按键
 * @param  key: 按键值
 * @retval true-处理成功, false-非幅度步进键
 */
bool Keypad4x4_HandleAmplitudeStep(KeypadKey_t key);

/**
 * @brief  处理系统控制按键
 * @param  key: 按键值
 * @retval true-处理成功, false-非系统控制键
 */
bool Keypad4x4_HandleSystemControl(KeypadKey_t key);

/**
 * @brief  处理所有按键 (统一入口)
 * @param  key: 按键值
 * @retval None
 */
void Keypad4x4_ProcessKey(KeypadKey_t key);

/**
 * @brief  获取按键名称字符串
 * @param  key: 按键值
 * @retval 按键名称字符串
 */
const char* Keypad4x4_GetKeyName(KeypadKey_t key);

/**
 * @brief  启用/禁用键盘扫描
 * @param  enable: true-启用, false-禁用
 * @retval None
 */
void Keypad4x4_Enable(bool enable);

/**
 * @brief  获取键盘状态
 * @retval true-启用, false-禁用
 */
bool Keypad4x4_IsEnabled(void);

#ifdef __cplusplus
}
#endif

#endif /* __KEYPAD_4X4_H */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
