Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    startup_stm32f40_41xxx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(.text) for Reset_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.EXTI0_IRQHandler) for EXTI0_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.TIM6_DAC_IRQHandler) for TIM6_DAC_IRQHandler
    startup_stm32f40_41xxx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f40_41xxx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(HEAP) for Heap_Mem
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(STACK) for Stack_Mem
    main.o(i.AD9854_System_Init) refers to ad9854.o(i.AD9854_Init) for AD9854_Init
    main.o(i.AD9854_System_Init) refers to main.o(i.Error_Handler) for Error_Handler
    main.o(i.AD9854_System_Init) refers to systick.o(i.Delay_ms) for Delay_ms
    main.o(i.AD9854_System_Init) refers to main.o(.data) for ad9854_status
    main.o(i.Error_Handler) refers to stm32f4xx_gpio.o(i.GPIO_ToggleBits) for GPIO_ToggleBits
    main.o(i.Error_Handler) refers to systick.o(i.Delay_ms) for Delay_ms
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_DeInit) for RCC_DeInit
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_HSEConfig) for RCC_HSEConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp) for RCC_WaitForHSEStartUp
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_PLLConfig) for RCC_PLLConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_PLLCmd) for RCC_PLLCmd
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    main.o(i.SystemClock_Config) refers to stm32f4xx_flash.o(i.FLASH_SetLatency) for FLASH_SetLatency
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_HCLKConfig) for RCC_HCLKConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_PCLK1Config) for RCC_PCLK1Config
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_PCLK2Config) for RCC_PCLK2Config
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_SYSCLKConfig) for RCC_SYSCLKConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_GetSYSCLKSource) for RCC_GetSYSCLKSource
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to systick.o(i.SysTick_Init) for SysTick_Init
    main.o(i.main) refers to bsp.o(i.BSP_Init) for BSP_Init
    main.o(i.main) refers to main.o(i.AD9854_System_Init) for AD9854_System_Init
    main.o(i.main) refers to ad9854.o(i.AD9854_InitControlInterface) for AD9854_InitControlInterface
    main.o(i.main) refers to ad9854.o(i.AD9854_EnableModelCircuit) for AD9854_EnableModelCircuit
    main.o(i.main) refers to main.o(i.Error_Handler) for Error_Handler
    main.o(i.main) refers to ad9854.o(i.AD9854_SetTargetFrequency) for AD9854_SetTargetFrequency
    main.o(i.main) refers to ad9854.o(i.AD9854_SetTargetAmplitude) for AD9854_SetTargetAmplitude
    main.o(i.main) refers to ad9854.o(i.AD9854_EnableOutput) for AD9854_EnableOutput
    main.o(i.main) refers to systick.o(i.Delay_ms) for Delay_ms
    main.o(i.main) refers to stm32f4xx_gpio.o(i.GPIO_ToggleBits) for GPIO_ToggleBits
    main.o(i.main) refers to ad9854.o(i.AD9854_GetControlParams) for AD9854_GetControlParams
    main.o(i.main) refers to stm32f4xx_gpio.o(i.GPIO_ReadOutputDataBit) for GPIO_ReadOutputDataBit
    main.o(i.main) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    main.o(i.main) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    main.o(i.main) refers to main.o(.data) for ad9854_status
    main.o(i.main) refers to main.o(.bss) for control_params
    stm32f4xx_it.o(i.EXTI0_IRQHandler) refers to main.o(i.EXTI0_IRQHandler_Internal) for EXTI0_IRQHandler_Internal
    stm32f4xx_it.o(i.SysTick_Handler) refers to main.o(i.TimingDelay_Decrement) for TimingDelay_Decrement
    stm32f4xx_it.o(i.SysTick_Handler) refers to systick.o(i.SysTick_Handler_Internal) for SysTick_Handler_Internal
    stm32f4xx_it.o(i.SysTick_Handler) refers to main.o(.data) for uwTick
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    system_stm32f4xx.o(i.SystemInit) refers to system_stm32f4xx.o(i.SetSysClock) for SetSysClock
    bsp.o(i.BSP_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    bsp.o(i.BSP_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    bsp.o(i.BSP_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    misc.o(i.NVIC_Init) refers to main.o(i.assert_failed) for assert_failed
    misc.o(i.NVIC_PriorityGroupConfig) refers to main.o(i.assert_failed) for assert_failed
    misc.o(i.NVIC_SetVectorTable) refers to main.o(i.assert_failed) for assert_failed
    misc.o(i.NVIC_SystemLPConfig) refers to main.o(i.assert_failed) for assert_failed
    misc.o(i.SysTick_CLKSourceConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd) for RCC_AHB1PeriphResetCmd
    stm32f4xx_gpio.o(i.GPIO_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_PinAFConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_PinLockConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ReadInputData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ReadOutputData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ReadOutputDataBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ResetBits) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_SetBits) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ToggleBits) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_Write) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_WriteBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockLPModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockLPModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockLPModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB3PeriphResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB1PeriphClockLPModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB2PeriphClockLPModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AdjustHSICalibrationValue) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_BackupResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_ClockSecuritySystemCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_GetClocksFreq) refers to stm32f4xx_rcc.o(.data) for APBAHBPrescTable
    stm32f4xx_rcc.o(i.RCC_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_HCLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_HSEConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_HSICmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_I2SCLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_LSEConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_LSEModeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_LSICmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_LTDCCLKDivConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_MCO1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_MCO2Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PCLK1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PCLK2Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLI2SCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLI2SConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLSAICmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLSAIConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_RTCCLKCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_RTCCLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_SAIBlockACLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_SAIBlockBCLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_SAIPLLI2SClkDivConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_SAIPLLSAIClkDivConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_SYSCLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_TIMCLKPresConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f4xx_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f4xx_flash.o(i.FLASH_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_DataCacheCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_EraseAllBank1Sectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseAllBank1Sectors) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_EraseAllBank2Sectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseAllBank2Sectors) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_EraseAllSectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseAllSectors) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_EraseSector) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_EraseSector) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_InstructionCacheCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_BORConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_BootConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_Launch) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_PCROP1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_PCROP1Config) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_PCROPConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_PCROPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_PCROPSelectionConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_RDPConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_RDPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_UserConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_UserConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_WRP1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_WRP1Config) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_WRPConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_WRPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_PrefetchBufferCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ProgramByte) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ProgramByte) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramDoubleWord) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ProgramDoubleWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramHalfWord) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramWord) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ProgramWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_SetLatency) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_flash.o(i.FLASH_GetStatus) for FLASH_GetStatus
    systick.o(i.DWT_Init) refers to systick.o(.data) for s_dwt_initialized
    systick.o(i.Delay_ms) refers to systick.o(i.SysTick_UpdateStats) for SysTick_UpdateStats
    systick.o(i.Delay_ms) refers to systick.o(.data) for s_delay_counter
    systick.o(i.Delay_s) refers to systick.o(i.Delay_ms) for Delay_ms
    systick.o(i.Delay_us) refers to systick.o(i.SysTick_GetCalibratedDelay) for SysTick_GetCalibratedDelay
    systick.o(i.Delay_us) refers to systick.o(i.SysTick_UpdateStats) for SysTick_UpdateStats
    systick.o(i.Delay_us) refers to systick.o(.data) for s_dwt_initialized
    systick.o(i.SysTick_Calibrate) refers to systick.o(i.Delay_us) for Delay_us
    systick.o(i.SysTick_Calibrate) refers to systick.o(.data) for g_systick_cal
    systick.o(i.SysTick_GetCalibratedDelay) refers to systick.o(.data) for g_systick_cal
    systick.o(i.SysTick_GetStats) refers to systick.o(.bss) for g_systick_stats
    systick.o(i.SysTick_GetTick) refers to systick.o(.data) for g_systick_counter
    systick.o(i.SysTick_GetTimestamp_us) refers to systick.o(.data) for g_system_uptime_ms
    systick.o(i.SysTick_GetUptime_ms) refers to systick.o(.data) for g_system_uptime_ms
    systick.o(i.SysTick_Handler_Internal) refers to systick.o(.data) for g_systick_counter
    systick.o(i.SysTick_Init) refers to systick.o(i.NVIC_SetPriority) for NVIC_SetPriority
    systick.o(i.SysTick_Init) refers to systick.o(i.DWT_Init) for DWT_Init
    systick.o(i.SysTick_Init) refers to systick.o(i.SysTick_ResetStats) for SysTick_ResetStats
    systick.o(i.SysTick_Init) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    systick.o(i.SysTick_Init) refers to systick.o(.data) for g_systick_cal
    systick.o(i.SysTick_ResetStats) refers to systick.o(.bss) for g_systick_stats
    systick.o(i.SysTick_SetTemperatureCompensation) refers to systick.o(.data) for g_systick_cal
    systick.o(i.SysTick_UpdateStats) refers to systick.o(.bss) for g_systick_stats
    systick.o(i.SysTick_UpdateStats) refers to systick.o(.data) for g_system_uptime_ms
    ad9854.o(i.AD9854_AmplitudeCodeToVpp) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    ad9854.o(i.AD9854_AmplitudeCodeToVpp) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    ad9854.o(i.AD9854_AmplitudeCodeToVpp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    ad9854.o(i.AD9854_ApplyCurrentParams) refers to ad9854.o(i.AD9854_ValidateParams) for AD9854_ValidateParams
    ad9854.o(i.AD9854_ApplyCurrentParams) refers to ad9854.o(i.AD9854_SetFrequency) for AD9854_SetFrequency
    ad9854.o(i.AD9854_ApplyCurrentParams) refers to ad9854.o(i.AD9854_SetAmplitude) for AD9854_SetAmplitude
    ad9854.o(i.AD9854_ApplyCurrentParams) refers to ad9854.o(.data) for g_control_params
    ad9854.o(i.AD9854_CalculateModelCircuitGain) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    ad9854.o(i.AD9854_CalculateModelCircuitGain) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    ad9854.o(i.AD9854_CalculateModelCircuitGain) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    ad9854.o(i.AD9854_CalculateModelCircuitGain) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    ad9854.o(i.AD9854_CalculateModelCircuitGain) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    ad9854.o(i.AD9854_CalculateRequiredOutput) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    ad9854.o(i.AD9854_CalculateRequiredOutput) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    ad9854.o(i.AD9854_CalculateRequiredOutput) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    ad9854.o(i.AD9854_CalculateRequiredOutputWithModel) refers to ad9854.o(i.AD9854_CalculateModelCircuitGain) for AD9854_CalculateModelCircuitGain
    ad9854.o(i.AD9854_CalculateRequiredOutputWithModel) refers to ad9854.o(i.AD9854_CalculateRequiredOutput) for AD9854_CalculateRequiredOutput
    ad9854.o(i.AD9854_EnableModelCircuit) refers to ad9854.o(i.AD9854_CalculateModelCircuitGain) for AD9854_CalculateModelCircuitGain
    ad9854.o(i.AD9854_EnableModelCircuit) refers to ad9854.o(i.AD9854_CalculateRequiredOutputWithModel) for AD9854_CalculateRequiredOutputWithModel
    ad9854.o(i.AD9854_EnableModelCircuit) refers to ad9854.o(i.AD9854_CalculateRequiredOutput) for AD9854_CalculateRequiredOutput
    ad9854.o(i.AD9854_EnableModelCircuit) refers to ad9854.o(i.AD9854_VppToAmplitudeCode) for AD9854_VppToAmplitudeCode
    ad9854.o(i.AD9854_EnableModelCircuit) refers to ad9854.o(i.AD9854_ApplyCurrentParams) for AD9854_ApplyCurrentParams
    ad9854.o(i.AD9854_EnableModelCircuit) refers to ad9854.o(.data) for g_control_params
    ad9854.o(i.AD9854_EnableOutput) refers to ad9854.o(i.AD9854_ApplyCurrentParams) for AD9854_ApplyCurrentParams
    ad9854.o(i.AD9854_EnableOutput) refers to ad9854.o(i.AD9854_SetAmplitude) for AD9854_SetAmplitude
    ad9854.o(i.AD9854_EnableOutput) refers to ad9854.o(.data) for g_control_params
    ad9854.o(i.AD9854_FrequencyToWord) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    ad9854.o(i.AD9854_FrequencyToWord) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    ad9854.o(i.AD9854_FrequencyToWord) refers to dfixull.o(x$fpl$llufromd) for __aeabi_d2ulz
    ad9854.o(i.AD9854_FrequencyToWord) refers to ad9854.o(.data) for freq_word
    ad9854.o(i.AD9854_GPIO_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    ad9854.o(i.AD9854_GPIO_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    ad9854.o(i.AD9854_GPIO_Init) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ad9854.o(i.AD9854_GPIO_Init) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ad9854.o(i.AD9854_GetControlParams) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    ad9854.o(i.AD9854_GetControlParams) refers to ad9854.o(.data) for g_control_params
    ad9854.o(i.AD9854_Init) refers to ad9854.o(i.AD9854_GPIO_Init) for AD9854_GPIO_Init
    ad9854.o(i.AD9854_Init) refers to ad9854.o(i.AD9854_Delay_us) for AD9854_Delay_us
    ad9854.o(i.AD9854_Init) refers to ad9854.o(i.AD9854_Reset) for AD9854_Reset
    ad9854.o(i.AD9854_Init) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ad9854.o(i.AD9854_Init) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ad9854.o(i.AD9854_Init) refers to ad9854.o(i.AD9854_WriteReg) for AD9854_WriteReg
    ad9854.o(i.AD9854_Init) refers to ad9854.o(i.AD9854_Update) for AD9854_Update
    ad9854.o(i.AD9854_ProcessCommand) refers to ad9854.o(i.AD9854_SetTargetFrequency) for AD9854_SetTargetFrequency
    ad9854.o(i.AD9854_ProcessCommand) refers to ad9854.o(i.AD9854_SetTargetAmplitude) for AD9854_SetTargetAmplitude
    ad9854.o(i.AD9854_ProcessCommand) refers to ad9854.o(i.AD9854_SetGainFactor) for AD9854_SetGainFactor
    ad9854.o(i.AD9854_ProcessCommand) refers to ad9854.o(i.AD9854_EnableOutput) for AD9854_EnableOutput
    ad9854.o(i.AD9854_Reset) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ad9854.o(i.AD9854_Reset) refers to ad9854.o(i.AD9854_Delay_us) for AD9854_Delay_us
    ad9854.o(i.AD9854_Reset) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ad9854.o(i.AD9854_SetAmplitude) refers to ad9854.o(i.AD9854_WriteReg) for AD9854_WriteReg
    ad9854.o(i.AD9854_SetAmplitude) refers to ad9854.o(i.AD9854_Update) for AD9854_Update
    ad9854.o(i.AD9854_SetFrequency) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    ad9854.o(i.AD9854_SetFrequency) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    ad9854.o(i.AD9854_SetFrequency) refers to ad9854.o(i.AD9854_FrequencyToWord) for AD9854_FrequencyToWord
    ad9854.o(i.AD9854_SetFrequency) refers to ad9854.o(i.AD9854_WriteFrequencyWord) for AD9854_WriteFrequencyWord
    ad9854.o(i.AD9854_SetFrequency) refers to ad9854.o(i.AD9854_Update) for AD9854_Update
    ad9854.o(i.AD9854_SetGainFactor) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    ad9854.o(i.AD9854_SetGainFactor) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    ad9854.o(i.AD9854_SetGainFactor) refers to ad9854.o(i.AD9854_CalculateRequiredOutput) for AD9854_CalculateRequiredOutput
    ad9854.o(i.AD9854_SetGainFactor) refers to ad9854.o(i.AD9854_VppToAmplitudeCode) for AD9854_VppToAmplitudeCode
    ad9854.o(i.AD9854_SetGainFactor) refers to ad9854.o(i.AD9854_ApplyCurrentParams) for AD9854_ApplyCurrentParams
    ad9854.o(i.AD9854_SetGainFactor) refers to ad9854.o(.data) for g_control_params
    ad9854.o(i.AD9854_SetTargetAmplitude) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    ad9854.o(i.AD9854_SetTargetAmplitude) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    ad9854.o(i.AD9854_SetTargetAmplitude) refers to ad9854.o(i.AD9854_CalculateRequiredOutputWithModel) for AD9854_CalculateRequiredOutputWithModel
    ad9854.o(i.AD9854_SetTargetAmplitude) refers to ad9854.o(i.AD9854_CalculateRequiredOutput) for AD9854_CalculateRequiredOutput
    ad9854.o(i.AD9854_SetTargetAmplitude) refers to ad9854.o(i.AD9854_VppToAmplitudeCode) for AD9854_VppToAmplitudeCode
    ad9854.o(i.AD9854_SetTargetAmplitude) refers to ad9854.o(i.AD9854_ApplyCurrentParams) for AD9854_ApplyCurrentParams
    ad9854.o(i.AD9854_SetTargetAmplitude) refers to ad9854.o(.data) for g_control_params
    ad9854.o(i.AD9854_SetTargetFrequency) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    ad9854.o(i.AD9854_SetTargetFrequency) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    ad9854.o(i.AD9854_SetTargetFrequency) refers to ad9854.o(i.AD9854_CalculateModelCircuitGain) for AD9854_CalculateModelCircuitGain
    ad9854.o(i.AD9854_SetTargetFrequency) refers to ad9854.o(i.AD9854_CalculateRequiredOutputWithModel) for AD9854_CalculateRequiredOutputWithModel
    ad9854.o(i.AD9854_SetTargetFrequency) refers to ad9854.o(i.AD9854_VppToAmplitudeCode) for AD9854_VppToAmplitudeCode
    ad9854.o(i.AD9854_SetTargetFrequency) refers to ad9854.o(i.AD9854_ApplyCurrentParams) for AD9854_ApplyCurrentParams
    ad9854.o(i.AD9854_SetTargetFrequency) refers to ad9854.o(.data) for g_control_params
    ad9854.o(i.AD9854_Update) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ad9854.o(i.AD9854_Update) refers to ad9854.o(i.AD9854_Delay_us) for AD9854_Delay_us
    ad9854.o(i.AD9854_Update) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ad9854.o(i.AD9854_ValidateParams) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    ad9854.o(i.AD9854_ValidateParams) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    ad9854.o(i.AD9854_VppToAmplitudeCode) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    ad9854.o(i.AD9854_VppToAmplitudeCode) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    ad9854.o(i.AD9854_VppToAmplitudeCode) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    ad9854.o(i.AD9854_WriteFrequencyWord) refers to ad9854.o(i.AD9854_WriteReg) for AD9854_WriteReg
    ad9854.o(i.AD9854_WriteFrequencyWord) refers to ad9854.o(.data) for freq_word
    ad9854.o(i.AD9854_WriteReg) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ad9854.o(i.AD9854_WriteReg) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ad9854.o(i.AD9854_WriteReg) refers to ad9854.o(i.AD9854_Delay_us) for AD9854_Delay_us
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixull.o(x$fpl$llufromd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixull.o(x$fpl$llufromd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixull.o(x$fpl$llufromdr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixull.o(x$fpl$llufromdr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    sqrt.o(i.__hardfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_umaal.o(x$fpl$dsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_umaal.o(x$fpl$dsqrt) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f40_41xxx.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (192 bytes).
    Removing bsp.o(.rev16_text), (4 bytes).
    Removing bsp.o(.revsh_text), (4 bytes).
    Removing bsp.o(.rrx_text), (6 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing misc.o(.rrx_text), (6 bytes).
    Removing misc.o(i.NVIC_Init), (192 bytes).
    Removing misc.o(i.NVIC_PriorityGroupConfig), (80 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (88 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (92 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (80 bytes).
    Removing stm32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_DeInit), (416 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_PinAFConfig), (424 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_PinLockConfig), (204 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadInputData), (160 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit), (268 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadOutputData), (160 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_StructInit), (18 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_Write), (160 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_WriteBit), (280 bytes).
    Removing stm32f4xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockLPModeCmd), (104 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd), (104 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockLPModeCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockLPModeCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphResetCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphClockLPModeCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB2PeriphClockLPModeCmd), (104 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd), (104 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AdjustHSICalibrationValue), (68 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_BackupResetCmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearITPendingBit), (52 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClockSecuritySystemCmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetClocksFreq), (232 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetITStatus), (96 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HSICmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_I2SCLKConfig), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ITConfig), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSEConfig), (96 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSEModeConfig), (84 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSICmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LTDCCLKDivConfig), (80 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO1Config), (128 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO2Config), (128 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SCmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SConfig), (88 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAICmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAIConfig), (116 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKCmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKConfig), (412 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockACLKConfig), (76 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockBCLKConfig), (76 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLI2SClkDivConfig), (72 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLSAIClkDivConfig), (76 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_TIMCLKPresConfig), (56 bytes).
    Removing stm32f4xx_rcc.o(.data), (16 bytes).
    Removing stm32f4xx_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ClearFlag), (68 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_DataCacheCmd), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_DataCacheReset), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllBank1Sectors), (164 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllBank2Sectors), (164 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllSectors), (164 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseSector), (300 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_GetFlagStatus), (104 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_GetStatus), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ITConfig), (104 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_InstructionCacheCmd), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_InstructionCacheReset), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_Lock), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_BORConfig), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_BootConfig), (76 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetBOR), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetPCROP), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetPCROP1), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetRDP), (24 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetWRP1), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Launch), (32 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROP1Config), (116 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROPConfig), (116 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROPSelectionConfig), (76 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_RDPConfig), (76 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Unlock), (36 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_UserConfig), (140 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_WRP1Config), (116 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_WRPConfig), (116 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_PrefetchBufferCmd), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramByte), (148 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramDoubleWord), (160 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramHalfWord), (152 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramWord), (152 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_Unlock), (36 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_WaitForLastOperation), (34 bytes).
    Removing systick.o(.rev16_text), (4 bytes).
    Removing systick.o(.revsh_text), (4 bytes).
    Removing systick.o(.rrx_text), (6 bytes).
    Removing systick.o(i.Delay_s), (24 bytes).
    Removing systick.o(i.Delay_us), (80 bytes).
    Removing systick.o(i.SysTick_Calibrate), (96 bytes).
    Removing systick.o(i.SysTick_GetCalibratedDelay), (72 bytes).
    Removing systick.o(i.SysTick_GetStats), (24 bytes).
    Removing systick.o(i.SysTick_GetTick), (12 bytes).
    Removing systick.o(i.SysTick_GetTimestamp_us), (56 bytes).
    Removing systick.o(i.SysTick_GetUptime_ms), (12 bytes).
    Removing systick.o(i.SysTick_NonBlocking_Init), (32 bytes).
    Removing systick.o(i.SysTick_NonBlocking_IsCompleted), (48 bytes).
    Removing systick.o(i.SysTick_SetTemperatureCompensation), (24 bytes).
    Removing ad9854.o(.rev16_text), (4 bytes).
    Removing ad9854.o(.revsh_text), (4 bytes).
    Removing ad9854.o(.rrx_text), (6 bytes).
    Removing ad9854.o(i.AD9854_AmplitudeCodeToVpp), (72 bytes).
    Removing ad9854.o(i.AD9854_ProcessCommand), (132 bytes).
    Removing ad9854.o(i.AD9854_SetGainFactor), (152 bytes).

138 unused section(s) (total 10780 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dfixull.s                       0x00000000   Number         0  dfixull.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/dsqrt.s                         0x00000000   Number         0  dsqrt_umaal.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    Library\\misc.c                          0x00000000   Number         0  misc.o ABSOLUTE
    Library\\stm32f4xx_flash.c               0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    Library\\stm32f4xx_gpio.c                0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    Library\\stm32f4xx_rcc.c                 0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    Library\misc.c                           0x00000000   Number         0  misc.o ABSOLUTE
    Library\stm32f4xx_flash.c                0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    Library\stm32f4xx_gpio.c                 0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    Library\stm32f4xx_rcc.c                  0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    Modules\Core\systick.c                   0x00000000   Number         0  systick.o ABSOLUTE
    Modules\Generation\ad9854.c              0x00000000   Number         0  ad9854.o ABSOLUTE
    Modules\\Core\\systick.c                 0x00000000   Number         0  systick.o ABSOLUTE
    Modules\\Generation\\ad9854.c            0x00000000   Number         0  ad9854.o ABSOLUTE
    Start\startup_stm32f40_41xxx.s           0x00000000   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    User\\bsp.c                              0x00000000   Number         0  bsp.o ABSOLUTE
    User\\main.c                             0x00000000   Number         0  main.o ABSOLUTE
    User\\stm32f4xx_it.c                     0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    User\\system_stm32f4xx.c                 0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    User\bsp.c                               0x00000000   Number         0  bsp.o ABSOLUTE
    User\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    User\stm32f4xx_it.c                      0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    User\system_stm32f4xx.c                  0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f40_41xxx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001e0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x080001fc   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x080001fe   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000202   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000204   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x08000206   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x08000208   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000208   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000208   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800020e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800020e   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000212   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000212   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800021a   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0800021c   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0800021c   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000220   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000228   Section       64  startup_stm32f40_41xxx.o(.text)
    $v0                                      0x08000228   Number         0  startup_stm32f40_41xxx.o(.text)
    .text                                    0x08000268   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x080002cc   Section        0  heapauxi.o(.text)
    .text                                    0x080002d2   Section        0  _rserrno.o(.text)
    .text                                    0x080002e8   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x080002f0   Section        8  libspace.o(.text)
    .text                                    0x080002f8   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000342   Section        0  exit.o(.text)
    .text                                    0x08000354   Section        0  sys_exit.o(.text)
    .text                                    0x08000360   Section        2  use_no_semi.o(.text)
    .text                                    0x08000362   Section        0  indicate_semi.o(.text)
    i.AD9854_ApplyCurrentParams              0x08000364   Section        0  ad9854.o(i.AD9854_ApplyCurrentParams)
    AD9854_ApplyCurrentParams                0x08000365   Thumb Code    74  ad9854.o(i.AD9854_ApplyCurrentParams)
    i.AD9854_CalculateModelCircuitGain       0x080003b4   Section        0  ad9854.o(i.AD9854_CalculateModelCircuitGain)
    i.AD9854_CalculateRequiredOutput         0x080004a8   Section        0  ad9854.o(i.AD9854_CalculateRequiredOutput)
    i.AD9854_CalculateRequiredOutputWithModel 0x0800052c   Section        0  ad9854.o(i.AD9854_CalculateRequiredOutputWithModel)
    i.AD9854_Delay_us                        0x08000570   Section        0  ad9854.o(i.AD9854_Delay_us)
    i.AD9854_EnableModelCircuit              0x08000584   Section        0  ad9854.o(i.AD9854_EnableModelCircuit)
    i.AD9854_EnableOutput                    0x08000610   Section        0  ad9854.o(i.AD9854_EnableOutput)
    i.AD9854_FrequencyToWord                 0x08000630   Section        0  ad9854.o(i.AD9854_FrequencyToWord)
    AD9854_FrequencyToWord                   0x08000631   Thumb Code    86  ad9854.o(i.AD9854_FrequencyToWord)
    i.AD9854_GPIO_Init                       0x0800069c   Section        0  ad9854.o(i.AD9854_GPIO_Init)
    i.AD9854_GetControlParams                0x08000744   Section        0  ad9854.o(i.AD9854_GetControlParams)
    i.AD9854_Init                            0x08000760   Section        0  ad9854.o(i.AD9854_Init)
    i.AD9854_InitControlInterface            0x080007bc   Section        0  ad9854.o(i.AD9854_InitControlInterface)
    i.AD9854_Reset                           0x080007d8   Section        0  ad9854.o(i.AD9854_Reset)
    i.AD9854_SetAmplitude                    0x080007fc   Section        0  ad9854.o(i.AD9854_SetAmplitude)
    AD9854_SetAmplitude                      0x080007fd   Thumb Code    54  ad9854.o(i.AD9854_SetAmplitude)
    i.AD9854_SetFrequency                    0x08000834   Section        0  ad9854.o(i.AD9854_SetFrequency)
    AD9854_SetFrequency                      0x08000835   Thumb Code    84  ad9854.o(i.AD9854_SetFrequency)
    i.AD9854_SetTargetAmplitude              0x08000898   Section        0  ad9854.o(i.AD9854_SetTargetAmplitude)
    i.AD9854_SetTargetFrequency              0x08000950   Section        0  ad9854.o(i.AD9854_SetTargetFrequency)
    i.AD9854_System_Init                     0x080009f4   Section        0  main.o(i.AD9854_System_Init)
    i.AD9854_Update                          0x08000a14   Section        0  ad9854.o(i.AD9854_Update)
    i.AD9854_ValidateParams                  0x08000a38   Section        0  ad9854.o(i.AD9854_ValidateParams)
    i.AD9854_VppToAmplitudeCode              0x08000b44   Section        0  ad9854.o(i.AD9854_VppToAmplitudeCode)
    i.AD9854_WriteFrequencyWord              0x08000ba0   Section        0  ad9854.o(i.AD9854_WriteFrequencyWord)
    AD9854_WriteFrequencyWord                0x08000ba1   Thumb Code    34  ad9854.o(i.AD9854_WriteFrequencyWord)
    i.AD9854_WriteReg                        0x08000bc8   Section        0  ad9854.o(i.AD9854_WriteReg)
    i.BSP_Init                               0x08000c48   Section        0  bsp.o(i.BSP_Init)
    i.BusFault_Handler                       0x08000c7c   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DWT_Init                               0x08000c80   Section        0  systick.o(i.DWT_Init)
    i.DebugMon_Handler                       0x08000cdc   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Delay_ms                               0x08000ce0   Section        0  systick.o(i.Delay_ms)
    i.EXTI0_IRQHandler                       0x08000d0c   Section        0  stm32f4xx_it.o(i.EXTI0_IRQHandler)
    i.EXTI0_IRQHandler_Internal              0x08000d14   Section        0  main.o(i.EXTI0_IRQHandler_Internal)
    i.Error_Handler                          0x08000d18   Section        0  main.o(i.Error_Handler)
    Error_Handler                            0x08000d19   Thumb Code    18  main.o(i.Error_Handler)
    i.FLASH_SetLatency                       0x08000d30   Section        0  stm32f4xx_flash.o(i.FLASH_SetLatency)
    i.GPIO_Init                              0x08000da4   Section        0  stm32f4xx_gpio.o(i.GPIO_Init)
    i.GPIO_ReadOutputDataBit                 0x08000f4c   Section        0  stm32f4xx_gpio.o(i.GPIO_ReadOutputDataBit)
    i.GPIO_ResetBits                         0x08001058   Section        0  stm32f4xx_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x08001108   Section        0  stm32f4xx_gpio.o(i.GPIO_SetBits)
    i.GPIO_ToggleBits                        0x080011b8   Section        0  stm32f4xx_gpio.o(i.GPIO_ToggleBits)
    i.HardFault_Handler                      0x0800125c   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.MemManage_Handler                      0x08001260   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08001264   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.NVIC_SetPriority                       0x08001268   Section        0  systick.o(i.NVIC_SetPriority)
    NVIC_SetPriority                         0x08001269   Thumb Code    32  systick.o(i.NVIC_SetPriority)
    i.PendSV_Handler                         0x08001290   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.RCC_AHB1PeriphClockCmd                 0x08001294   Section        0  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    i.RCC_APB1PeriphClockCmd                 0x080012fc   Section        0  stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x08001360   Section        0  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_DeInit                             0x080013c8   Section        0  stm32f4xx_rcc.o(i.RCC_DeInit)
    i.RCC_GetFlagStatus                      0x0800142c   Section        0  stm32f4xx_rcc.o(i.RCC_GetFlagStatus)
    i.RCC_GetSYSCLKSource                    0x080014d0   Section        0  stm32f4xx_rcc.o(i.RCC_GetSYSCLKSource)
    i.RCC_HCLKConfig                         0x080014e0   Section        0  stm32f4xx_rcc.o(i.RCC_HCLKConfig)
    i.RCC_HSEConfig                          0x08001540   Section        0  stm32f4xx_rcc.o(i.RCC_HSEConfig)
    i.RCC_PCLK1Config                        0x08001584   Section        0  stm32f4xx_rcc.o(i.RCC_PCLK1Config)
    i.RCC_PCLK2Config                        0x080015dc   Section        0  stm32f4xx_rcc.o(i.RCC_PCLK2Config)
    i.RCC_PLLCmd                             0x08001634   Section        0  stm32f4xx_rcc.o(i.RCC_PLLCmd)
    i.RCC_PLLConfig                          0x0800166c   Section        0  stm32f4xx_rcc.o(i.RCC_PLLConfig)
    i.RCC_SYSCLKConfig                       0x08001724   Section        0  stm32f4xx_rcc.o(i.RCC_SYSCLKConfig)
    i.RCC_WaitForHSEStartUp                  0x0800176c   Section        0  stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp)
    i.SVC_Handler                            0x080017a4   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.SetSysClock                            0x080017a8   Section        0  system_stm32f4xx.o(i.SetSysClock)
    SetSysClock                              0x080017a9   Thumb Code   220  system_stm32f4xx.o(i.SetSysClock)
    i.SysTick_Handler                        0x08001894   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SysTick_Handler_Internal               0x080018b0   Section        0  systick.o(i.SysTick_Handler_Internal)
    i.SysTick_Init                           0x080018e8   Section        0  systick.o(i.SysTick_Init)
    i.SysTick_ResetStats                     0x08001964   Section        0  systick.o(i.SysTick_ResetStats)
    i.SysTick_UpdateStats                    0x08001984   Section        0  systick.o(i.SysTick_UpdateStats)
    SysTick_UpdateStats                      0x08001985   Thumb Code    80  systick.o(i.SysTick_UpdateStats)
    i.SystemClock_Config                     0x080019e4   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08001a50   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TIM6_DAC_IRQHandler                    0x08001ab8   Section        0  stm32f4xx_it.o(i.TIM6_DAC_IRQHandler)
    i.TimingDelay_Decrement                  0x08001aba   Section        0  main.o(i.TimingDelay_Decrement)
    i.UsageFault_Handler                     0x08001abc   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__hardfp_sqrt                          0x08001ac0   Section        0  sqrt.o(i.__hardfp_sqrt)
    i.assert_failed                          0x08001b3a   Section        0  main.o(i.assert_failed)
    i.main                                   0x08001b40   Section        0  main.o(i.main)
    x$fpl$dadd                               0x08001c70   Section      336  daddsub_clz.o(x$fpl$dadd)
    $v0                                      0x08001c70   Number         0  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x08001c81   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcmpinf                            0x08001dc0   Section       24  dcmpi.o(x$fpl$dcmpinf)
    $v0                                      0x08001dc0   Number         0  dcmpi.o(x$fpl$dcmpinf)
    x$fpl$ddiv                               0x08001dd8   Section      688  ddiv.o(x$fpl$ddiv)
    $v0                                      0x08001dd8   Number         0  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x08001ddf   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$dfixu                              0x08002088   Section       90  dfixu.o(x$fpl$dfixu)
    $v0                                      0x08002088   Number         0  dfixu.o(x$fpl$dfixu)
    x$fpl$dleqf                              0x080020e4   Section      120  dleqf.o(x$fpl$dleqf)
    $v0                                      0x080020e4   Number         0  dleqf.o(x$fpl$dleqf)
    x$fpl$dmul                               0x0800215c   Section      340  dmul.o(x$fpl$dmul)
    $v0                                      0x0800215c   Number         0  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x080022b0   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x080022b0   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x0800234c   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x0800234c   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$drleqf                             0x08002358   Section      108  drleqf.o(x$fpl$drleqf)
    $v0                                      0x08002358   Number         0  drleqf.o(x$fpl$drleqf)
    x$fpl$drsb                               0x080023c4   Section       22  daddsub_clz.o(x$fpl$drsb)
    $v0                                      0x080023c4   Number         0  daddsub_clz.o(x$fpl$drsb)
    x$fpl$dsqrt                              0x080023dc   Section      408  dsqrt_umaal.o(x$fpl$dsqrt)
    $v0                                      0x080023dc   Number         0  dsqrt_umaal.o(x$fpl$dsqrt)
    x$fpl$dsub                               0x08002574   Section      468  daddsub_clz.o(x$fpl$dsub)
    $v0                                      0x08002574   Number         0  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x08002585   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$fpinit                             0x08002748   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x08002748   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$llufromd                           0x08002754   Section      120  dfixull.o(x$fpl$llufromd)
    $v0                                      0x08002754   Number         0  dfixull.o(x$fpl$llufromd)
    x$fpl$usenofp                            0x080027cc   Section        0  usenofp.o(x$fpl$usenofp)
    .data                                    0x20000000   Section        5  main.o(.data)
    ad9854_status                            0x20000004   Data           1  main.o(.data)
    .data                                    0x20000008   Section       20  system_stm32f4xx.o(.data)
    .data                                    0x2000001c   Section       25  systick.o(.data)
    s_delay_counter                          0x20000030   Data           4  systick.o(.data)
    s_dwt_initialized                        0x20000034   Data           1  systick.o(.data)
    .data                                    0x20000038   Section       64  ad9854.o(.data)
    freq_word                                0x20000038   Data           6  ad9854.o(.data)
    g_control_params                         0x20000040   Data          56  ad9854.o(.data)
    .bss                                     0x20000078   Section       56  main.o(.bss)
    control_params                           0x20000078   Data          56  main.o(.bss)
    .bss                                     0x200000b0   Section       16  systick.o(.bss)
    .bss                                     0x200000c0   Section       96  libspace.o(.bss)
    HEAP                                     0x20000120   Section      512  startup_stm32f40_41xxx.o(HEAP)
    Heap_Mem                                 0x20000120   Data         512  startup_stm32f40_41xxx.o(HEAP)
    STACK                                    0x20000320   Section     1024  startup_stm32f40_41xxx.o(STACK)
    Stack_Mem                                0x20000320   Data        1024  startup_stm32f40_41xxx.o(STACK)
    __initial_sp                             0x20000720   Data           0  startup_stm32f40_41xxx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f40_41xxx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f40_41xxx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001e1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x080001fd   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080001ff   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_alloca_1                   0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x08000205   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x08000209   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000209   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000209   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800020f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800020f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000213   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000213   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800021b   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0800021d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0800021d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000221   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000229   Thumb Code     8  startup_stm32f40_41xxx.o(.text)
    ADC_IRQHandler                           0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX0_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX1_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_SCE_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_TX_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX0_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX1_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_SCE_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_TX_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CRYP_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DCMI_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream0_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream1_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream2_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream3_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream4_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream5_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream6_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream7_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream0_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream1_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream2_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream3_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream4_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream5_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream6_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream7_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_IRQHandler                           0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_WKUP_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI15_10_IRQHandler                     0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI1_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI2_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI3_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI4_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI9_5_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FLASH_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FPU_IRQHandler                           0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FSMC_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    HASH_RNG_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_ER_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_EV_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_ER_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_EV_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_ER_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_EV_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_IRQHandler                        0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_IRQHandler                        0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    PVD_IRQHandler                           0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RCC_IRQHandler                           0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_Alarm_IRQHandler                     0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_WKUP_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SDIO_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI1_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI2_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI3_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TAMP_STAMP_IRQHandler                    0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_CC_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM2_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM3_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM4_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM5_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM7_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_CC_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART4_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART5_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART1_IRQHandler                        0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART2_IRQHandler                        0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART3_IRQHandler                        0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART6_IRQHandler                        0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    WWDG_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __user_initial_stackheap                 0x08000245   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __aeabi_memcpy4                          0x08000269   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x08000269   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x08000269   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x080002b1   Thumb Code     0  rt_memcpy_w.o(.text)
    __use_two_region_memory                  0x080002cd   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080002cf   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080002d1   Thumb Code     2  heapauxi.o(.text)
    __read_errno                             0x080002d3   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x080002dd   Thumb Code    12  _rserrno.o(.text)
    __aeabi_errno_addr                       0x080002e9   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x080002e9   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x080002e9   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __user_libspace                          0x080002f1   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x080002f1   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x080002f1   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x080002f9   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08000343   Thumb Code    18  exit.o(.text)
    _sys_exit                                0x08000355   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x08000361   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000361   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x08000363   Thumb Code     0  indicate_semi.o(.text)
    AD9854_CalculateModelCircuitGain         0x080003b5   Thumb Code   204  ad9854.o(i.AD9854_CalculateModelCircuitGain)
    AD9854_CalculateRequiredOutput           0x080004a9   Thumb Code   114  ad9854.o(i.AD9854_CalculateRequiredOutput)
    AD9854_CalculateRequiredOutputWithModel  0x0800052d   Thumb Code    68  ad9854.o(i.AD9854_CalculateRequiredOutputWithModel)
    AD9854_Delay_us                          0x08000571   Thumb Code    20  ad9854.o(i.AD9854_Delay_us)
    AD9854_EnableModelCircuit                0x08000585   Thumb Code   134  ad9854.o(i.AD9854_EnableModelCircuit)
    AD9854_EnableOutput                      0x08000611   Thumb Code    28  ad9854.o(i.AD9854_EnableOutput)
    AD9854_GPIO_Init                         0x0800069d   Thumb Code   156  ad9854.o(i.AD9854_GPIO_Init)
    AD9854_GetControlParams                  0x08000745   Thumb Code    24  ad9854.o(i.AD9854_GetControlParams)
    AD9854_Init                              0x08000761   Thumb Code    84  ad9854.o(i.AD9854_Init)
    AD9854_InitControlInterface              0x080007bd   Thumb Code    28  ad9854.o(i.AD9854_InitControlInterface)
    AD9854_Reset                             0x080007d9   Thumb Code    32  ad9854.o(i.AD9854_Reset)
    AD9854_SetTargetAmplitude                0x08000899   Thumb Code   164  ad9854.o(i.AD9854_SetTargetAmplitude)
    AD9854_SetTargetFrequency                0x08000951   Thumb Code   144  ad9854.o(i.AD9854_SetTargetFrequency)
    AD9854_System_Init                       0x080009f5   Thumb Code    28  main.o(i.AD9854_System_Init)
    AD9854_Update                            0x08000a15   Thumb Code    32  ad9854.o(i.AD9854_Update)
    AD9854_ValidateParams                    0x08000a39   Thumb Code   218  ad9854.o(i.AD9854_ValidateParams)
    AD9854_VppToAmplitudeCode                0x08000b45   Thumb Code    74  ad9854.o(i.AD9854_VppToAmplitudeCode)
    AD9854_WriteReg                          0x08000bc9   Thumb Code   120  ad9854.o(i.AD9854_WriteReg)
    BSP_Init                                 0x08000c49   Thumb Code    52  bsp.o(i.BSP_Init)
    BusFault_Handler                         0x08000c7d   Thumb Code     4  stm32f4xx_it.o(i.BusFault_Handler)
    DWT_Init                                 0x08000c81   Thumb Code    78  systick.o(i.DWT_Init)
    DebugMon_Handler                         0x08000cdd   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Delay_ms                                 0x08000ce1   Thumb Code    40  systick.o(i.Delay_ms)
    EXTI0_IRQHandler                         0x08000d0d   Thumb Code     8  stm32f4xx_it.o(i.EXTI0_IRQHandler)
    EXTI0_IRQHandler_Internal                0x08000d15   Thumb Code     2  main.o(i.EXTI0_IRQHandler_Internal)
    FLASH_SetLatency                         0x08000d31   Thumb Code    84  stm32f4xx_flash.o(i.FLASH_SetLatency)
    GPIO_Init                                0x08000da5   Thumb Code   352  stm32f4xx_gpio.o(i.GPIO_Init)
    GPIO_ReadOutputDataBit                   0x08000f4d   Thumb Code   194  stm32f4xx_gpio.o(i.GPIO_ReadOutputDataBit)
    GPIO_ResetBits                           0x08001059   Thumb Code   104  stm32f4xx_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x08001109   Thumb Code   104  stm32f4xx_gpio.o(i.GPIO_SetBits)
    GPIO_ToggleBits                          0x080011b9   Thumb Code    92  stm32f4xx_gpio.o(i.GPIO_ToggleBits)
    HardFault_Handler                        0x0800125d   Thumb Code     4  stm32f4xx_it.o(i.HardFault_Handler)
    MemManage_Handler                        0x08001261   Thumb Code     4  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08001265   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x08001291   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    RCC_AHB1PeriphClockCmd                   0x08001295   Thumb Code    70  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    RCC_APB1PeriphClockCmd                   0x080012fd   Thumb Code    70  stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08001361   Thumb Code    70  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_DeInit                               0x080013c9   Thumb Code    82  stm32f4xx_rcc.o(i.RCC_DeInit)
    RCC_GetFlagStatus                        0x0800142d   Thumb Code   134  stm32f4xx_rcc.o(i.RCC_GetFlagStatus)
    RCC_GetSYSCLKSource                      0x080014d1   Thumb Code    10  stm32f4xx_rcc.o(i.RCC_GetSYSCLKSource)
    RCC_HCLKConfig                           0x080014e1   Thumb Code    66  stm32f4xx_rcc.o(i.RCC_HCLKConfig)
    RCC_HSEConfig                            0x08001541   Thumb Code    38  stm32f4xx_rcc.o(i.RCC_HSEConfig)
    RCC_PCLK1Config                          0x08001585   Thumb Code    58  stm32f4xx_rcc.o(i.RCC_PCLK1Config)
    RCC_PCLK2Config                          0x080015dd   Thumb Code    60  stm32f4xx_rcc.o(i.RCC_PCLK2Config)
    RCC_PLLCmd                               0x08001635   Thumb Code    28  stm32f4xx_rcc.o(i.RCC_PLLCmd)
    RCC_PLLConfig                            0x0800166d   Thumb Code   154  stm32f4xx_rcc.o(i.RCC_PLLConfig)
    RCC_SYSCLKConfig                         0x08001725   Thumb Code    42  stm32f4xx_rcc.o(i.RCC_SYSCLKConfig)
    RCC_WaitForHSEStartUp                    0x0800176d   Thumb Code    56  stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp)
    SVC_Handler                              0x080017a5   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08001895   Thumb Code    22  stm32f4xx_it.o(i.SysTick_Handler)
    SysTick_Handler_Internal                 0x080018b1   Thumb Code    42  systick.o(i.SysTick_Handler_Internal)
    SysTick_Init                             0x080018e9   Thumb Code   114  systick.o(i.SysTick_Init)
    SysTick_ResetStats                       0x08001965   Thumb Code    22  systick.o(i.SysTick_ResetStats)
    SystemClock_Config                       0x080019e5   Thumb Code   106  main.o(i.SystemClock_Config)
    SystemInit                               0x08001a51   Thumb Code    88  system_stm32f4xx.o(i.SystemInit)
    TIM6_DAC_IRQHandler                      0x08001ab9   Thumb Code     2  stm32f4xx_it.o(i.TIM6_DAC_IRQHandler)
    TimingDelay_Decrement                    0x08001abb   Thumb Code     2  main.o(i.TimingDelay_Decrement)
    UsageFault_Handler                       0x08001abd   Thumb Code     4  stm32f4xx_it.o(i.UsageFault_Handler)
    __hardfp_sqrt                            0x08001ac1   Thumb Code   122  sqrt.o(i.__hardfp_sqrt)
    assert_failed                            0x08001b3b   Thumb Code     4  main.o(i.assert_failed)
    main                                     0x08001b41   Thumb Code   268  main.o(i.main)
    __aeabi_dadd                             0x08001c71   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x08001c71   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcmp_Inf                           0x08001dc1   Thumb Code    24  dcmpi.o(x$fpl$dcmpinf)
    __aeabi_ddiv                             0x08001dd9   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x08001dd9   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_d2uiz                            0x08002089   Thumb Code     0  dfixu.o(x$fpl$dfixu)
    _dfixu                                   0x08002089   Thumb Code    90  dfixu.o(x$fpl$dfixu)
    __aeabi_cdcmple                          0x080020e5   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    _dcmple                                  0x080020e5   Thumb Code   120  dleqf.o(x$fpl$dleqf)
    __fpl_dcmple_InfNaN                      0x08002147   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    __aeabi_dmul                             0x0800215d   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x0800215d   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x080022b1   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x0800234d   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_cdrcmple                         0x08002359   Thumb Code     0  drleqf.o(x$fpl$drleqf)
    _drcmple                                 0x08002359   Thumb Code   108  drleqf.o(x$fpl$drleqf)
    __aeabi_drsub                            0x080023c5   Thumb Code     0  daddsub_clz.o(x$fpl$drsb)
    _drsb                                    0x080023c5   Thumb Code    22  daddsub_clz.o(x$fpl$drsb)
    _dsqrt                                   0x080023dd   Thumb Code   404  dsqrt_umaal.o(x$fpl$dsqrt)
    __aeabi_dsub                             0x08002575   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x08002575   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    _fp_init                                 0x08002749   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08002751   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08002751   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __aeabi_d2ulz                            0x08002755   Thumb Code     0  dfixull.o(x$fpl$llufromd)
    _ll_ufrom_d                              0x08002755   Thumb Code   120  dfixull.o(x$fpl$llufromd)
    Region$$Table$$Base                      0x080027cc   Number         0  anon$$obj.o(Region$$Table)
    __I$use$fp                               0x080027cc   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Limit                     0x080027ec   Number         0  anon$$obj.o(Region$$Table)
    uwTick                                   0x20000000   Data           4  main.o(.data)
    SystemCoreClock                          0x20000008   Data           4  system_stm32f4xx.o(.data)
    AHBPrescTable                            0x2000000c   Data          16  system_stm32f4xx.o(.data)
    g_systick_counter                        0x2000001c   Data           4  systick.o(.data)
    g_system_uptime_ms                       0x20000020   Data           4  systick.o(.data)
    g_systick_cal                            0x20000024   Data          12  systick.o(.data)
    g_systick_stats                          0x200000b0   Data          16  systick.o(.bss)
    __libspace_start                         0x200000c0   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000120   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00002864, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000027ec, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f40_41xxx.o
    0x08000188   0x08000188   0x00000008   Code   RO         1467  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         1682    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000001a   Code   RO         1684    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001de   0x080001de   0x00000002   PAD
    0x080001e0   0x080001e0   0x0000001c   Code   RO         1686    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001fc   0x080001fc   0x00000002   Code   RO         1555    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001fe   0x080001fe   0x00000004   Code   RO         1558    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1561    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1564    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1566    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1568    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1571    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1573    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1575    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1577    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1579    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1581    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1583    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1585    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1587    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1589    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1591    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1595    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1597    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1599    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         1601    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000002   Code   RO         1602    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000204   0x08000204   0x00000002   Code   RO         1622    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000206   0x08000206   0x00000000   Code   RO         1635    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000206   0x08000206   0x00000000   Code   RO         1637    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000206   0x08000206   0x00000000   Code   RO         1640    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x08000206   0x08000206   0x00000000   Code   RO         1643    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x08000206   0x08000206   0x00000000   Code   RO         1645    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000206   0x08000206   0x00000000   Code   RO         1648    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x08000206   0x08000206   0x00000002   Code   RO         1649    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x08000208   0x08000208   0x00000000   Code   RO         1511    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000208   0x08000208   0x00000000   Code   RO         1524    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000208   0x08000208   0x00000006   Code   RO         1536    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800020e   0x0800020e   0x00000000   Code   RO         1526    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800020e   0x0800020e   0x00000004   Code   RO         1527    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         1529    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000212   0x08000212   0x00000008   Code   RO         1530    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800021a   0x0800021a   0x00000002   Code   RO         1556    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x0800021c   0x0800021c   0x00000000   Code   RO         1604    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x0800021c   0x0800021c   0x00000004   Code   RO         1605    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000220   0x08000220   0x00000006   Code   RO         1606    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000226   0x08000226   0x00000002   PAD
    0x08000228   0x08000228   0x00000040   Code   RO            4    .text               startup_stm32f40_41xxx.o
    0x08000268   0x08000268   0x00000064   Code   RO         1463    .text               c_w.l(rt_memcpy_w.o)
    0x080002cc   0x080002cc   0x00000006   Code   RO         1465    .text               c_w.l(heapauxi.o)
    0x080002d2   0x080002d2   0x00000016   Code   RO         1512    .text               c_w.l(_rserrno.o)
    0x080002e8   0x080002e8   0x00000008   Code   RO         1541    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x080002f0   0x080002f0   0x00000008   Code   RO         1543    .text               c_w.l(libspace.o)
    0x080002f8   0x080002f8   0x0000004a   Code   RO         1546    .text               c_w.l(sys_stackheap_outer.o)
    0x08000342   0x08000342   0x00000012   Code   RO         1548    .text               c_w.l(exit.o)
    0x08000354   0x08000354   0x0000000c   Code   RO         1614    .text               c_w.l(sys_exit.o)
    0x08000360   0x08000360   0x00000002   Code   RO         1625    .text               c_w.l(use_no_semi.o)
    0x08000362   0x08000362   0x00000000   Code   RO         1627    .text               c_w.l(indicate_semi.o)
    0x08000362   0x08000362   0x00000002   PAD
    0x08000364   0x08000364   0x00000050   Code   RO         1294    i.AD9854_ApplyCurrentParams  ad9854.o
    0x080003b4   0x080003b4   0x000000f4   Code   RO         1295    i.AD9854_CalculateModelCircuitGain  ad9854.o
    0x080004a8   0x080004a8   0x00000084   Code   RO         1296    i.AD9854_CalculateRequiredOutput  ad9854.o
    0x0800052c   0x0800052c   0x00000044   Code   RO         1297    i.AD9854_CalculateRequiredOutputWithModel  ad9854.o
    0x08000570   0x08000570   0x00000014   Code   RO         1298    i.AD9854_Delay_us   ad9854.o
    0x08000584   0x08000584   0x0000008c   Code   RO         1299    i.AD9854_EnableModelCircuit  ad9854.o
    0x08000610   0x08000610   0x00000020   Code   RO         1300    i.AD9854_EnableOutput  ad9854.o
    0x08000630   0x08000630   0x0000006c   Code   RO         1301    i.AD9854_FrequencyToWord  ad9854.o
    0x0800069c   0x0800069c   0x000000a8   Code   RO         1302    i.AD9854_GPIO_Init  ad9854.o
    0x08000744   0x08000744   0x0000001c   Code   RO         1303    i.AD9854_GetControlParams  ad9854.o
    0x08000760   0x08000760   0x0000005c   Code   RO         1304    i.AD9854_Init       ad9854.o
    0x080007bc   0x080007bc   0x0000001c   Code   RO         1305    i.AD9854_InitControlInterface  ad9854.o
    0x080007d8   0x080007d8   0x00000024   Code   RO         1307    i.AD9854_Reset      ad9854.o
    0x080007fc   0x080007fc   0x00000036   Code   RO         1308    i.AD9854_SetAmplitude  ad9854.o
    0x08000832   0x08000832   0x00000002   PAD
    0x08000834   0x08000834   0x00000064   Code   RO         1309    i.AD9854_SetFrequency  ad9854.o
    0x08000898   0x08000898   0x000000b8   Code   RO         1311    i.AD9854_SetTargetAmplitude  ad9854.o
    0x08000950   0x08000950   0x000000a4   Code   RO         1312    i.AD9854_SetTargetFrequency  ad9854.o
    0x080009f4   0x080009f4   0x00000020   Code   RO           13    i.AD9854_System_Init  main.o
    0x08000a14   0x08000a14   0x00000024   Code   RO         1313    i.AD9854_Update     ad9854.o
    0x08000a38   0x08000a38   0x0000010c   Code   RO         1314    i.AD9854_ValidateParams  ad9854.o
    0x08000b44   0x08000b44   0x0000005c   Code   RO         1315    i.AD9854_VppToAmplitudeCode  ad9854.o
    0x08000ba0   0x08000ba0   0x00000028   Code   RO         1316    i.AD9854_WriteFrequencyWord  ad9854.o
    0x08000bc8   0x08000bc8   0x00000080   Code   RO         1317    i.AD9854_WriteReg   ad9854.o
    0x08000c48   0x08000c48   0x00000034   Code   RO          347    i.BSP_Init          bsp.o
    0x08000c7c   0x08000c7c   0x00000004   Code   RO          211    i.BusFault_Handler  stm32f4xx_it.o
    0x08000c80   0x08000c80   0x0000005c   Code   RO         1155    i.DWT_Init          systick.o
    0x08000cdc   0x08000cdc   0x00000002   Code   RO          212    i.DebugMon_Handler  stm32f4xx_it.o
    0x08000cde   0x08000cde   0x00000002   PAD
    0x08000ce0   0x08000ce0   0x0000002c   Code   RO         1156    i.Delay_ms          systick.o
    0x08000d0c   0x08000d0c   0x00000008   Code   RO          213    i.EXTI0_IRQHandler  stm32f4xx_it.o
    0x08000d14   0x08000d14   0x00000002   Code   RO           14    i.EXTI0_IRQHandler_Internal  main.o
    0x08000d16   0x08000d16   0x00000002   PAD
    0x08000d18   0x08000d18   0x00000018   Code   RO           15    i.Error_Handler     main.o
    0x08000d30   0x08000d30   0x00000074   Code   RO          931    i.FLASH_SetLatency  stm32f4xx_flash.o
    0x08000da4   0x08000da4   0x000001a8   Code   RO          446    i.GPIO_Init         stm32f4xx_gpio.o
    0x08000f4c   0x08000f4c   0x0000010c   Code   RO          452    i.GPIO_ReadOutputDataBit  stm32f4xx_gpio.o
    0x08001058   0x08001058   0x000000b0   Code   RO          453    i.GPIO_ResetBits    stm32f4xx_gpio.o
    0x08001108   0x08001108   0x000000b0   Code   RO          454    i.GPIO_SetBits      stm32f4xx_gpio.o
    0x080011b8   0x080011b8   0x000000a4   Code   RO          456    i.GPIO_ToggleBits   stm32f4xx_gpio.o
    0x0800125c   0x0800125c   0x00000004   Code   RO          214    i.HardFault_Handler  stm32f4xx_it.o
    0x08001260   0x08001260   0x00000004   Code   RO          215    i.MemManage_Handler  stm32f4xx_it.o
    0x08001264   0x08001264   0x00000002   Code   RO          216    i.NMI_Handler       stm32f4xx_it.o
    0x08001266   0x08001266   0x00000002   PAD
    0x08001268   0x08001268   0x00000028   Code   RO         1159    i.NVIC_SetPriority  systick.o
    0x08001290   0x08001290   0x00000002   Code   RO          217    i.PendSV_Handler    stm32f4xx_it.o
    0x08001292   0x08001292   0x00000002   PAD
    0x08001294   0x08001294   0x00000068   Code   RO          553    i.RCC_AHB1PeriphClockCmd  stm32f4xx_rcc.o
    0x080012fc   0x080012fc   0x00000064   Code   RO          562    i.RCC_APB1PeriphClockCmd  stm32f4xx_rcc.o
    0x08001360   0x08001360   0x00000068   Code   RO          565    i.RCC_APB2PeriphClockCmd  stm32f4xx_rcc.o
    0x080013c8   0x080013c8   0x00000064   Code   RO          573    i.RCC_DeInit        stm32f4xx_rcc.o
    0x0800142c   0x0800142c   0x000000a4   Code   RO          575    i.RCC_GetFlagStatus  stm32f4xx_rcc.o
    0x080014d0   0x080014d0   0x00000010   Code   RO          577    i.RCC_GetSYSCLKSource  stm32f4xx_rcc.o
    0x080014e0   0x080014e0   0x00000060   Code   RO          578    i.RCC_HCLKConfig    stm32f4xx_rcc.o
    0x08001540   0x08001540   0x00000044   Code   RO          579    i.RCC_HSEConfig     stm32f4xx_rcc.o
    0x08001584   0x08001584   0x00000058   Code   RO          589    i.RCC_PCLK1Config   stm32f4xx_rcc.o
    0x080015dc   0x080015dc   0x00000058   Code   RO          590    i.RCC_PCLK2Config   stm32f4xx_rcc.o
    0x08001634   0x08001634   0x00000038   Code   RO          591    i.RCC_PLLCmd        stm32f4xx_rcc.o
    0x0800166c   0x0800166c   0x000000b8   Code   RO          592    i.RCC_PLLConfig     stm32f4xx_rcc.o
    0x08001724   0x08001724   0x00000048   Code   RO          603    i.RCC_SYSCLKConfig  stm32f4xx_rcc.o
    0x0800176c   0x0800176c   0x00000038   Code   RO          605    i.RCC_WaitForHSEStartUp  stm32f4xx_rcc.o
    0x080017a4   0x080017a4   0x00000002   Code   RO          218    i.SVC_Handler       stm32f4xx_it.o
    0x080017a6   0x080017a6   0x00000002   PAD
    0x080017a8   0x080017a8   0x000000ec   Code   RO          306    i.SetSysClock       system_stm32f4xx.o
    0x08001894   0x08001894   0x0000001c   Code   RO          219    i.SysTick_Handler   stm32f4xx_it.o
    0x080018b0   0x080018b0   0x00000038   Code   RO         1166    i.SysTick_Handler_Internal  systick.o
    0x080018e8   0x080018e8   0x0000007c   Code   RO         1167    i.SysTick_Init      systick.o
    0x08001964   0x08001964   0x00000020   Code   RO         1170    i.SysTick_ResetStats  systick.o
    0x08001984   0x08001984   0x00000060   Code   RO         1172    i.SysTick_UpdateStats  systick.o
    0x080019e4   0x080019e4   0x0000006a   Code   RO           16    i.SystemClock_Config  main.o
    0x08001a4e   0x08001a4e   0x00000002   PAD
    0x08001a50   0x08001a50   0x00000068   Code   RO          308    i.SystemInit        system_stm32f4xx.o
    0x08001ab8   0x08001ab8   0x00000002   Code   RO          220    i.TIM6_DAC_IRQHandler  stm32f4xx_it.o
    0x08001aba   0x08001aba   0x00000002   Code   RO           17    i.TimingDelay_Decrement  main.o
    0x08001abc   0x08001abc   0x00000004   Code   RO          221    i.UsageFault_Handler  stm32f4xx_it.o
    0x08001ac0   0x08001ac0   0x0000007a   Code   RO         1499    i.__hardfp_sqrt     m_wm.l(sqrt.o)
    0x08001b3a   0x08001b3a   0x00000004   Code   RO           18    i.assert_failed     main.o
    0x08001b3e   0x08001b3e   0x00000002   PAD
    0x08001b40   0x08001b40   0x00000130   Code   RO           19    i.main              main.o
    0x08001c70   0x08001c70   0x00000150   Code   RO         1469    x$fpl$dadd          fz_wm.l(daddsub_clz.o)
    0x08001dc0   0x08001dc0   0x00000018   Code   RO         1514    x$fpl$dcmpinf       fz_wm.l(dcmpi.o)
    0x08001dd8   0x08001dd8   0x000002b0   Code   RO         1476    x$fpl$ddiv          fz_wm.l(ddiv.o)
    0x08002088   0x08002088   0x0000005a   Code   RO         1479    x$fpl$dfixu         fz_wm.l(dfixu.o)
    0x080020e2   0x080020e2   0x00000002   PAD
    0x080020e4   0x080020e4   0x00000078   Code   RO         1493    x$fpl$dleqf         fz_wm.l(dleqf.o)
    0x0800215c   0x0800215c   0x00000154   Code   RO         1495    x$fpl$dmul          fz_wm.l(dmul.o)
    0x080022b0   0x080022b0   0x0000009c   Code   RO         1516    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x0800234c   0x0800234c   0x0000000c   Code   RO         1518    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x08002358   0x08002358   0x0000006c   Code   RO         1497    x$fpl$drleqf        fz_wm.l(drleqf.o)
    0x080023c4   0x080023c4   0x00000016   Code   RO         1470    x$fpl$drsb          fz_wm.l(daddsub_clz.o)
    0x080023da   0x080023da   0x00000002   PAD
    0x080023dc   0x080023dc   0x00000198   Code   RO         1520    x$fpl$dsqrt         fz_wm.l(dsqrt_umaal.o)
    0x08002574   0x08002574   0x000001d4   Code   RO         1471    x$fpl$dsub          fz_wm.l(daddsub_clz.o)
    0x08002748   0x08002748   0x0000000a   Code   RO         1612    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x08002752   0x08002752   0x00000002   PAD
    0x08002754   0x08002754   0x00000078   Code   RO         1483    x$fpl$llufromd      fz_wm.l(dfixull.o)
    0x080027cc   0x080027cc   0x00000000   Code   RO         1522    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x080027cc   0x080027cc   0x00000020   Data   RO         1680    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x10000000, Load base: 0x08002864, Size: 0x00000000, Max: 0x00010000, ABSOLUTE)

    **** No section assigned to this execution region ****


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080027ec, Size: 0x00000720, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x080027ec   0x00000005   Data   RW           21    .data               main.o
    0x20000005   0x080027f1   0x00000003   PAD
    0x20000008   0x080027f4   0x00000014   Data   RW          309    .data               system_stm32f4xx.o
    0x2000001c   0x08002808   0x00000019   Data   RW         1174    .data               systick.o
    0x20000035   0x08002821   0x00000003   PAD
    0x20000038   0x08002824   0x00000040   Data   RW         1318    .data               ad9854.o
    0x20000078        -       0x00000038   Zero   RW           20    .bss                main.o
    0x200000b0        -       0x00000010   Zero   RW         1173    .bss                systick.o
    0x200000c0        -       0x00000060   Zero   RW         1544    .bss                c_w.l(libspace.o)
    0x20000120        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f40_41xxx.o
    0x20000320        -       0x00000400   Zero   RW            1    STACK               startup_stm32f40_41xxx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      2242        266          0         64          0      17780   ad9854.o
        52          0          0          0          0        451   bsp.o
       474         46          0          5         56      18954   main.o
         0          0          0          0          0     236692   misc.o
        64         26        392          0       1536        844   startup_stm32f40_41xxx.o
       116         32          0          0          0        603   stm32f4xx_flash.o
      1208        362          0          0          0      10791   stm32f4xx_gpio.o
        62          6          0          0          0       4859   stm32f4xx_it.o
      1296        358          0          0          0      25515   stm32f4xx_rcc.o
       340         32          0         20          0       1689   system_stm32f4xx.o
       484         76          0         25         16      31873   systick.o

    ----------------------------------------------------------------------
      6354       <USER>        <GROUP>        120       1608     350051   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        16          0          0          6          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        22          0          0          0          0        100   _rserrno.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         6          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
       826         16          0          0          0        492   daddsub_clz.o
        24          0          0          0          0        116   dcmpi.o
       688        140          0          0          0        256   ddiv.o
        90          4          0          0          0        140   dfixu.o
       120          4          0          0          0        152   dfixull.o
       120          4          0          0          0        140   dleqf.o
       340         12          0          0          0        152   dmul.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
       108          0          0          0          0        128   drleqf.o
       408         56          0          0          0        168   dsqrt_umaal.o
        10          0          0          0          0        116   fpinit.o
         0          0          0          0          0          0   usenofp.o
       122          0          0          0          0        148   sqrt.o

    ----------------------------------------------------------------------
      3442        <USER>          <GROUP>          0         96       3096   Library Totals
        12          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       406         20          0          0         96        832   c_w.l
      2902        240          0          0          0       2116   fz_wm.l
       122          0          0          0          0        148   m_wm.l

    ----------------------------------------------------------------------
      3442        <USER>          <GROUP>          0         96       3096   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      9796       1464        424        120       1704     345035   Grand Totals
      9796       1464        424        120       1704     345035   ELF Image Totals
      9796       1464        424        120          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                10220 (   9.98kB)
    Total RW  Size (RW Data + ZI Data)              1824 (   1.78kB)
    Total ROM Size (Code + RO Data + RW Data)      10340 (  10.10kB)

==============================================================================

