/**
  ******************************************************************************
  * @file    parallel_adc.h
  * <AUTHOR>
  * @version V1.0
  * @date    2024
  * @brief   高速并行ADC接口模块 - 竞赛级实现
  *          支持外部高速ADC(如LTC2248)的并行数据采集
  ******************************************************************************
  * @attention
  * 
  * 本模块特性：
  * 1. 14位并行数据接口 (LTC2246)
  * 2. 外部时钟同步采集
  * 3. 高速数据捕获和缓存
  * 4. 基础数据缓冲管理
  * 5. 时序同步机制
  * 6. 缓冲区溢出保护
  *
  * 硬件连接：
  * - ADC型号: LTC2246 (14位高速ADC)
  * - 数据线 D0-D13: PC0-PC13 (14位数据)
  * - 时钟线 CLK_OUT: PA0 (外部中断EXTI0)
  * - 数据有效 DV: PA1 (可选)
  * - 溢出指示 OVR: PA2 (可选)
  *
  ******************************************************************************
  */

#ifndef __PARALLEL_ADC_H
#define __PARALLEL_ADC_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include <stdint.h>
#include <stdbool.h>

/* Exported types ------------------------------------------------------------*/

/**
  * @brief  并行ADC配置结构体
  */
typedef struct {
    uint32_t max_sample_rate;    ///< 最大采样率(Hz) - 保守估计
    uint16_t data_width;         ///< 数据位宽(固定14位)
    bool enable_data_valid;      ///< 使能数据有效信号
    bool enable_overflow_detect; ///< 使能溢出检测
    uint8_t trigger_edge;        ///< 触发边沿(上升沿/下降沿)
} ParallelADC_Config_t;

/**
  * @brief  并行ADC状态枚举
  */
typedef enum {
    PARALLEL_ADC_STATE_IDLE = 0, ///< 空闲状态
    PARALLEL_ADC_STATE_RUNNING,  ///< 运行状态
    PARALLEL_ADC_STATE_FULL,     ///< 缓冲区满
    PARALLEL_ADC_STATE_ERROR     ///< 错误状态
} ParallelADC_State_t;

/**
  * @brief  并行ADC统计信息结构体
  */
typedef struct {
    uint32_t total_samples;      ///< 总采样数
    uint32_t valid_samples;      ///< 有效采样数
    uint32_t overflow_count;     ///< 溢出次数
    uint32_t error_count;        ///< 错误次数
    float actual_sample_rate;    ///< 实际采样率
    uint32_t max_sample_rate;    ///< 峰值采样率
    uint16_t max_value;          ///< 最大值
    uint16_t min_value;          ///< 最小值
    uint32_t avg_value;          ///< 平均值
} ParallelADC_Stats_t;

/**
  * @brief  并行ADC缓冲区结构体
  */
typedef struct {
    uint16_t* buffer;            ///< 数据缓冲区
    uint32_t size;               ///< 缓冲区大小
    volatile uint32_t head;      ///< 写指针
    volatile uint32_t tail;      ///< 读指针
    volatile uint32_t count;     ///< 数据计数
    volatile bool full;          ///< 缓冲区满标志
    volatile bool overflow;      ///< 溢出标志
} ParallelADC_Buffer_t;

/**
  * @brief  并行ADC句柄结构体
  */
typedef struct {
    GPIO_TypeDef* data_port;     ///< 数据端口
    GPIO_TypeDef* clock_port;    ///< 时钟端口
    uint16_t clock_pin;          ///< 时钟引脚
    ParallelADC_Config_t config; ///< 配置参数
    ParallelADC_Buffer_t buffer; ///< 数据缓冲区
    ParallelADC_Stats_t stats;   ///< 统计信息
    volatile ParallelADC_State_t state; ///< 当前状态
    volatile uint32_t last_sample_time;  ///< 上次采样时间
} ParallelADC_Handle_t;

/* Exported constants --------------------------------------------------------*/

/** @defgroup ParallelADC_Exported_Constants 并行ADC导出常量
  * @{
  */

#define PARALLEL_ADC_BUFFER_SIZE        4096U    ///< 缓冲区大小
#define PARALLEL_ADC_MAX_SAMPLE_RATE    10000000U ///< 保守最大采样率(10MSPS)
#define PARALLEL_ADC_MIN_SAMPLE_RATE    1000U    ///< 最小采样率(1KSPS)

#define PARALLEL_ADC_DATA_MASK_14BIT    0x3FFFU  ///< 14位数据掩码(LTC2246)
#define PARALLEL_ADC_DATA_MASK_12BIT    0x0FFFU  ///< 12位数据掩码
#define PARALLEL_ADC_DATA_MASK_10BIT    0x03FFU  ///< 10位数据掩码
#define PARALLEL_ADC_DATA_MASK_8BIT     0x00FFU  ///< 8位数据掩码

#define PARALLEL_ADC_TRIGGER_RISING     0        ///< 上升沿触发
#define PARALLEL_ADC_TRIGGER_FALLING    1        ///< 下降沿触发

/**
  * @}
  */

/* Exported macro ------------------------------------------------------------*/

/** @defgroup ParallelADC_Exported_Macros 并行ADC导出宏
  * @{
  */

/**
  * @brief  读取并行数据 (14位LTC2246)
  */
#define PARALLEL_ADC_READ_DATA()        (GPIOC->IDR & PARALLEL_ADC_DATA_MASK_14BIT)

/**
  * @brief  检查数据有效信号
  */
#define PARALLEL_ADC_IS_DATA_VALID()    (GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_1))

/**
  * @brief  检查溢出信号
  */
#define PARALLEL_ADC_IS_OVERFLOW()      (GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_2))

/**
  * @brief  检查缓冲区是否为空
  */
#define PARALLEL_ADC_IS_BUFFER_EMPTY(handle) ((handle)->buffer.count == 0)

/**
  * @brief  检查缓冲区是否已满
  */
#define PARALLEL_ADC_IS_BUFFER_FULL(handle)  ((handle)->buffer.full)

/**
  * @brief  获取缓冲区可用空间
  */
#define PARALLEL_ADC_GET_FREE_SPACE(handle)  ((handle)->buffer.size - (handle)->buffer.count)

/**
  * @brief  获取缓冲区数据长度
  */
#define PARALLEL_ADC_GET_DATA_COUNT(handle)  ((handle)->buffer.count)

/**
  * @}
  */

/* Exported variables --------------------------------------------------------*/

/** @defgroup ParallelADC_Exported_Variables 并行ADC导出变量
  * @{
  */

extern ParallelADC_Handle_t g_parallel_adc_handle;  ///< 并行ADC句柄
extern volatile bool g_parallel_adc_data_ready;     ///< 数据就绪标志
extern volatile bool g_parallel_adc_overflow;       ///< 溢出标志

// 黄金参考全局变量
extern volatile uint16_t g_adc_buffer[];            ///< ADC数据缓冲区
extern volatile uint32_t g_adc_buffer_index;        ///< 缓冲区索引
extern volatile uint8_t g_adc_capture_finished;     ///< 采集完成标志

/**
  * @}
  */

/* Exported functions --------------------------------------------------------*/

/** @defgroup ParallelADC_Exported_Functions 并行ADC导出函数
  * @{
  */

/**
  * @brief  并行ADC初始化
  * @param  config: 配置参数指针
  * @retval 0: 成功, -1: 失败
  */
int8_t ParallelADC_Init(ParallelADC_Config_t* config);

/**
  * @brief  并行ADC反初始化
  * @param  None
  * @retval None
  */
void ParallelADC_DeInit(void);

/**
  * @brief  开始数据采集
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t ParallelADC_Start(void);

/**
  * @brief  停止数据采集
  * @param  None
  * @retval None
  */
void ParallelADC_Stop(void);

/**
  * @brief  读取单个数据
  * @param  data: 数据指针
  * @retval 0: 成功, -1: 无数据
  */
int8_t ParallelADC_ReadSingle(uint16_t* data);

/**
  * @brief  读取多个数据
  * @param  buffer: 数据缓冲区
  * @param  length: 期望读取长度
  * @retval 实际读取的数据长度
  */
uint32_t ParallelADC_ReadMultiple(uint16_t* buffer, uint32_t length);

/**
  * @brief  获取缓冲区状态
  * @param  None
  * @retval 当前状态
  */
ParallelADC_State_t ParallelADC_GetState(void);

/**
  * @brief  清空缓冲区
  * @param  None
  * @retval None
  */
void ParallelADC_ClearBuffer(void);

/**
  * @brief  获取统计信息
  * @param  stats: 统计信息结构体指针
  * @retval None
  */
void ParallelADC_GetStats(ParallelADC_Stats_t* stats);

/**
  * @brief  重置统计信息
  * @param  None
  * @retval None
  */
void ParallelADC_ResetStats(void);

/**
  * @brief  设置触发模式
  * @param  edge: 触发边沿
  * @retval 0: 成功, -1: 失败
  */
int8_t ParallelADC_SetTriggerEdge(uint8_t edge);

/**
  * @brief  获取实时采样率
  * @param  None
  * @retval 当前采样率(Hz)
  */
float ParallelADC_GetSampleRate(void);

/**
  * @brief  EXTI0中断处理函数(时钟中断)
  * @param  None
  * @retval None
  */
void EXTI0_IRQHandler(void);

/**
  * @brief  EXTI0中断处理函数内部逻辑
  * @param  None
  * @retval None
  * @note   由stm32f4xx_it.c中的EXTI0_IRQHandler调用
  */
void EXTI0_IRQHandler_Internal(void);

/**
  * @}
  */

#ifdef __cplusplus
}
#endif

#endif /* __PARALLEL_ADC_H */

/************************ (C) COPYRIGHT 嵌入式竞赛团队 *****END OF FILE****/
