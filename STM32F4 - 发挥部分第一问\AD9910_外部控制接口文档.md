# AD9910外部控制接口文档

## 🎯 概述

本文档详细描述AD9910 DDS信号发生器的外部控制接口，支持串口屏和4x4矩阵键盘等外部设备对信号参数进行实时控制。

### 🔧 核心功能
- **实时频率控制**: 1Hz - 420MHz，精度±0.001%
- **实时峰峰值控制**: 考虑后续电路增益的精确控制
- **增益计算算法**: 自动补偿频率、温度、非线性失真
- **预设参数管理**: 8组预设配置，快速切换
- **多设备支持**: 串口屏、4x4矩阵键盘统一接口

## 📋 系统架构

```
外部设备 -> 命令解析器 -> 控制接口 -> 参数管理 -> AD9910驱动 -> 信号输出
    ↓           ↓           ↓          ↓           ↓
串口屏/键盘  命令格式化   参数验证   增益计算    高精度DDS
```

## 🔌 支持的外部设备

### 1. 串口屏控制 (UART)
- **通信协议**: UART，9600-115200 bps
- **命令格式**: 文本协议，以\r\n结尾
- **响应格式**: 状态码 + 消息 + 数据

### 2. 4x4矩阵键盘控制 (GPIO)
- **扫描方式**: GPIO扫描，支持按键防抖
- **操作模式**: 直接控制 + 数值输入模式
- **显示反馈**: 可配合OLED显示屏使用

## 📡 串口控制协议

### 命令格式
```
命令名:参数\r\n
```

### 支持的命令

#### 1. 频率控制
```bash
# 设置频率 (Hz)
SET_FREQ:5000000\r\n        # 设置5MHz
SET_FREQ:1000000\r\n        # 设置1MHz

# 响应
OK:FREQ_SET:5000000\r\n     # 成功
ERROR:OUT_OF_RANGE\r\n      # 频率超出范围
```

#### 2. 峰峰值控制
```bash
# 设置目标峰峰值 (mV，考虑增益)
SET_AMP:500\r\n             # 设置0.5V峰峰值
SET_AMP:1000\r\n            # 设置1V峰峰值

# 响应
OK:AMP_SET:500\r\n          # 成功
ERROR:OUT_OF_RANGE\r\n      # 幅度超出范围
```

#### 3. 增益控制
```bash
# 设置增益系数
SET_GAIN:1.5\r\n            # 设置1.5倍增益
SET_GAIN:2.0\r\n            # 设置2倍增益

# 响应
OK:GAIN_SET:1.5\r\n         # 成功
ERROR:INVALID_PARAM\r\n     # 增益无效
```

#### 4. 输出控制
```bash
# 使能输出
ENABLE_OUT\r\n
# 响应: OK:OUTPUT_ENABLED\r\n

# 禁用输出
DISABLE_OUT\r\n
# 响应: OK:OUTPUT_DISABLED\r\n
```

#### 5. 状态查询
```bash
# 获取当前状态
GET_STATUS\r\n

# 响应
STATUS:FREQ:5000000:AMP:500:GAIN:1.0:OUT:1\r\n
# 格式: STATUS:FREQ:频率:AMP:幅度:GAIN:增益:OUT:输出状态
```

#### 6. 预设管理
```bash
# 加载预设 (0-7)
LOAD_PRESET:0\r\n           # 加载预设0 (5MHz/0.5V)
LOAD_PRESET:1\r\n           # 加载预设1 (1MHz/1V)

# 响应
OK:PRESET_LOADED:0\r\n      # 成功
ERROR:INVALID_PARAM\r\n     # 预设编号无效
```

#### 7. 系统控制
```bash
# 系统复位
RESET\r\n
# 响应: OK:SYSTEM_RESET\r\n

# 获取参数范围
GET_RANGES\r\n
# 响应: RANGES:FREQ:1:420000000:AMP:10:5000:GAIN:0.1:10.0\r\n
```

## ⌨️ 键盘控制协议

### 4x4矩阵键盘布局
```
[1] [2] [3] [A]  ->  [FREQ+] [AMP+]  [PRESET1] [MENU]
[4] [5] [6] [B]  ->  [FREQ-] [AMP-]  [PRESET2] [ENTER]
[7] [8] [9] [C]  ->  [GAIN+] [GAIN-] [PRESET3] [ESC]
[*] [0] [#] [D]  ->  [OUT]   [RESET] [PRESET4] [INFO]
```

### 按键功能说明

#### 基本控制
- **FREQ+/FREQ-**: 频率步进调节 (±100kHz)
- **AMP+/AMP-**: 幅度步进调节 (±50mV)
- **GAIN+/GAIN-**: 增益步进调节 (±0.1)
- **OUT**: 输出开关切换

#### 预设功能
- **PRESET1-4**: 快速加载预设参数
- **RESET**: 系统复位到默认状态

#### 菜单功能
- **MENU**: 进入菜单模式
- **ENTER**: 确认当前操作
- **ESC**: 取消当前操作
- **INFO**: 显示当前状态信息

### 键盘操作模式

#### 1. 直接控制模式 (默认)
- 按键直接执行对应功能
- 立即生效，无需确认

#### 2. 数值输入模式
- 通过数字键输入精确数值
- 需要ENTER确认或ESC取消

## 🔢 参数范围和限制

### 频率参数
- **范围**: 1Hz - 420MHz
- **分辨率**: 0.23Hz
- **精度**: ±0.001%
- **步进**: 键盘模式100kHz，串口模式任意

### 峰峰值参数
- **AD9910输出**: 10mV - 800mV
- **目标输出**: 10mV - 5V (考虑增益)
- **分辨率**: 0.049mV/步
- **精度**: ±0.01%

### 增益参数
- **范围**: 0.1 - 10.0
- **分辨率**: 0.001
- **类型**: 后续电路总增益系数

## 🧮 增益计算算法

### 增益链路
```
总增益 = 缓冲器增益 × 可变增益 × 滤波器增益 × 频率校正 × 温度校正 × 非线性校正
```

### 校正算法

#### 频率校正
```c
频率校正系数 = 1 + (-10ppm/MHz) × (频率 - 1MHz)
```

#### 温度校正
```c
温度校正系数 = 1 + (50ppm/°C) × (温度 - 25°C)
```

#### 非线性校正
- **低幅度** (<100mV): +2%补偿
- **高幅度** (>3V): -2%补偿
- **中等幅度**: 无补偿

### 计算示例
```c
// 目标: 输出1V峰峰值，5MHz，25°C
// 增益设置: 2.0倍

频率校正 = 1 + (-10e-6) × (5000000 - 1000000) = 0.96
温度校正 = 1 + (50e-6) × (25 - 25) = 1.0
总增益 = 1.0 × 2.0 × 0.95 × 0.96 × 1.0 × 1.0 = 1.824

AD9910输出 = 1000mV / 1.824 = 548mV
```

## 📋 预设参数配置

### 默认预设
| 编号 | 名称 | 频率 | 目标幅度 | 增益 | 说明 |
|------|------|------|----------|------|------|
| 0 | 5MHz_0.5V | 5MHz | 500mV | 1.0 | 标准测试信号 |
| 1 | 1MHz_1V | 1MHz | 1000mV | 1.0 | 低频大幅度 |
| 2 | 10MHz_0.3V | 10MHz | 300mV | 1.0 | 高频小幅度 |
| 3 | 用户自定义 | - | - | - | 可自定义 |
| 4-7 | 保留 | - | - | - | 后续扩展 |

## 🔧 集成示例

### 串口屏集成
```c
// 串口接收中断处理
void UART_IRQHandler(void)
{
    uint8_t data = UART_ReceiveData();
    CommandParser_AddUARTData(&data, 1);
}

// 主循环处理
void main_loop(void)
{
    ParsedCommand_t command;
    if (CommandParser_ProcessPendingUART(&command) == PARSE_STATUS_OK) {
        ControlStatus_t status = AD9910_Control_Execute(
            command.command, command.param1, command.param2);
        
        // 生成响应
        CommandResponse_t response;
        CommandParser_GenerateResponse(&command, status, &response);
        
        // 发送响应
        char output[256];
        uint16_t len = CommandParser_FormatUARTResponse(&response, output, sizeof(output));
        UART_SendData(output, len);
    }
}
```

### 4x4键盘集成
```c
// 键盘扫描处理
void keypad_scan(void)
{
    uint8_t key = scan_keypad();
    if (key != 0) {
        ParsedCommand_t command;
        if (CommandParser_ParseKeypad(key, &command) == PARSE_STATUS_OK) {
            ControlStatus_t status = AD9910_Control_Execute(
                command.command, command.param1, command.param2);
            
            // 显示结果
            display_status(status);
        }
    }
}
```

## ⚠️ 注意事项

### 安全限制
1. **频率范围**: 严格限制在1Hz-420MHz
2. **幅度保护**: 防止过驱动后续电路
3. **增益验证**: 防止增益设置导致输出超限

### 性能优化
1. **命令缓存**: 避免频繁设置相同参数
2. **批量更新**: 支持多参数同时设置
3. **响应优化**: 快速响应外部控制命令

### 兼容性
1. **向后兼容**: 保持API接口稳定
2. **扩展性**: 支持新增外部设备类型
3. **可配置**: 支持参数范围自定义

---

**文档版本**: V1.0  
**更新日期**: 2024-08-02  
**适用版本**: AD9910外部控制系统V3.0
