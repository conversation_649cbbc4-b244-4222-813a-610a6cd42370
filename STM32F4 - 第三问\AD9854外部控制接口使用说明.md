# AD9854外部控制接口使用说明

## 概述

本文档详细说明了AD9854 DDS信号发生器的外部控制接口设计和使用方法。系统支持串口屏和4x4矩阵键盘两种控制方式，可以实时调节频率、峰峰值，并考虑后续电路的增益补偿。

## 系统架构

### 控制层次结构
```
外部控制设备 (串口屏/矩阵键盘)
    ↓
外部控制接口 (external_control.c)
    ↓
AD9854控制层 (ad9854.c)
    ↓
AD9854硬件 (DDS芯片)
```

### 核心功能模块
1. **参数控制** - 频率、峰峰值、增益系数设置
2. **增益补偿** - 自动计算AD9854所需输出以达到目标峰峰值
3. **外部接口** - 串口屏和矩阵键盘控制
4. **参数存储** - Flash保存/加载配置

## 控制参数说明

### 可控参数范围

| 参数 | 范围 | 精度 | 说明 |
|------|------|------|------|
| 频率 | 1Hz ~ 150MHz | 48位 (0.0018Hz) | AD9854硬件限制 |
| 目标峰峰值 | 10mV ~ 5V | 12位 | 考虑后续电路增益 |
| 增益系数 | 0.01 ~ 100 | 浮点 | 后续电路电压增益 |

### 增益补偿算法

**核心公式**：
```
AD9854输出 = 目标峰峰值 / 增益系数
```

**示例**：
- 目标输出：2V峰峰值
- 后续电路增益：4倍
- AD9854需要输出：2V ÷ 4 = 500mV

## API接口说明

### 基础控制函数

```c
// 设置目标频率
AD9854_StatusTypeDef AD9854_SetTargetFrequency(double frequency_hz);

// 设置目标峰峰值 (考虑后续电路增益)
AD9854_StatusTypeDef AD9854_SetTargetAmplitude(double target_vpp_mv);

// 设置后续电路增益系数
AD9854_StatusTypeDef AD9854_SetGainFactor(double gain_factor);

// 使能/禁用输出
AD9854_StatusTypeDef AD9854_EnableOutput(uint8_t enable);
```

### 外部控制接口

```c
// 初始化控制接口
AD9854_StatusTypeDef AD9854_InitControlInterface(AD9854_ControlInterface_t interface_type);

// 处理外部控制命令
AD9854_StatusTypeDef AD9854_ProcessCommand(AD9854_Command_t command, double param1, double param2);

// 获取当前参数
AD9854_StatusTypeDef AD9854_GetControlParams(AD9854_ControlParams_t *params);
```

## 使用示例

### 基本参数设置

```c
// 初始化AD9854
AD9854_Init();

// 设置5MHz频率
AD9854_SetTargetFrequency(5000000.0);

// 设置后续电路增益为2倍
AD9854_SetGainFactor(2.0);

// 设置目标输出1V峰峰值 (AD9854将输出500mV)
AD9854_SetTargetAmplitude(1000.0);

// 使能输出
AD9854_EnableOutput(1);
```

### 命令控制方式

```c
// 通过命令接口设置参数
AD9854_ProcessCommand(CMD_SET_FREQUENCY, 10000000.0, 0);  // 10MHz
AD9854_ProcessCommand(CMD_SET_AMPLITUDE, 500.0, 0);       // 500mV
AD9854_ProcessCommand(CMD_SET_GAIN, 1.5, 0);              // 1.5倍增益
AD9854_ProcessCommand(CMD_ENABLE_OUTPUT, 1, 0);           // 使能输出
```

### 参数查询

```c
AD9854_ControlParams_t params;
AD9854_GetControlParams(&params);

printf("频率: %.0f Hz\n", params.frequency_hz);
printf("目标峰峰值: %.1f mV\n", params.target_vpp_mv);
printf("AD9854输出: %.1f mV\n", params.ad9854_vpp_mv);
printf("增益系数: %.2f\n", params.gain_factor);
printf("输出状态: %s\n", params.enable ? "使能" : "禁用");
```

## 串口屏控制

### 通信协议

**波特率**: 115200  
**数据位**: 8  
**停止位**: 1  
**校验位**: 无  

### 命令格式

| 命令 | 格式 | 示例 | 说明 |
|------|------|------|------|
| 设置频率 | `FREQ:value` | `FREQ:5000000` | 设置5MHz |
| 设置幅度 | `AMP:value` | `AMP:500` | 设置500mV |
| 设置增益 | `GAIN:value` | `GAIN:2.0` | 设置2倍增益 |
| 使能输出 | `EN:1` | `EN:1` | 使能输出 |
| 禁用输出 | `EN:0` | `EN:0` | 禁用输出 |
| 查询状态 | `STATUS?` | `STATUS?` | 查询当前状态 |

### 响应格式

```
// 状态响应
FREQ:5000000,AMP:500,GAIN:2.0,EN:1

// 错误响应
ERROR:01  // 参数超出范围
ERROR:02  // 硬件故障
ERROR:03  // 命令格式错误
```

## 矩阵键盘控制

### 键盘布局

```
[1] [2] [3] [A]
[4] [5] [6] [B]
[7] [8] [9] [C]
[*] [0] [#] [D]
```

### 按键功能

| 按键 | 功能 | 说明 |
|------|------|------|
| 0-9 | 数字输入 | 参数数值输入 |
| A | 确认 | 确认当前输入 |
| B | 取消 | 取消当前输入 |
| C | 菜单 | 进入/切换菜单 |
| D | 返回 | 返回上级菜单 |
| * | 小数点 | 输入小数点 |
| # | 单位切换 | 切换单位 (Hz/kHz/MHz) |

### 菜单结构

```
主菜单
├── 频率设置
│   ├── 直接输入频率值
│   └── 单位选择 (Hz/kHz/MHz)
├── 幅度设置
│   ├── 直接输入幅度值
│   └── 单位选择 (mV/V)
├── 增益设置
│   └── 输入增益系数
├── 输出控制
│   ├── 使能输出
│   └── 禁用输出
├── 系统信息
│   ├── 当前参数显示
│   └── 系统状态
└── 保存/加载
    ├── 保存当前配置
    └── 加载预设配置
```

## 参数存储

### Flash存储区域

- **起始地址**: 0x08060000 (扇区7)
- **存储容量**: 128KB
- **预设数量**: 8个
- **每个预设**: 64字节

### 预设配置格式

```c
typedef struct {
    char name[16];               // 配置名称
    double frequency_hz;         // 频率
    double target_vpp_mv;        // 目标峰峰值
    double gain_factor;          // 增益系数
    uint8_t enable;              // 使能标志
    uint8_t reserved[39];        // 保留字节
} ParamPreset_t;
```

### 默认预设

| 索引 | 名称 | 频率 | 峰峰值 | 增益 |
|------|------|------|--------|------|
| 0 | 5MHz_500mV | 5MHz | 500mV | 1.0 |
| 1 | 1MHz_1V | 1MHz | 1V | 1.0 |
| 2 | 10MHz_250mV | 10MHz | 250mV | 1.0 |
| 3 | 100kHz_2V | 100kHz | 2V | 1.0 |
| 4-7 | (空) | - | - | - |

## 错误处理

### 错误代码

| 代码 | 含义 | 处理方法 |
|------|------|----------|
| 0x01 | 参数超出范围 | 检查输入参数 |
| 0x02 | 硬件通信失败 | 检查硬件连接 |
| 0x03 | 命令格式错误 | 检查命令格式 |
| 0x04 | Flash操作失败 | 重试或复位 |

### 故障排除

1. **无法设置频率**
   - 检查频率范围 (1Hz ~ 150MHz)
   - 确认AD9854初始化成功

2. **输出幅度不正确**
   - 检查增益系数设置
   - 验证后续电路增益

3. **外部控制无响应**
   - 检查通信接口连接
   - 确认波特率设置

## 扩展功能

### 待实现功能

1. **高级调制**
   - FSK调制控制
   - BPSK调制控制
   - 扫频功能

2. **自动校准**
   - 频率校准
   - 幅度校准
   - 温度补偿

3. **远程控制**
   - 网络接口
   - 蓝牙控制
   - 上位机软件

### 接口扩展

系统预留了扩展接口，可以方便地添加新的控制方式：

```c
// 添加新的控制接口类型
typedef enum {
    CONTROL_INTERFACE_NONE = 0,
    CONTROL_INTERFACE_UART_SCREEN = 1,
    CONTROL_INTERFACE_MATRIX_KEYPAD = 2,
    CONTROL_INTERFACE_BLUETOOTH = 3,     // 新增蓝牙接口
    CONTROL_INTERFACE_ETHERNET = 4       // 新增网络接口
} AD9854_ControlInterface_t;
```

## 总结

AD9854外部控制接口提供了灵活、可扩展的参数控制方案：

1. **智能增益补偿** - 自动计算所需输出，简化使用
2. **多种控制方式** - 支持串口屏和矩阵键盘
3. **参数存储** - Flash保存常用配置
4. **易于扩展** - 模块化设计，便于添加新功能

该接口为电赛G题提供了完整的信号源控制解决方案，满足了实时参数调节和精确控制的需求。
