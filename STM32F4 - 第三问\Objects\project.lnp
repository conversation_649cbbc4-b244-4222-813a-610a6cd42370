--cpu=Cortex-M4.fp
".\objects\startup_stm32f40_41xxx.o"
".\objects\main.o"
".\objects\stm32f4xx_it.o"
".\objects\system_stm32f4xx.o"
".\objects\bsp.o"
".\objects\misc.o"
".\objects\stm32f4xx_gpio.o"
".\objects\stm32f4xx_rcc.o"
".\objects\stm32f4xx_flash.o"
".\objects\stm32f4xx_usart.o"
".\objects\stm32f4xx_dma.o"
".\objects\systick.o"
".\objects\usart.o"
".\objects\ad9854.o"
".\objects\external_control.o"
"D:\keil5   MDK\ARM\PACK\ARM\CMSIS\5.0.1\CMSIS\Lib\ARM\arm_cortexM4lf_math.lib"
--strict --scatter ".\Objects\project.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\Listings\project.map" -o .\Objects\project.axf