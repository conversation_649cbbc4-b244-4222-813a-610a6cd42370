/**
  ******************************************************************************
  * @file    usart.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024
  * @brief   高性能串口调试模块实现
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "usart.h"
#include "systick.h"
#include <stdarg.h>
#include <string.h>
#include <stdio.h>  // 添加vsnprintf支持

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/

/** @defgroup USART_Private_Defines USART私有定义
  * @{
  */

#define USART_TX_PIN                GPIO_Pin_9   ///< PA9
#define USART_RX_PIN                GPIO_Pin_10  ///< PA10
#define USART_GPIO_PORT             GPIOA
#define USART_GPIO_CLK              RCC_AHB1Periph_GPIOA
#define USART_CLK                   RCC_APB2Periph_USART1

#define USART_TX_DMA_STREAM         DMA2_Stream7
#define USART_RX_DMA_STREAM         DMA2_Stream2
#define USART_TX_DMA_CHANNEL        DMA_Channel_4
#define USART_RX_DMA_CHANNEL        DMA_Channel_4
#define USART_DMA_CLK               RCC_AHB1Periph_DMA2

/**
  * @}
  */

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/** @defgroup USART_Private_Variables USART私有变量
  * @{
  */

USART_Handle_t g_usart1_handle;                  ///< USART1句柄
volatile bool g_usart_tx_complete_flag = true;   ///< 发送完成标志
volatile bool g_usart_rx_complete_flag = false;  ///< 接收完成标志

// 静态缓冲区
static uint8_t s_tx_buffer[USART_TX_BUFFER_SIZE];
static uint8_t s_rx_buffer[USART_RX_BUFFER_SIZE];
static char s_printf_buffer[512];                ///< printf缓冲区

/**
  * @}
  */

/* Private function prototypes -----------------------------------------------*/
static void USART_GPIO_Config(void);
static void USART_DMA_Config(void);
static void USART_NVIC_Config(void);
static void RingBuffer_Init(RingBuffer_t* rb, uint8_t* buffer, uint32_t size);
static uint32_t RingBuffer_Write(RingBuffer_t* rb, const uint8_t* data, uint32_t length) __attribute__((unused));
static uint32_t RingBuffer_Read(RingBuffer_t* rb, uint8_t* data, uint32_t length) __attribute__((unused));

/* Private functions ---------------------------------------------------------*/

/**
  * @brief  USART GPIO配置
  * @param  None
  * @retval None
  */
static void USART_GPIO_Config(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    
    // 使能GPIO时钟
    RCC_AHB1PeriphClockCmd(USART_GPIO_CLK, ENABLE);
    
    // 配置USART引脚为复用功能
    GPIO_PinAFConfig(USART_GPIO_PORT, GPIO_PinSource9, GPIO_AF_USART1);   // TX
    GPIO_PinAFConfig(USART_GPIO_PORT, GPIO_PinSource10, GPIO_AF_USART1);  // RX
    
    // 配置TX引脚
    GPIO_InitStructure.GPIO_Pin = USART_TX_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(USART_GPIO_PORT, &GPIO_InitStructure);
    
    // 配置RX引脚
    GPIO_InitStructure.GPIO_Pin = USART_RX_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(USART_GPIO_PORT, &GPIO_InitStructure);
}

/**
  * @brief  USART DMA配置
  * @param  None
  * @retval None
  */
static void USART_DMA_Config(void)
{
    DMA_InitTypeDef DMA_InitStructure;
    
    // 使能DMA时钟
    RCC_AHB1PeriphClockCmd(USART_DMA_CLK, ENABLE);
    
    // 配置DMA TX Stream
    DMA_DeInit(USART_TX_DMA_STREAM);
    DMA_InitStructure.DMA_Channel = USART_TX_DMA_CHANNEL;
    DMA_InitStructure.DMA_PeripheralBaseAddr = (uint32_t)&USART1->DR;
    DMA_InitStructure.DMA_Memory0BaseAddr = (uint32_t)s_tx_buffer;
    DMA_InitStructure.DMA_DIR = DMA_DIR_MemoryToPeripheral;
    DMA_InitStructure.DMA_BufferSize = 0; // 动态设置
    DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;
    DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;
    DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_Byte;
    DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_Byte;
    DMA_InitStructure.DMA_Mode = DMA_Mode_Normal;
    DMA_InitStructure.DMA_Priority = DMA_Priority_Medium;
    DMA_InitStructure.DMA_FIFOMode = DMA_FIFOMode_Disable;
    DMA_InitStructure.DMA_FIFOThreshold = DMA_FIFOThreshold_HalfFull;
    DMA_InitStructure.DMA_MemoryBurst = DMA_MemoryBurst_Single;
    DMA_InitStructure.DMA_PeripheralBurst = DMA_PeripheralBurst_Single;
    DMA_Init(USART_TX_DMA_STREAM, &DMA_InitStructure);
    
    // 配置DMA RX Stream
    DMA_DeInit(USART_RX_DMA_STREAM);
    DMA_InitStructure.DMA_Channel = USART_RX_DMA_CHANNEL;
    DMA_InitStructure.DMA_PeripheralBaseAddr = (uint32_t)&USART1->DR;
    DMA_InitStructure.DMA_Memory0BaseAddr = (uint32_t)s_rx_buffer;
    DMA_InitStructure.DMA_DIR = DMA_DIR_PeripheralToMemory;
    DMA_InitStructure.DMA_BufferSize = USART_RX_BUFFER_SIZE;
    DMA_Init(USART_RX_DMA_STREAM, &DMA_InitStructure);
    
    // 使能DMA中断
    DMA_ITConfig(USART_TX_DMA_STREAM, DMA_IT_TC, ENABLE);  // 传输完成中断
    DMA_ITConfig(USART_RX_DMA_STREAM, DMA_IT_TC, ENABLE);  // 传输完成中断
}

/**
  * @brief  USART NVIC配置
  * @param  None
  * @retval None
  */
static void USART_NVIC_Config(void)
{
    NVIC_InitTypeDef NVIC_InitStructure;
    
    // 配置USART1中断
    NVIC_InitStructure.NVIC_IRQChannel = USART1_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);
    
    // 配置DMA TX中断
    NVIC_InitStructure.NVIC_IRQChannel = DMA2_Stream7_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 2;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);
    
    // 配置DMA RX中断
    NVIC_InitStructure.NVIC_IRQChannel = DMA2_Stream2_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 2;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);
}

/**
  * @brief  环形缓冲区初始化
  * @param  rb: 环形缓冲区指针
  * @param  buffer: 缓冲区指针
  * @param  size: 缓冲区大小
  * @retval None
  */
static void RingBuffer_Init(RingBuffer_t* rb, uint8_t* buffer, uint32_t size)
{
    rb->buffer = buffer;
    rb->size = size;
    rb->head = 0;
    rb->tail = 0;
    rb->count = 0;
    rb->overflow = false;
}

/**
  * @brief  向环形缓冲区写入数据
  * @param  rb: 环形缓冲区指针
  * @param  data: 数据指针
  * @param  length: 数据长度
  * @retval 实际写入的字节数
  */
static uint32_t RingBuffer_Write(RingBuffer_t* rb, const uint8_t* data, uint32_t length)
{
    uint32_t written = 0;
    
    for (uint32_t i = 0; i < length; i++) {
        if (rb->count >= rb->size) {
            rb->overflow = true;
            break; // 缓冲区已满
        }
        
        rb->buffer[rb->head] = data[i];
        rb->head = (rb->head + 1) % rb->size;
        rb->count++;
        written++;
    }
    
    return written;
}

/**
  * @brief  从环形缓冲区读取数据
  * @param  rb: 环形缓冲区指针
  * @param  data: 数据指针
  * @param  length: 期望读取长度
  * @retval 实际读取的字节数
  */
static uint32_t RingBuffer_Read(RingBuffer_t* rb, uint8_t* data, uint32_t length)
{
    uint32_t read = 0;
    
    for (uint32_t i = 0; i < length; i++) {
        if (rb->count == 0) {
            break; // 缓冲区为空
        }
        
        data[i] = rb->buffer[rb->tail];
        rb->tail = (rb->tail + 1) % rb->size;
        rb->count--;
        read++;
    }
    
    return read;
}

/* Exported functions --------------------------------------------------------*/

/**
  * @brief  USART1初始化
  * @param  baudrate: 波特率
  * @retval 0: 成功, -1: 失败
  */
int8_t USART1_Init(uint32_t baudrate)
{
    USART_InitTypeDef USART_InitStructure;
    
    // 参数检查
    if (baudrate == 0 || baudrate > USART_MAX_BAUDRATE) {
        return -1;
    }
    
    // 初始化句柄
    g_usart1_handle.instance = USART1;
    g_usart1_handle.config.baudrate = baudrate;
    g_usart1_handle.config.word_length = USART_WordLength_8b;
    g_usart1_handle.config.stop_bits = USART_StopBits_1;
    g_usart1_handle.config.parity = USART_Parity_No;
    g_usart1_handle.config.flow_control = USART_HardwareFlowControl_None;
    g_usart1_handle.config.dma_tx_enable = true;
    g_usart1_handle.config.dma_rx_enable = true;
    
    // 初始化环形缓冲区
    RingBuffer_Init(&g_usart1_handle.tx_buffer, s_tx_buffer, USART_TX_BUFFER_SIZE);
    RingBuffer_Init(&g_usart1_handle.rx_buffer, s_rx_buffer, USART_RX_BUFFER_SIZE);
    
    // 配置GPIO
    USART_GPIO_Config();
    
    // 配置DMA
    USART_DMA_Config();
    
    // 配置NVIC
    USART_NVIC_Config();
    
    // 使能USART时钟
    RCC_APB2PeriphClockCmd(USART_CLK, ENABLE);
    
    // 配置USART
    USART_InitStructure.USART_BaudRate = baudrate;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;
    USART_Init(USART1, &USART_InitStructure);
    
    // 使能USART DMA
    USART_DMACmd(USART1, USART_DMAReq_Tx, ENABLE);
    USART_DMACmd(USART1, USART_DMAReq_Rx, ENABLE);
    
    // 使能USART中断
    USART_ITConfig(USART1, USART_IT_IDLE, ENABLE);  // 空闲中断
    USART_ITConfig(USART1, USART_IT_PE, ENABLE);    // 校验错误中断
    USART_ITConfig(USART1, USART_IT_ERR, ENABLE);   // 错误中断
    
    // 使能USART
    USART_Cmd(USART1, ENABLE);
    
    // 启动DMA接收
    DMA_Cmd(USART_RX_DMA_STREAM, ENABLE);
    
    // 重置统计信息
    USART_ResetStats();

    return 0;
}

/**
  * @brief  发送单个字节
  * @param  data: 要发送的字节
  * @retval 0: 成功, -1: 失败
  */
int8_t USART_SendByte(uint8_t data)
{
    // 等待发送寄存器空
    uint32_t timeout = 1000;
    while (USART_GetFlagStatus(USART1, USART_FLAG_TXE) == RESET) {
        if (--timeout == 0) {
            g_usart1_handle.stats.tx_errors++;
            return -1;
        }
    }

    // 发送数据
    USART_SendData(USART1, data);

    // 更新统计
    g_usart1_handle.stats.tx_bytes++;

    return 0;
}

/**
  * @brief  发送字符串
  * @param  str: 要发送的字符串
  * @retval 发送的字节数
  */
uint32_t USART_SendString(const char* str)
{
    if (str == NULL) {
        return 0;
    }

    uint32_t length = strlen(str);
    return USART_Module_SendData((const uint8_t*)str, length);
}

/**
  * @brief  发送数据缓冲区
  * @param  data: 数据缓冲区
  * @param  length: 数据长度
  * @retval 发送的字节数
  */
uint32_t USART_Module_SendData(const uint8_t* data, uint32_t length)
{
    if (data == NULL || length == 0) {
        return 0;
    }

    uint32_t sent = 0;

    for (uint32_t i = 0; i < length; i++) {
        if (USART_SendByte(data[i]) == 0) {
            sent++;
        } else {
            break;
        }
    }

    return sent;
}

/**
  * @brief  DMA发送数据
  * @param  data: 数据缓冲区
  * @param  length: 数据长度
  * @retval 0: 成功, -1: 失败
  */
int8_t USART_SendData_DMA(const uint8_t* data, uint32_t length)
{
    if (data == NULL || length == 0 || length > USART_TX_BUFFER_SIZE) {
        return -1;
    }

    // 检查DMA是否忙
    if (g_usart1_handle.tx_busy) {
        return -1;
    }

    // 复制数据到DMA缓冲区
    memcpy(s_tx_buffer, data, length);

    // 配置DMA传输
    DMA_Cmd(USART_TX_DMA_STREAM, DISABLE);
    DMA_SetCurrDataCounter(USART_TX_DMA_STREAM, length);
    DMA_Cmd(USART_TX_DMA_STREAM, ENABLE);

    // 设置忙标志
    g_usart1_handle.tx_busy = true;
    g_usart_tx_complete_flag = false;

    return 0;
}

/**
  * @brief  接收单个字节
  * @param  data: 接收数据指针
  * @param  timeout_ms: 超时时间(ms)
  * @retval 0: 成功, -1: 超时或失败
  */
int8_t USART_ReceiveByte(uint8_t* data, uint32_t timeout_ms)
{
    if (data == NULL) {
        return -1;
    }

    uint32_t start_time = SysTick_GetTick();

    while (USART_GetFlagStatus(USART1, USART_FLAG_RXNE) == RESET) {
        if ((SysTick_GetTick() - start_time) > timeout_ms) {
            return -1; // 超时
        }
    }

    *data = USART_ReceiveData(USART1);
    g_usart1_handle.stats.rx_bytes++;

    return 0;
}

/**
  * @brief  接收数据到缓冲区
  * @param  buffer: 接收缓冲区
  * @param  length: 期望接收长度
  * @param  timeout_ms: 超时时间(ms)
  * @retval 实际接收的字节数
  */
uint32_t USART_Module_ReceiveData(uint8_t* buffer, uint32_t length, uint32_t timeout_ms)
{
    if (buffer == NULL || length == 0) {
        return 0;
    }

    uint32_t received = 0;
    uint32_t start_time = SysTick_GetTick();

    while (received < length) {
        if (USART_ReceiveByte(&buffer[received], 1) == 0) {
            received++;
            start_time = SysTick_GetTick(); // 重置超时计时器
        } else {
            if ((SysTick_GetTick() - start_time) > timeout_ms) {
                break; // 超时
            }
        }
    }

    return received;
}

/**
  * @brief  格式化输出(printf重定向)
  * @param  format: 格式字符串
  * @param  ...: 可变参数
  * @retval 输出的字符数
  */
int USART_Printf(const char* format, ...)
{
    va_list args;
    int length;

    va_start(args, format);
    length = vsnprintf(s_printf_buffer, sizeof(s_printf_buffer), format, args);
    va_end(args);

    if (length > 0) {
        USART_SendString(s_printf_buffer);
    }

    return length;
}

/**
  * @brief  发送十六进制数据
  * @param  data: 数据缓冲区
  * @param  length: 数据长度
  * @retval None
  */
void USART_SendHex(const uint8_t* data, uint32_t length)
{
    if (data == NULL || length == 0) {
        return;
    }

    USART_Printf("HEX[%d]: ", length);

    for (uint32_t i = 0; i < length; i++) {
        USART_Printf("%02X ", data[i]);
        if ((i + 1) % 16 == 0) {
            USART_Printf("\r\n");
        }
    }

    if (length % 16 != 0) {
        USART_Printf("\r\n");
    }
}

/**
  * @brief  获取统计信息
  * @param  stats: 统计信息结构体指针
  * @retval None
  */
void USART_GetStats(USART_Stats_t* stats)
{
    if (stats == NULL) {
        return;
    }

    *stats = g_usart1_handle.stats;
}

/**
  * @brief  重置统计信息
  * @param  None
  * @retval None
  */
void USART_ResetStats(void)
{
    memset(&g_usart1_handle.stats, 0, sizeof(USART_Stats_t));
}

/**
  * @brief  检查发送是否完成
  * @param  None
  * @retval true: 完成, false: 未完成
  */
bool USART_IsTxComplete(void)
{
    return g_usart_tx_complete_flag;
}

/**
  * @brief  等待发送完成
  * @param  timeout_ms: 超时时间(ms)
  * @retval 0: 成功, -1: 超时
  */
int8_t USART_WaitTxComplete(uint32_t timeout_ms)
{
    uint32_t start_time = SysTick_GetTick();

    while (!g_usart_tx_complete_flag) {
        if ((SysTick_GetTick() - start_time) > timeout_ms) {
            return -1; // 超时
        }
    }

    return 0; // 成功
}

/**
  * @brief  USART1中断处理函数
  * @param  None
  * @retval None
  */
void USART1_IRQHandler(void)
{
    // 处理空闲中断
    if (USART_GetITStatus(USART1, USART_IT_IDLE) != RESET) {
        USART_ClearITPendingBit(USART1, USART_IT_IDLE);

        // 停止DMA接收
        DMA_Cmd(USART_RX_DMA_STREAM, DISABLE);

        // 计算接收到的数据长度
        uint32_t received_length = USART_RX_BUFFER_SIZE - DMA_GetCurrDataCounter(USART_RX_DMA_STREAM);

        if (received_length > 0) {
            // 更新统计信息
            g_usart1_handle.stats.rx_bytes += received_length;
            g_usart_rx_complete_flag = true;
        }

        // 重新启动DMA接收
        DMA_SetCurrDataCounter(USART_RX_DMA_STREAM, USART_RX_BUFFER_SIZE);
        DMA_Cmd(USART_RX_DMA_STREAM, ENABLE);
    }

    // 处理校验错误
    if (USART_GetITStatus(USART1, USART_IT_PE) != RESET) {
        USART_ClearITPendingBit(USART1, USART_IT_PE);
        g_usart1_handle.stats.rx_errors++;
    }

    // 处理其他错误
    if (USART_GetITStatus(USART1, USART_IT_ERR) != RESET) {
        USART_ClearITPendingBit(USART1, USART_IT_ERR);
        g_usart1_handle.stats.rx_errors++;
    }
}

/**
  * @brief  DMA2_Stream7中断处理函数(USART1_TX)
  * @param  None
  * @retval None
  */
void DMA2_Stream7_IRQHandler(void)
{
    // 检查传输完成中断
    if (DMA_GetITStatus(USART_TX_DMA_STREAM, DMA_IT_TCIF7) != RESET) {
        DMA_ClearITPendingBit(USART_TX_DMA_STREAM, DMA_IT_TCIF7);

        // 清除忙标志
        g_usart1_handle.tx_busy = false;
        g_usart_tx_complete_flag = true;

        // 更新统计信息
        g_usart1_handle.stats.dma_tx_complete++;
    }

    // 检查传输错误中断
    if (DMA_GetITStatus(USART_TX_DMA_STREAM, DMA_IT_TEIF7) != RESET) {
        DMA_ClearITPendingBit(USART_TX_DMA_STREAM, DMA_IT_TEIF7);

        // 清除忙标志
        g_usart1_handle.tx_busy = false;

        // 更新错误统计
        g_usart1_handle.stats.tx_errors++;
    }
}

/**
  * @brief  DMA2_Stream2中断处理函数(USART1_RX)
  * @param  None
  * @retval None
  */
void DMA2_Stream2_IRQHandler(void)
{
    // 检查传输完成中断
    if (DMA_GetITStatus(USART_RX_DMA_STREAM, DMA_IT_TCIF2) != RESET) {
        DMA_ClearITPendingBit(USART_RX_DMA_STREAM, DMA_IT_TCIF2);

        // 更新统计信息
        g_usart1_handle.stats.dma_rx_complete++;
        g_usart1_handle.stats.rx_bytes += USART_RX_BUFFER_SIZE;
        g_usart_rx_complete_flag = true;

        // 重新启动DMA接收
        DMA_SetCurrDataCounter(USART_RX_DMA_STREAM, USART_RX_BUFFER_SIZE);
        DMA_Cmd(USART_RX_DMA_STREAM, ENABLE);
    }

    // 检查传输错误中断
    if (DMA_GetITStatus(USART_RX_DMA_STREAM, DMA_IT_TEIF2) != RESET) {
        DMA_ClearITPendingBit(USART_RX_DMA_STREAM, DMA_IT_TEIF2);

        // 更新错误统计
        g_usart1_handle.stats.rx_errors++;
    }
}

/**
  * @brief  printf重定向到USART
  * @param  ch: 要输出的字符
  * @param  f: 文件指针(未使用)
  * @retval 输出的字符
  */
int fputc(int ch, FILE *f)
{
    USART_SendByte((uint8_t)ch);
    return ch;
}

/**
  * @brief  scanf重定向从USART
  * @param  f: 文件指针(未使用)
  * @retval 接收到的字符
  */
int fgetc(FILE *f)
{
    uint8_t ch;
    if (USART_ReceiveByte(&ch, 1000) == 0) {
        return (int)ch;
    }
    return -1;
}

/************************ (C) COPYRIGHT 嵌入式竞赛团队 *****END OF FILE****/
