/**
  ******************************************************************************
  * @file    gain_calculator.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   增益计算算法实现文件 - 电赛G题专用
  ******************************************************************************
  * @attention
  *
  * 本文件实现电赛G题后续电路的增益计算算法功能
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "gain_calculator.h"
#include <math.h>
#include <stdio.h>

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/* 当前增益级联配置 */
static GainStage_t current_stages;

/* 系统初始化标志 */
static bool gain_calc_initialized = false;

/* Private function prototypes -----------------------------------------------*/
static double CalculateTotalGain(const GainStage_t* stages);
static bool ValidateGainConfig(const GainConfig_t* config);

/* Private functions ---------------------------------------------------------*/

/**
 * @brief  增益计算器初始化
 * @param  None
 * @retval None
 */
void GainCalculator_Init(void)
{
    /* 初始化默认增益级联 */
    current_stages.buffer_gain = GAIN_BUFFER_TYPICAL;
    current_stages.variable_gain = 1.0;  // 默认1倍增益
    current_stages.filter_gain = GAIN_FILTER_TYPICAL;
    current_stages.freq_correction = 1.0;
    current_stages.temp_correction = 1.0;
    current_stages.nonlinear_correction = 1.0;
    
    gain_calc_initialized = true;
}

/**
 * @brief  计算总增益
 * @param  config: 增益配置
 * @param  result: 计算结果输出
 * @retval true-计算成功, false-计算失败
 */
bool GainCalculator_Calculate(const GainConfig_t* config, GainResult_t* result)
{
    if (!gain_calc_initialized || config == NULL || result == NULL) {
        return false;
    }
    
    /* 验证配置参数 */
    if (!ValidateGainConfig(config)) {
        result->is_valid = false;
        return false;
    }
    
    /* 计算各级校正系数 */
    current_stages.freq_correction = GainCalculator_FrequencyCorrection(config->frequency_hz);
    current_stages.temp_correction = GainCalculator_TemperatureCorrection(config->temperature_c);
    
    if (config->enable_nonlinear_correction) {
        current_stages.nonlinear_correction = GainCalculator_NonlinearCorrection(config->input_amplitude_mv);
    } else {
        current_stages.nonlinear_correction = 1.0;
    }
    
    /* 计算总增益 */
    result->calculated_gain = CalculateTotalGain(&current_stages);
    
    /* 计算AD9910所需输出 */
    result->ad9910_output_mv = (double)config->input_amplitude_mv / result->calculated_gain;
    
    /* 计算最终输出 */
    result->final_output_mv = (double)config->input_amplitude_mv;
    
    /* 计算增益误差 */
    if (config->target_total_gain > 0) {
        result->gain_error_percent = fabs(result->calculated_gain - config->target_total_gain) / 
                                   config->target_total_gain * 100.0;
    } else {
        result->gain_error_percent = 0.0;
    }
    
    /* 验证结果有效性 */
    result->is_valid = (result->ad9910_output_mv >= 10.0 && result->ad9910_output_mv <= 800.0);
    
    return result->is_valid;
}

/**
 * @brief  计算AD9910所需输出幅度
 * @param  target_output_mv: 目标输出幅度 (mV)
 * @param  total_gain: 总增益
 * @retval AD9910应输出的幅度 (mV)
 */
uint16_t GainCalculator_GetAD9910Output(uint16_t target_output_mv, double total_gain)
{
    if (total_gain <= 0) return 0;
    
    double ad9910_output = (double)target_output_mv / total_gain;
    
    /* 限制在AD9910有效范围内 */
    if (ad9910_output < 10.0) ad9910_output = 10.0;
    if (ad9910_output > 800.0) ad9910_output = 800.0;
    
    return (uint16_t)(ad9910_output + 0.5);  // 四舍五入
}

/**
 * @brief  计算最终输出幅度
 * @param  ad9910_output_mv: AD9910输出幅度 (mV)
 * @param  total_gain: 总增益
 * @retval 最终输出幅度 (mV)
 */
uint16_t GainCalculator_GetFinalOutput(uint16_t ad9910_output_mv, double total_gain)
{
    double final_output = (double)ad9910_output_mv * total_gain;
    
    return (uint16_t)(final_output + 0.5);  // 四舍五入
}

/**
 * @brief  设置可变增益放大器增益
 * @param  variable_gain: 可变增益值
 * @retval true-设置成功, false-设置失败
 */
bool GainCalculator_SetVariableGain(double variable_gain)
{
    if (!GainCalculator_ValidateGain(variable_gain)) {
        return false;
    }
    
    if (variable_gain < GAIN_VARIABLE_MIN || variable_gain > GAIN_VARIABLE_MAX) {
        return false;
    }
    
    current_stages.variable_gain = variable_gain;
    return true;
}

/**
 * @brief  获取当前增益级联信息
 * @param  stages: 增益级联结构体输出
 * @retval None
 */
void GainCalculator_GetStages(GainStage_t* stages)
{
    if (stages != NULL) {
        *stages = current_stages;
    }
}

/**
 * @brief  频率校正系数计算
 * @param  frequency_hz: 工作频率
 * @retval 频率校正系数
 */
double GainCalculator_FrequencyCorrection(uint32_t frequency_hz)
{
    /* 频率校正公式: correction = 1 + slope * (freq - ref_freq) */
    double freq_diff = (double)frequency_hz - FREQ_CORRECTION_REF_HZ;
    double correction = 1.0 + FREQ_CORRECTION_SLOPE * freq_diff;
    
    /* 限制校正范围 */
    if (correction < 0.9) correction = 0.9;
    if (correction > 1.1) correction = 1.1;
    
    return correction;
}

/**
 * @brief  温度校正系数计算
 * @param  temperature_c: 工作温度
 * @retval 温度校正系数
 */
double GainCalculator_TemperatureCorrection(int16_t temperature_c)
{
    /* 温度校正公式: correction = 1 + coeff * (temp - ref_temp) */
    double temp_diff = (double)temperature_c - TEMP_CORRECTION_REF_C;
    double correction = 1.0 + TEMP_CORRECTION_COEFF * temp_diff;
    
    /* 限制校正范围 */
    if (correction < 0.95) correction = 0.95;
    if (correction > 1.05) correction = 1.05;
    
    return correction;
}

/**
 * @brief  非线性校正系数计算
 * @param  amplitude_mv: 信号幅度
 * @retval 非线性校正系数
 */
double GainCalculator_NonlinearCorrection(uint16_t amplitude_mv)
{
    double correction = 1.0;
    
    if (amplitude_mv < NONLINEAR_THRESHOLD_LOW_MV) {
        /* 低幅度区域，增益可能偏低 */
        correction = NONLINEAR_CORRECTION_LOW;
    } else if (amplitude_mv > NONLINEAR_THRESHOLD_HIGH_MV) {
        /* 高幅度区域，增益可能偏高 */
        correction = NONLINEAR_CORRECTION_HIGH;
    }
    
    return correction;
}

/**
 * @brief  验证增益参数有效性
 * @param  gain: 增益值
 * @retval true-有效, false-无效
 */
bool GainCalculator_ValidateGain(double gain)
{
    return (gain >= GAIN_TOTAL_MIN && gain <= GAIN_TOTAL_MAX && !isnan(gain) && !isinf(gain));
}

/**
 * @brief  优化增益分配
 * @param  target_gain: 目标总增益
 * @param  stages: 优化后的增益级联输出
 * @retval true-优化成功, false-优化失败
 */
bool GainCalculator_OptimizeStages(double target_gain, GainStage_t* stages)
{
    if (!GainCalculator_ValidateGain(target_gain) || stages == NULL) {
        return false;
    }
    
    /* 复制当前配置 */
    *stages = current_stages;
    
    /* 计算所需的可变增益 */
    double fixed_gain = stages->buffer_gain * stages->filter_gain * 
                       stages->freq_correction * stages->temp_correction * 
                       stages->nonlinear_correction;
    
    double required_variable_gain = target_gain / fixed_gain;
    
    /* 检查可变增益是否在有效范围内 */
    if (required_variable_gain < GAIN_VARIABLE_MIN || required_variable_gain > GAIN_VARIABLE_MAX) {
        return false;
    }
    
    stages->variable_gain = required_variable_gain;
    return true;
}

/* ==================== 电赛G题专用函数 ==================== */

/**
 * @brief  电赛G题标准增益配置
 * @param  output_range: 输出范围 (1=0.1-1V, 2=1-10V, 3=10-100V)
 * @param  stages: 配置后的增益级联输出
 * @retval true-配置成功, false-配置失败
 */
bool GainCalculator_ConfigForContest(uint8_t output_range, GainStage_t* stages)
{
    if (stages == NULL || output_range < 1 || output_range > 3) {
        return false;
    }
    
    /* 初始化基本配置 */
    *stages = current_stages;
    
    /* 根据输出范围配置增益 */
    switch (output_range) {
        case 1:  // 0.1-1V 输出范围
            stages->variable_gain = 1.25;  // 1.25倍增益 (800mV*1.25=1V)
            break;
            
        case 2:  // 1-10V 输出范围
            stages->variable_gain = 12.5;  // 12.5倍增益 (800mV*12.5=10V)
            break;
            
        case 3:  // 10-100V 输出范围 (需要额外功率放大器)
            stages->variable_gain = 125.0; // 125倍增益 (理论值，实际需要多级放大)
            return false;  // 超出单级放大器能力
            
        default:
            return false;
    }
    
    return GainCalculator_ValidateGain(stages->variable_gain);
}

/**
 * @brief  电赛G题精度优化
 * @param  target_accuracy_percent: 目标精度 (%)
 * @param  config: 优化配置输出
 * @retval true-优化成功, false-优化失败
 */
bool GainCalculator_OptimizeAccuracy(double target_accuracy_percent, GainConfig_t* config)
{
    if (config == NULL || target_accuracy_percent <= 0) {
        return false;
    }
    
    /* 根据目标精度配置参数 */
    config->enable_nonlinear_correction = (target_accuracy_percent < 1.0);  // 高精度时启用非线性校正
    config->temperature_c = 25;  // 假设室温工作
    
    return true;
}

/**
 * @brief  电赛G题动态范围计算
 * @param  min_output_mv: 最小输出 (mV)
 * @param  max_output_mv: 最大输出 (mV)
 * @retval 动态范围 (dB)
 */
double GainCalculator_CalculateDynamicRange(uint16_t min_output_mv, uint16_t max_output_mv)
{
    if (min_output_mv == 0 || max_output_mv <= min_output_mv) {
        return 0.0;
    }
    
    return 20.0 * log10((double)max_output_mv / (double)min_output_mv);
}

/* ==================== 调试和测试函数 ==================== */

/**
 * @brief  增益计算自测试
 * @param  None
 * @retval true-测试通过, false-测试失败
 */
bool GainCalculator_SelfTest(void)
{
    GainConfig_t test_config = {
        .target_total_gain = 2.0,
        .frequency_hz = 5000000,
        .temperature_c = 25,
        .input_amplitude_mv = 500,
        .enable_nonlinear_correction = true
    };
    
    GainResult_t test_result;
    
    return GainCalculator_Calculate(&test_config, &test_result) && test_result.is_valid;
}

/* ==================== 私有函数实现 ==================== */

/**
 * @brief  计算总增益
 * @param  stages: 增益级联结构体
 * @retval 总增益
 */
static double CalculateTotalGain(const GainStage_t* stages)
{
    if (stages == NULL) return 0.0;
    
    return stages->buffer_gain * stages->variable_gain * stages->filter_gain *
           stages->freq_correction * stages->temp_correction * stages->nonlinear_correction;
}

/**
 * @brief  验证增益配置有效性
 * @param  config: 增益配置
 * @retval true-有效, false-无效
 */
static bool ValidateGainConfig(const GainConfig_t* config)
{
    if (config == NULL) return false;
    
    return (config->frequency_hz >= 1 && config->frequency_hz <= 420000000) &&
           (config->input_amplitude_mv >= 10 && config->input_amplitude_mv <= 5000) &&
           (config->temperature_c >= -40 && config->temperature_c <= 85) &&
           GainCalculator_ValidateGain(config->target_total_gain);
}

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
