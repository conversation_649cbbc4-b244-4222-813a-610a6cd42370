/**
  ******************************************************************************
  * @file    模型电路测试示例.c
  * <AUTHOR> 第三问 模型电路测试
  * @version V1.0
  * @date    2024
  * @brief   AD9854模型电路功能测试示例代码
  ******************************************************************************
  * @attention
  * 
  * 本文件展示如何使用AD9854的模型电路功能
  * 模型电路传递函数: H(s) = 5/(10^-8*s^2 + 3*10^-4*s + 1)
  * 
  * 测试内容:
  * 1. 不同频率下的增益计算
  * 2. 自动增益补偿演示
  * 3. 模型电路与手动增益模式对比
  * 
  ******************************************************************************
  */

#include "ad9854.h"
#include <stdio.h>

/**
 * @brief  测试模型电路在不同频率下的增益
 * @param  None
 * @retval None
 */
void Test_ModelCircuitGain(void)
{
    printf("\n=== 模型电路增益测试 ===\n");
    printf("传递函数: H(s) = 5/(10^-8*s^2 + 3*10^-4*s + 1)\n\n");
    
    // 测试频率点
    double test_frequencies[] = {
        100.0,      // 100Hz
        1000.0,     // 1kHz
        10000.0,    // 10kHz
        100000.0,   // 100kHz
        1000000.0,  // 1MHz
        5000000.0   // 5MHz
    };
    
    int num_freqs = sizeof(test_frequencies) / sizeof(test_frequencies[0]);
    
    printf("频率\t\t增益\t\t增益(dB)\n");
    printf("----------------------------------------\n");
    
    for (int i = 0; i < num_freqs; i++) {
        double gain = AD9854_CalculateModelCircuitGain(test_frequencies[i]);
        double gain_db = 20.0 * log10(gain);
        
        if (test_frequencies[i] < 1000) {
            printf("%.0fHz\t\t%.3f\t\t%.1fdB\n", test_frequencies[i], gain, gain_db);
        } else if (test_frequencies[i] < 1000000) {
            printf("%.0fkHz\t\t%.3f\t\t%.1fdB\n", test_frequencies[i]/1000, gain, gain_db);
        } else {
            printf("%.0fMHz\t\t%.3f\t\t%.1fdB\n", test_frequencies[i]/1000000, gain, gain_db);
        }
    }
}

/**
 * @brief  测试自动增益补偿功能
 * @param  None
 * @retval None
 */
void Test_AutoGainCompensation(void)
{
    printf("\n=== 自动增益补偿测试 ===\n");
    printf("目标输出: 500mV峰峰值\n\n");
    
    AD9854_ControlParams_t params;
    double target_vpp = 500.0;  // 500mV目标输出
    
    // 启用模型电路
    AD9854_EnableModelCircuit(1);
    
    // 测试不同频率
    double test_frequencies[] = {1000.0, 10000.0, 100000.0, 1000000.0, 5000000.0};
    int num_freqs = sizeof(test_frequencies) / sizeof(test_frequencies[0]);
    
    printf("频率\t\t模型增益\tAD9854输出\t实际输出\n");
    printf("--------------------------------------------------------\n");
    
    for (int i = 0; i < num_freqs; i++) {
        // 设置频率和目标幅度
        AD9854_SetTargetFrequency(test_frequencies[i]);
        AD9854_SetTargetAmplitude(target_vpp);
        
        // 获取计算结果
        AD9854_GetControlParams(&params);
        
        // 计算实际最终输出
        double actual_output = params.ad9854_vpp_mv * params.model_circuit_gain;
        
        if (test_frequencies[i] < 1000000) {
            printf("%.0fkHz\t\t%.3f\t\t%.1fmV\t\t%.1fmV\n", 
                   test_frequencies[i]/1000, 
                   params.model_circuit_gain,
                   params.ad9854_vpp_mv,
                   actual_output);
        } else {
            printf("%.0fMHz\t\t%.3f\t\t%.1fmV\t\t%.1fmV\n", 
                   test_frequencies[i]/1000000, 
                   params.model_circuit_gain,
                   params.ad9854_vpp_mv,
                   actual_output);
        }
    }
}

/**
 * @brief  对比模型电路模式与手动增益模式
 * @param  None
 * @retval None
 */
void Test_ModeComparison(void)
{
    printf("\n=== 模式对比测试 ===\n");
    printf("测试频率: 1kHz, 目标输出: 500mV\n\n");
    
    AD9854_ControlParams_t params;
    double test_freq = 1000.0;
    double target_vpp = 500.0;
    
    // 测试模型电路模式
    printf("1. 模型电路模式:\n");
    AD9854_EnableModelCircuit(1);
    AD9854_SetTargetFrequency(test_freq);
    AD9854_SetTargetAmplitude(target_vpp);
    AD9854_GetControlParams(&params);
    
    printf("   模型电路增益: %.3f\n", params.model_circuit_gain);
    printf("   AD9854输出: %.1fmV\n", params.ad9854_vpp_mv);
    printf("   预期最终输出: %.1fmV\n", params.ad9854_vpp_mv * params.model_circuit_gain);
    
    // 测试手动增益模式
    printf("\n2. 手动增益模式 (增益=5.0):\n");
    AD9854_SetGainFactor(5.0);  // 这会自动禁用模型电路模式
    AD9854_SetTargetAmplitude(target_vpp);
    AD9854_GetControlParams(&params);
    
    printf("   手动增益系数: %.1f\n", params.gain_factor);
    printf("   AD9854输出: %.1fmV\n", params.ad9854_vpp_mv);
    printf("   预期最终输出: %.1fmV\n", params.ad9854_vpp_mv * params.gain_factor);
    printf("   模型电路模式: %s\n", params.use_model_circuit ? "启用" : "禁用");
}

/**
 * @brief  演示频率变化时的自动重新计算
 * @param  None
 * @retval None
 */
void Test_FrequencyChange(void)
{
    printf("\n=== 频率变化自动重新计算测试 ===\n");
    printf("目标输出: 200mV峰峰值\n\n");
    
    AD9854_ControlParams_t params;
    double target_vpp = 200.0;  // 200mV目标输出
    
    // 启用模型电路
    AD9854_EnableModelCircuit(1);
    AD9854_SetTargetAmplitude(target_vpp);
    
    printf("频率变化过程:\n");
    printf("频率\t\t增益\t\tAD9854输出\n");
    printf("----------------------------------------\n");
    
    // 模拟频率扫描
    double frequencies[] = {1000, 5000, 10000, 50000, 100000};
    int num_freqs = sizeof(frequencies) / sizeof(frequencies[0]);
    
    for (int i = 0; i < num_freqs; i++) {
        AD9854_SetTargetFrequency(frequencies[i]);
        AD9854_GetControlParams(&params);
        
        printf("%.0fHz\t\t%.3f\t\t%.1fmV\n", 
               frequencies[i], 
               params.model_circuit_gain,
               params.ad9854_vpp_mv);
    }
}

/**
 * @brief  主测试函数
 * @param  None
 * @retval None
 */
void ModelCircuit_TestMain(void)
{
    printf("AD9854模型电路功能测试\n");
    printf("======================\n");
    
    // 初始化AD9854
    if (AD9854_Init() != AD9854_OK) {
        printf("AD9854初始化失败!\n");
        return;
    }
    
    // 运行各项测试
    Test_ModelCircuitGain();
    Test_AutoGainCompensation();
    Test_ModeComparison();
    Test_FrequencyChange();
    
    printf("\n=== 测试完成 ===\n");
    printf("模型电路功能正常工作!\n");
}

/**
 * @brief  实际应用示例
 * @param  None
 * @retval None
 */
void ModelCircuit_ApplicationExample(void)
{
    printf("\n=== 实际应用示例 ===\n");
    
    // 场景：需要在1kHz频率下输出300mV峰峰值的信号
    printf("应用场景: 1kHz频率，300mV峰峰值输出\n\n");
    
    // 1. 初始化系统
    AD9854_Init();
    
    // 2. 启用模型电路自动补偿
    AD9854_EnableModelCircuit(1);
    printf("✓ 启用模型电路自动增益补偿\n");
    
    // 3. 设置目标参数
    AD9854_SetTargetFrequency(1000.0);    // 1kHz
    AD9854_SetTargetAmplitude(300.0);     // 300mV
    AD9854_EnableOutput(1);               // 使能输出
    printf("✓ 设置目标: 1kHz, 300mV峰峰值\n");
    
    // 4. 查看系统计算结果
    AD9854_ControlParams_t params;
    AD9854_GetControlParams(&params);
    
    printf("\n系统计算结果:\n");
    printf("- 目标频率: %.0f Hz\n", params.frequency_hz);
    printf("- 目标峰峰值: %.1f mV\n", params.target_vpp_mv);
    printf("- 模型电路增益: %.3f\n", params.model_circuit_gain);
    printf("- AD9854输出: %.1f mV\n", params.ad9854_vpp_mv);
    printf("- 幅度控制码: %d\n", params.amplitude_code);
    printf("- 预期最终输出: %.1f mV\n", params.ad9854_vpp_mv * params.model_circuit_gain);
    
    printf("\n✓ 系统已自动配置完成，可以输出目标信号\n");
}

/* 使用说明:
 * 
 * 1. 在main函数中调用 ModelCircuit_TestMain() 进行完整测试
 * 2. 调用 ModelCircuit_ApplicationExample() 查看实际应用示例
 * 3. 根据需要修改测试参数和频率点
 * 
 * 注意事项:
 * - 确保已正确连接AD9854硬件
 * - 高频时模型电路增益很小，可能无法达到目标输出
 * - 可以通过串口或调试器查看测试结果
 */
