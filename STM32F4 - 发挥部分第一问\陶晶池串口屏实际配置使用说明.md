# 陶晶池串口屏实际配置使用说明

## 📋 界面布局说明

### Page0 (主界面)
```
┌─────────────────────────────────────────────────────────────┐
│ 输出频率: [t2]  0.0                                    [n0] │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ 模拟电路电压输出值: [t4]  0.0                          [t0] │
│                                                        HZ   │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│                                                        [b0] │
│                                                   测量未知电路│
└─────────────────────────────────────────────────────────────┘
```

### Page1 (测量界面)
```
┌─────────────────────────────────────────────────────────────┐
│ 输出频率: [t2]  0.0                                    [n0] │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ 模拟电路电压输出值: [t4]  0.0                          [t0] │
│                                                        HZ   │
├─────────────────────────────────────────────────────────────┤
│ 未知电路滤波为: [t6]                                   [b0] │
│                                                      newtxt │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 控件功能说明

### Page0 控件
- **t2 (文本)**: 输出频率显示，实时显示当前波形频率，带单位 (Hz/kHz/MHz)
- **t4 (文本)**: 模拟电路电压输出值，显示考虑增益后的峰峰值 (mV/V)
- **n0 (数字)**: 数字键盘输入，点击弹出数字键盘设置数值
- **t0 (文本)**: 全键盘输入，点击弹出全键盘设置单位 (大小写不敏感)
- **b0 (按钮)**: 测量未知电路，按下进入Page1并发送 `printh 31` 事件

### Page1 控件
- **t2, t4, n0, t0**: 功能与Page0相同
- **t6 (文本)**: 滤波类型显示，显示测量结果 ("等待" 或 具体滤波类型)
- **b0 (按钮)**: 页面切换，按下返回Page0

## 🔧 API接口说明

### 核心显示函数
```c
/* 初始化并显示Page0 */
void TJC_HMI_ShowWelcome(void);

/* 设置t2控件 - 输出频率显示 (实时显示，带单位) */
void TJC_HMI_SetFrequencyDisplay(uint32_t frequency_hz);

/* 设置t4控件 - 模拟电路电压输出值 (考虑增益后的峰峰值) */
void TJC_HMI_SetVoltageDisplay(uint16_t voltage_mv);

/* 设置t6控件 - 滤波类型显示 */
void TJC_HMI_SetFilterTypeDisplay(const char* filter_type);
```

### 输入处理函数
```c
/* 处理n0控件数字键盘输入 */
void TJC_HMI_HandleNumberInput(uint32_t value);

/* 处理t0控件全键盘输入 (单位设置，大小写不敏感) */
void TJC_HMI_HandleUnitInput(const char* unit_text);

/* 处理b0按钮事件 */
void TJC_HMI_HandleButtonB0(uint8_t current_page);

/* 开始测量未知电路 (响应printh 31事件) */
void TJC_HMI_StartMeasurement(void);
```

### 测试函数
```c
/* 测试陶晶池串口屏所有功能 */
void TJC_HMI_TestAllFunctions(void);
```

## 📡 单位处理说明

### 支持的单位 (大小写不敏感)
- **频率单位**: Hz, hz, HZ, kHz, khz, KHZ, MHz, mhz, MHZ
- **电压单位**: mV, mv, MV, V, v

### 单位转换示例
```c
/* 用户输入 "5" + "MHz" → 5,000,000 Hz */
TJC_HMI_HandleNumberInput(5);
TJC_HMI_HandleUnitInput("MHz");

/* 用户输入 "1000" + "khz" → 1,000,000 Hz */
TJC_HMI_HandleNumberInput(1000);
TJC_HMI_HandleUnitInput("khz");  // 小写也支持

/* 用户输入 "21" + "mhz" → 21,000,000 Hz */
TJC_HMI_HandleNumberInput(21);
TJC_HMI_HandleUnitInput("mhz");  // 混合大小写也支持
```

## 🎮 操作流程

### 基本频率设置
1. 点击 **n0** 控件，弹出数字键盘
2. 输入数值 (如: 5)
3. 点击 **t0** 控件，弹出全键盘
4. 输入单位 (如: MHz, mhz, MHZ 都可以)
5. 系统自动计算并设置AD9910频率
6. **t2** 控件实时显示当前频率
7. **t4** 控件实时显示电路输出电压

### 测量未知电路
1. 在Page0点击 **b0** 按钮 "测量未知电路"
2. 自动切换到Page1
3. 系统发送 `printh 31` 事件
4. **t6** 控件显示 "等待"
5. 测量完成后显示滤波类型
6. 点击 **b0** 按钮返回Page0

## 🔍 调试和测试

### 完整功能测试
```c
void Test_TJC_HMI_Complete(void)
{
    /* 初始化 */
    TJC_HMI_Init();
    TJC_HMI_ShowWelcome();
    
    /* 运行完整测试 */
    TJC_HMI_TestAllFunctions();
}
```

### 单独功能测试
```c
/* 测试频率设置 */
TJC_HMI_HandleNumberInput(21);      // 输入21
TJC_HMI_HandleUnitInput("MHz");     // 输入MHz
// 结果: AD9910输出21MHz

/* 测试电压显示 */
TJC_HMI_SetVoltageDisplay(420);     // 显示420mV

/* 测试页面切换 */
TJC_HMI_HandleButtonB0(0);          // Page0 → Page1
TJC_HMI_HandleButtonB0(1);          // Page1 → Page0

/* 测试滤波类型显示 */
TJC_HMI_SetFilterTypeDisplay("低通滤波器");
```

## ⚠️ 注意事项

### 硬件连接
- **USART2_TX (PA2)** → 陶晶池串口屏 RX
- **USART2_RX (PA3)** → 陶晶池串口屏 TX
- **GND** → GND
- **VCC** → 3.3V/5V (根据串口屏要求)

### 软件配置
- 波特率: 115200
- 数据位: 8
- 停止位: 1
- 校验位: 无
- 流控: 无

### 频率范围
- **最小频率**: 1 Hz
- **最大频率**: 420 MHz (AD9910限制)
- **默认频率**: 5 MHz

### 电压范围
- **最小电压**: 10 mV
- **最大电压**: 5000 mV (5V)
- **默认电压**: 500 mV

## 🚀 集成示例

### 在main.c中的使用
```c
int main(void)
{
    /* 系统初始化 */
    SystemInit();
    
    /* 陶晶池串口屏初始化 */
    TJC_HMI_Init();
    TJC_HMI_ShowWelcome();
    
    /* AD9910控制系统初始化 */
    AD9910_Control_Init();
    
    /* 主循环 */
    while (1) {
        /* 处理串口屏事件 */
        TJC_HMI_Process();
        
        /* 其他任务 */
        Delay_ms(10);
    }
}
```

### 事件处理示例
```c
/* 在串口中断中调用 */
void USART2_IRQHandler(void)
{
    if (USART_GetITStatus(USART2, USART_IT_RXNE) != RESET) {
        uint8_t data = USART_ReceiveData(USART2);
        /* 数据会自动被TJC_HMI_Process()处理 */
    }
}
```

---

**文档版本**: v1.0  
**更新时间**: 2024-08-02  
**适用版本**: STM32F4 发挥部分第一问  
**状态**: ✅ 已完成基于实际配置的重写
