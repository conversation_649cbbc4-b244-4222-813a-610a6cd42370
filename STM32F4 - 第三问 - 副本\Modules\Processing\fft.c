/**
  ******************************************************************************
  * @file    fft.c
  * <AUTHOR>
  * @version V2.0
  * @date    2024
  * @brief   FFT计算模块实现 - 黄金参考版本
  *          严格按照CMSIS-DSP库标准调用流程
  ******************************************************************************
  * @attention
  * 
  * 黄金参考实现要求：
  * 1. 严格按照 init -> rfft -> cmplx_mag 三步执行
  * 2. 输入必须是长度为fft_size的实数数组
  * 3. 输出是长度为fft_size的幅度值数组(实际有用点数为fft_size/2)
  * 
  * Keil配置要求：
  * - 在C/C++选项的Define中添加：ARM_MATH_CM4, __FPU_PRESENT=1
  * - 链接库文件：arm_cortexM4lf_math.lib
  * - 包含路径：..\..\Libraries\CMSIS\DSP\Include
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "fft.h"
#include "systick.h"
#include <string.h>

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/

/** @defgroup FFT_Private_Defines FFT私有定义
  * @{
  */

// 最大支持的FFT点数
#define FFT_MAX_POINTS              4096U

/**
  * @}
  */

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/** @defgroup FFT_Private_Variables FFT私有变量
  * @{
  */

FFT_Handle_t g_fft_handle;                       ///< FFT句柄
volatile bool g_fft_computation_complete = false; ///< FFT计算完成标志

// 静态缓冲区 - 用于存放计算结果的复数数组
static float32_t s_complex_output[FFT_MAX_POINTS * 2]; // 大小是fft_size * 2

/**
  * @}
  */

/* Private function prototypes -----------------------------------------------*/
static bool FFT_ValidateSize(uint16_t fft_size);

/* Private functions ---------------------------------------------------------*/

/**
  * @brief  验证FFT大小是否有效
  * @param  fft_size: FFT点数
  * @retval true: 有效, false: 无效
  */
static bool FFT_ValidateSize(uint16_t fft_size)
{
    // CMSIS-DSP支持的FFT大小：16, 32, 64, 128, 256, 512, 1024, 2048, 4096
    switch (fft_size) {
        case 16:
        case 32:
        case 64:
        case 128:
        case 256:
        case 512:
        case 1024:
        case 2048:
        case 4096:
            return true;
        default:
            return false;
    }
}

/* Exported functions --------------------------------------------------------*/

/**
  * @brief  FFT模块初始化
  * @param  config: 配置参数指针
  * @retval 0: 成功, -1: 失败
  */
int8_t FFT_Init(FFT_Config_t* config)
{
    if (config == NULL) {
        return -1;
    }
    
    // 验证FFT大小
    if (!FFT_ValidateSize((uint16_t)config->fft_size)) {
        return -1;
    }
    
    // 初始化句柄
    g_fft_handle.config = *config;
    g_fft_handle.is_initialized = false;
    g_fft_handle.is_computing = false;
    g_fft_handle.result_ready = false;
    
    // 清空结果
    memset(&g_fft_handle.result, 0, sizeof(FFT_Result_t));
    memset(&g_fft_handle.stats, 0, sizeof(FFT_Stats_t));
    
    g_fft_handle.is_initialized = true;
    
    return 0;
}

/**
  * @brief  FFT模块反初始化
  * @param  None
  * @retval None
  */
void FFT_DeInit(void)
{
    g_fft_handle.is_initialized = false;
    g_fft_handle.is_computing = false;
    g_fft_handle.result_ready = false;
}

/**
  * @brief  计算实数FFT - 黄金参考实现
  * @param  input: 输入数据指针(长度必须为fft_size)
  * @param  length: 数据长度(必须等于配置的fft_size)
  * @retval 0: 成功, -1: 失败
  * @note   严格按照三步流程：init -> rfft -> cmplx_mag
  */
int8_t FFT_ComputeReal(const float32_t* input, uint32_t length)
{
    if (!g_fft_handle.is_initialized || input == NULL) {
        return -1;
    }
    
    if (length != g_fft_handle.config.fft_size) {
        return -1; // 长度必须匹配
    }
    
    if (g_fft_handle.is_computing) {
        return -1; // 正在计算中
    }
    
    g_fft_handle.is_computing = true;
    g_fft_computation_complete = false;
    
    uint32_t start_time = SysTick_GetTick();
    
#ifdef ARM_MATH_CM4
    // 黄金参考步骤1：初始化实数FFT实例
    arm_rfft_fast_instance_f32 fft_instance;
    arm_status status = arm_rfft_fast_init_f32(&fft_instance, (uint16_t)length);
    
    if (status != ARM_MATH_SUCCESS) {
        g_fft_handle.is_computing = false;
        return -1;
    }
    
    // 黄金参考步骤2：执行实数FFT
    // 输入是长度为fft_size的实数数组
    // 输出是长度为fft_size*2的复数数组 (格式: [R0, I0, R1, I1, ...])
    arm_rfft_fast_f32(&fft_instance, (float32_t*)input, s_complex_output, 0); // 0表示正向FFT
    
    // 黄金参考步骤3：计算复数结果的模值（即频谱的幅度）
    // 输入是长度为fft_size*2的复数数组
    // 输出是长度为fft_size的幅度值数组
    // 注意：实际有用的点数是 fft_size / 2
    if (g_fft_handle.result.magnitude == NULL) {
        // 如果没有外部提供缓冲区，使用内部缓冲区的一部分
        g_fft_handle.result.magnitude = s_complex_output; // 复用缓冲区
    }
    
    arm_cmplx_mag_f32(s_complex_output, g_fft_handle.result.magnitude, (uint16_t)length);
    
    // 更新结果信息
    g_fft_handle.result.bin_count = length / 2; // 有用的频率bin数量
    g_fft_handle.result.frequency_resolution = (float32_t)g_fft_handle.config.sample_rate / length;
    
    // 查找峰值
    uint32_t peak_index;
    arm_max_f32(g_fft_handle.result.magnitude, g_fft_handle.result.bin_count, 
                &g_fft_handle.result.peak_magnitude, &peak_index);
    g_fft_handle.result.peak_bin = peak_index;
    g_fft_handle.result.peak_frequency = peak_index * g_fft_handle.result.frequency_resolution;
    
#else
    // 如果没有定义ARM_MATH_CM4，返回错误
    g_fft_handle.is_computing = false;
    return -1;
#endif
    
    // 更新统计信息
    uint32_t computation_time = SysTick_GetTick() - start_time;
    g_fft_handle.stats.fft_count++;
    g_fft_handle.stats.avg_computation_time = 
        (g_fft_handle.stats.avg_computation_time * (g_fft_handle.stats.fft_count - 1) + computation_time) / 
        g_fft_handle.stats.fft_count;
    
    if (computation_time > g_fft_handle.stats.max_computation_time) {
        g_fft_handle.stats.max_computation_time = computation_time;
    }
    
    g_fft_handle.is_computing = false;
    g_fft_handle.result_ready = true;
    g_fft_computation_complete = true;
    
    return 0;
}

/**
  * @brief  获取FFT结果
  * @param  result: 结果结构体指针
  * @retval 0: 成功, -1: 失败
  */
int8_t FFT_GetResult(FFT_Result_t* result)
{
    if (!g_fft_handle.is_initialized || result == NULL || !g_fft_handle.result_ready) {
        return -1;
    }
    
    *result = g_fft_handle.result;
    
    return 0;
}

/**
  * @brief  获取统计信息
  * @param  stats: 统计信息结构体指针
  * @retval None
  */
void FFT_GetStats(FFT_Stats_t* stats)
{
    if (stats == NULL) {
        return;
    }
    
    *stats = g_fft_handle.stats;
}

/**
  * @brief  重置统计信息
  * @param  None
  * @retval None
  */
void FFT_ResetStats(void)
{
    memset(&g_fft_handle.stats, 0, sizeof(FFT_Stats_t));
}

/**
  * @brief  峰值检测
  * @param  threshold: 检测阈值
  * @param  min_distance: 最小间距
  * @retval 检测到的峰值数量
  */
uint32_t FFT_PeakDetection(float32_t threshold, uint32_t min_distance)
{
    if (!g_fft_handle.result_ready || g_fft_handle.result.magnitude == NULL) {
        return 0;
    }
    
    uint32_t peak_count = 0;
    uint32_t last_peak_index = 0;
    
    for (uint32_t i = 1; i < g_fft_handle.result.bin_count - 1; i++) {
        // 检查是否为局部最大值且超过阈值
        if (g_fft_handle.result.magnitude[i] > threshold &&
            g_fft_handle.result.magnitude[i] > g_fft_handle.result.magnitude[i-1] &&
            g_fft_handle.result.magnitude[i] > g_fft_handle.result.magnitude[i+1]) {
            
            // 检查与上一个峰值的距离
            if (peak_count == 0 || (i - last_peak_index) >= min_distance) {
                peak_count++;
                last_peak_index = i;
            }
        }
    }
    
    g_fft_handle.stats.peak_detect_count++;
    
    return peak_count;
}

/************************ (C) COPYRIGHT 嵌入式竞赛团队 *****END OF FILE****/
