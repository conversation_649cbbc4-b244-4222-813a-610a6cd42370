/**
  ******************************************************************************
  * @file    ad9910_hal.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   AD9910硬件抽象层实现文件 - STM32F4平台适配
  ******************************************************************************
  * @attention
  *
  * 本文件实现AD9910 DDS芯片的底层硬件接口功能
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "ad9910_hal.h"
#include "../Core/systick.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
/* Private functions ---------------------------------------------------------*/

/**
 * @brief  AD9910硬件初始化
 * @param  None
 * @retval None
 */
void AD9910_HAL_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;

    /* 使能GPIO时钟 */
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA | RCC_AHB1Periph_GPIOB | 
                           RCC_AHB1Periph_GPIOC, ENABLE);

    /* 配置输出引脚 */
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;

    /* GPIOA输出引脚配置 */
    GPIO_InitStructure.GPIO_Pin = AD9910_SDIO_PIN | AD9910_RST_PIN | 
                                  AD9910_SCK_PIN | AD9910_DRC_PIN;
    GPIO_Init(GPIOA, &GPIO_InitStructure);

    /* GPIOB输出引脚配置 */
    GPIO_InitStructure.GPIO_Pin = AD9910_CSB_PIN;
    GPIO_Init(GPIOB, &GPIO_InitStructure);

    /* GPIOC输出引脚配置 */
    GPIO_InitStructure.GPIO_Pin = AD9910_PWR_PIN | AD9910_DPH_PIN | 
                                  AD9910_IOUP_PIN | AD9910_PF0_PIN | 
                                  AD9910_PF1_PIN | AD9910_PF2_PIN | 
                                  AD9910_OSK_PIN;
    GPIO_Init(GPIOC, &GPIO_InitStructure);

    /* 配置输入引脚 (DRO) */
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_InitStructure.GPIO_Pin = AD9910_DRO_PIN;
    GPIO_Init(GPIOC, &GPIO_InitStructure);

    /* 设置初始状态 */
    AD9910_PWR_LOW();      // 电源关闭
    AD9910_CSB_HIGH();     // 片选无效
    AD9910_SCK_LOW();      // 时钟低电平
    AD9910_SDIO_LOW();     // 数据线低电平
    AD9910_RST_HIGH();     // 复位有效
    AD9910_DPH_LOW();      // 数据保持无效
    AD9910_DRC_LOW();      // 数据控制无效
    AD9910_OSK_LOW();      // 输出移位键控无效
    AD9910_IOUP_LOW();     // I/O更新无效
    
    /* Profile选择引脚设置为0 */
    AD9910_PF0_LOW();
    AD9910_PF1_LOW();
    AD9910_PF2_LOW();
}

/**
 * @brief  AD9910发送8位数据
 * @param  data: 要发送的8位数据
 * @retval None
 */
void AD9910_HAL_Send8Bits(uchar data)
{
    uchar i, bit_mask;
    
    bit_mask = 0x80;  // 从最高位开始发送
    AD9910_SCK_LOW();
    
    for (i = 0; i < 8; i++) {
        // 设置数据线
        if ((data & bit_mask) == 0) {
            AD9910_SDIO_LOW();
        } else {
            AD9910_SDIO_HIGH();
        }
        
        // 时钟上升沿
        AD9910_SCK_HIGH();
        bit_mask = bit_mask >> 1;
        
        // 时钟下降沿
        AD9910_SCK_LOW();
    }
}

/**
 * @brief  AD9910硬件复位
 * @param  None
 * @retval None
 */
void AD9910_HAL_Reset(void)
{
    AD9910_RST_HIGH();
    AD9910_HAL_DelayMs(5);
    AD9910_RST_LOW();
    AD9910_HAL_DelayMs(1);
}

/**
 * @brief  AD9910 I/O更新
 * @param  None
 * @retval None
 */
void AD9910_HAL_IOUpdate(void)
{
    AD9910_IOUP_HIGH();
    AD9910_HAL_DelayUs(1);  // 短暂延时
    AD9910_IOUP_LOW();
}

/**
 * @brief  AD9910电源控制
 * @param  enable: 1-使能电源，0-关闭电源
 * @retval None
 */
void AD9910_HAL_PowerControl(uint8_t enable)
{
    if (enable) {
        AD9910_PWR_LOW();   // 低电平使能电源
    } else {
        AD9910_PWR_HIGH();  // 高电平关闭电源
    }
}

/**
 * @brief  AD9910 Profile选择
 * @param  profile: Profile编号 (0-7)
 * @retval None
 */
void AD9910_HAL_SelectProfile(uint8_t profile)
{
    // 设置Profile选择引脚
    if (profile & 0x01) {
        AD9910_PF0_HIGH();
    } else {
        AD9910_PF0_LOW();
    }
    
    if (profile & 0x02) {
        AD9910_PF1_HIGH();
    } else {
        AD9910_PF1_LOW();
    }
    
    if (profile & 0x04) {
        AD9910_PF2_HIGH();
    } else {
        AD9910_PF2_LOW();
    }
}

/**
 * @brief  微秒级延时
 * @param  us: 延时微秒数
 * @retval None
 */
void AD9910_HAL_DelayUs(uint32_t us)
{
    // 使用系统滴答定时器实现微秒延时
    // 假设系统时钟168MHz，每个时钟周期约6ns
    // 1us需要约168个时钟周期
    volatile uint32_t count = us * 42;  // 粗略估算
    while (count--) {
        __NOP();
    }
}

/**
 * @brief  毫秒级延时
 * @param  ms: 延时毫秒数
 * @retval None
 */
void AD9910_HAL_DelayMs(uint32_t ms)
{
    // 使用SysTick延时函数
    SysTick_Delay_Ms(ms);
}

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
