# AD9854模型电路集成说明

## 概述

成功将已知模型电路的传递函数集成到AD9854控制系统中，实现了频率相关的自动增益补偿。系统现在可以根据设定的目标频率自动计算模型电路的增益，并相应调整AD9854的输出，确保最终输出达到用户设定的峰峰值。

## 模型电路传递函数

### 数学表达式
$$H(s) = \frac{5}{10^{-8}s^2 + 3 \times 10^{-4}s + 1}$$

### 参数分析
- **分子**: 5 (直流增益)
- **分母**: 二阶系统
  - $s^2$ 系数: $10^{-8}$ 
  - $s$ 系数: $3 \times 10^{-4}$
  - 常数项: 1

### 系统特性
这是一个**二阶低通滤波器**，具有以下特性：
- **直流增益**: 5 (约14dB)
- **自然频率**: $\omega_n = \sqrt{\frac{1}{10^{-8}}} = 10^4$ rad/s ≈ 1.59kHz
- **阻尼比**: $\zeta = \frac{3 \times 10^{-4}}{2\sqrt{10^{-8}}} = 1.5$ (过阻尼系统)

## 频率响应计算

### 幅度响应公式
$$|H(j\omega)| = \frac{5}{\sqrt{(1-10^{-8}\omega^2)^2 + (3 \times 10^{-4}\omega)^2}}$$

其中 $\omega = 2\pi f$ (角频率)

### 关键频率点的增益

| 频率 | 角频率 ω (rad/s) | 增益 \|H(jω)\| | 增益 (dB) |
|------|------------------|----------------|-----------|
| 100Hz | 628 | 4.97 | 13.9dB |
| 1kHz | 6283 | 4.70 | 13.4dB |
| 10kHz | 62832 | 2.50 | 8.0dB |
| 100kHz | 628318 | 0.50 | -6.0dB |
| 1MHz | 6283185 | 0.05 | -26.0dB |
| 5MHz | 31415927 | 0.002 | -54.0dB |

## 软件实现

### 核心算法函数

```c
/**
 * @brief  计算已知模型电路在指定频率下的增益
 * @param  frequency_hz: 频率 (Hz)
 * @retval double 模型电路增益 (幅度)
 */
double AD9854_CalculateModelCircuitGain(double frequency_hz)
{
    double omega = 2.0 * 3.14159265359 * frequency_hz;  // ω = 2πf
    double omega2 = omega * omega;                       // ω^2
    
    // 计算分母各项
    double real_part = 1.0 - MODEL_CIRCUIT_A2 * omega2;  // (1 - 10^-8*ω^2)
    double imag_part = MODEL_CIRCUIT_A1 * omega;          // (3*10^-4*ω)
    
    // 计算幅度响应
    double magnitude = MODEL_CIRCUIT_DC_GAIN / 
                      sqrt(real_part * real_part + imag_part * imag_part);
    
    return magnitude;
}
```

### 自动增益补偿

```c
/**
 * @brief  使用已知模型电路计算AD9854所需输出
 * @param  target_vpp_mv: 目标峰峰值 (mV)
 * @param  frequency_hz: 频率 (Hz)
 * @retval double AD9854所需输出峰峰值 (mV)
 */
double AD9854_CalculateRequiredOutputWithModel(double target_vpp_mv, double frequency_hz)
{
    double model_gain = AD9854_CalculateModelCircuitGain(frequency_hz);
    return AD9854_CalculateRequiredOutput(target_vpp_mv, model_gain);
}
```

## 使用方法

### 1. 启用模型电路计算

```c
// 启用模型电路自动增益补偿
AD9854_EnableModelCircuit(1);
```

### 2. 设置目标参数

```c
// 设置频率 (系统自动计算该频率下的增益)
AD9854_SetTargetFrequency(5000000.0);  // 5MHz

// 设置目标峰峰值 (系统自动补偿模型电路增益)
AD9854_SetTargetAmplitude(500.0);      // 500mV最终输出
```

### 3. 查询计算结果

```c
AD9854_ControlParams_t params;
AD9854_GetControlParams(&params);

printf("目标频率: %.0f Hz\n", params.frequency_hz);
printf("目标峰峰值: %.1f mV\n", params.target_vpp_mv);
printf("模型电路增益: %.3f\n", params.model_circuit_gain);
printf("AD9854输出: %.1f mV\n", params.ad9854_vpp_mv);
```

## 实际应用示例

### 示例1: 5MHz信号输出

**目标**: 最终输出5MHz、500mV峰峰值的信号

**计算过程**:
1. 模型电路在5MHz的增益: ≈ 0.002
2. AD9854需要输出: 500mV ÷ 0.002 = 250V (超出范围)
3. 系统自动限制到最大输出: 500mV
4. 实际最终输出: 500mV × 0.002 = 1mV

**结论**: 5MHz频率下模型电路增益极小，无法达到500mV输出

### 示例2: 1kHz信号输出

**目标**: 最终输出1kHz、500mV峰峰值的信号

**计算过程**:
1. 模型电路在1kHz的增益: ≈ 4.70
2. AD9854需要输出: 500mV ÷ 4.70 ≈ 106mV
3. 系统设置AD9854输出106mV
4. 实际最终输出: 106mV × 4.70 ≈ 500mV

**结论**: 1kHz频率下可以精确达到目标输出

### 示例3: 100kHz信号输出

**目标**: 最终输出100kHz、500mV峰峰值的信号

**计算过程**:
1. 模型电路在100kHz的增益: ≈ 0.50
2. AD9854需要输出: 500mV ÷ 0.50 = 1000mV (超出范围)
3. 系统自动限制到最大输出: 500mV
4. 实际最终输出: 500mV × 0.50 = 250mV

**结论**: 100kHz频率下只能达到250mV输出

## 系统优势

### 1. 自动化补偿
- 用户只需设定目标频率和峰峰值
- 系统自动计算模型电路增益
- 自动调整AD9854输出以达到目标

### 2. 频率相关增益
- 不同频率下自动使用不同的增益值
- 精确的数学模型计算
- 实时响应频率变化

### 3. 智能限制
- 自动检测AD9854输出范围限制
- 防止设置超出硬件能力的参数
- 提供实际可达到的输出预测

### 4. 灵活切换
- 可以在模型电路模式和手动增益模式间切换
- 保留原有的手动增益设置功能
- 适应不同的应用场景

## 控制接口

### 新增API函数

```c
// 启用/禁用模型电路计算
AD9854_StatusTypeDef AD9854_EnableModelCircuit(uint8_t enable);

// 计算模型电路增益
double AD9854_CalculateModelCircuitGain(double frequency_hz);

// 使用模型电路计算所需输出
double AD9854_CalculateRequiredOutputWithModel(double target_vpp_mv, double frequency_hz);
```

### 扩展的控制参数

```c
typedef struct {
    double frequency_hz;        // 输出频率 (Hz)
    double target_vpp_mv;       // 目标峰峰值 (mV)
    double ad9854_vpp_mv;       // AD9854实际输出峰峰值 (mV)
    uint16_t amplitude_code;    // AD9854幅度控制码 (0-4095)
    double gain_factor;         // 手动增益系数
    double model_circuit_gain;  // 模型电路增益 (自动计算)
    uint8_t enable;             // 输出使能标志
    uint8_t use_model_circuit;  // 是否使用模型电路计算
} AD9854_ControlParams_t;
```

## 应用建议

### 1. 低频应用 (< 10kHz)
- 模型电路增益接近5，效果良好
- 可以精确达到目标输出
- 推荐使用模型电路模式

### 2. 中频应用 (10kHz - 100kHz)
- 模型电路增益逐渐下降
- 需要更大的AD9854输出
- 注意检查输出范围限制

### 3. 高频应用 (> 100kHz)
- 模型电路增益很小
- 可能无法达到目标输出
- 建议降低目标峰峰值或使用手动增益模式

## 总结

模型电路的集成为AD9854控制系统提供了更加智能和精确的增益补偿能力。系统现在可以：

1. **自动适应频率变化** - 根据传递函数实时计算增益
2. **精确控制输出** - 在模型电路能力范围内精确达到目标
3. **智能范围管理** - 自动处理硬件限制和参数边界
4. **保持灵活性** - 支持模型电路和手动增益两种模式

这为电赛G题的电路模型探究提供了强有力的技术支持，使得信号源能够精确适应已知电路的特性。
