/**
  ******************************************************************************
  * @file    uart_hmi.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   串口屏(HMI)通信模块实现文件
  ******************************************************************************
  * @attention
  *
  * 本文件实现与串口屏的通信功能
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "uart_hmi.h"
#include "../Control/ad9910_control.h"
#include <stdio.h>
#include <string.h>

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/* 接收缓冲区和状态 */
static uint8_t hmi_rx_buffer[HMI_RX_BUFFER_SIZE];
static uint16_t hmi_rx_status = 0;  // 高位标志接收状态，低位为数据长度
static UartRxStatus_t rx_status = UART_RX_IDLE;

/* Private function prototypes -----------------------------------------------*/
/* Private functions ---------------------------------------------------------*/

/**
 * @brief  HMI串口初始化
 * @param  baud_rate: 波特率
 * @retval None
 */
void HMI_UART_Init(uint32_t baud_rate)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    USART_InitTypeDef USART_InitStructure;
    NVIC_InitTypeDef NVIC_InitStructure;

    /* 使能时钟 */
    RCC_AHB1PeriphClockCmd(HMI_USART_TX_RCC, ENABLE);
    RCC_APB2PeriphClockCmd(HMI_USART_RCC, ENABLE);

    /* 配置GPIO复用功能 */
    GPIO_PinAFConfig(HMI_USART_TX_PORT, HMI_USART_TX_AF_PIN, HMI_USART_AF);
    GPIO_PinAFConfig(HMI_USART_RX_PORT, HMI_USART_RX_AF_PIN, HMI_USART_AF);

    /* 配置TX引脚 */
    GPIO_StructInit(&GPIO_InitStructure);
    GPIO_InitStructure.GPIO_Pin = HMI_USART_TX_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(HMI_USART_TX_PORT, &GPIO_InitStructure);

    /* 配置RX引脚 */
    GPIO_InitStructure.GPIO_Pin = HMI_USART_RX_PIN;
    GPIO_Init(HMI_USART_RX_PORT, &GPIO_InitStructure);

    /* 配置USART */
    USART_DeInit(HMI_USART);
    USART_StructInit(&USART_InitStructure);
    USART_InitStructure.USART_BaudRate = baud_rate;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_Init(HMI_USART, &USART_InitStructure);

    /* 清除标志位 */
    USART_ClearFlag(HMI_USART, USART_FLAG_RXNE);

    /* 使能串口 */
    USART_Cmd(HMI_USART, ENABLE);

    /* 使能接收中断 */
    USART_ITConfig(HMI_USART, USART_IT_RXNE, ENABLE);

    /* 配置中断 */
    NVIC_InitStructure.NVIC_IRQChannel = USART1_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);

    /* 初始化接收状态 */
    hmi_rx_status = 0;
    rx_status = UART_RX_IDLE;
}

/**
 * @brief  发送单个字符
 * @param  ch: 要发送的字符
 * @retval None
 */
void HMI_SendChar(uint8_t ch)
{
    USART_SendData(HMI_USART, ch);
    while (RESET == USART_GetFlagStatus(HMI_USART, USART_FLAG_TXE)) {}
}

/**
 * @brief  发送字符串
 * @param  str: 要发送的字符串
 * @retval None
 */
void HMI_SendString(uint8_t *str)
{
    while (str && *str) {
        HMI_SendChar(*str++);
    }
}

/**
 * @brief  发送字符串到HMI文本控件
 * @param  control_name: 控件名称
 * @param  text: 要显示的文本
 * @retval None
 */
void HMI_SendText(const char* control_name, const char* text)
{
    printf("%s.txt=\"%s\"%s", control_name, text, HMI_CMD_SUFFIX);
}

/**
 * @brief  发送数字到HMI数字控件
 * @param  control_name: 控件名称
 * @param  number: 要显示的数字
 * @retval None
 */
void HMI_SendNumber(const char* control_name, int32_t number)
{
    printf("%s.val=%ld%s", control_name, number, HMI_CMD_SUFFIX);
}

/**
 * @brief  发送浮点数到HMI控件
 * @param  control_name: 控件名称
 * @param  value: 要显示的浮点数
 * @param  decimal_places: 小数位数
 * @retval None
 */
void HMI_SendFloat(const char* control_name, float value, uint8_t decimal_places)
{
    int32_t scaled_value = (int32_t)(value * 100);  // 默认2位小数
    if (decimal_places == 1) {
        scaled_value = (int32_t)(value * 10);
    } else if (decimal_places == 3) {
        scaled_value = (int32_t)(value * 1000);
    }
    
    printf("%s.val=%ld%s", control_name, scaled_value, HMI_CMD_SUFFIX);
}

/**
 * @brief  发送进度条值
 * @param  control_name: 进度条控件名称
 * @param  value: 进度值 (0-100)
 * @retval None
 */
void HMI_SendProgress(const char* control_name, uint8_t value)
{
    if (value > 100) value = 100;
    printf("%s.val=%d%s", control_name, value, HMI_CMD_SUFFIX);
}

/**
 * @brief  设置控件可见性
 * @param  control_name: 控件名称
 * @param  visible: true-可见, false-隐藏
 * @retval None
 */
void HMI_SetVisible(const char* control_name, bool visible)
{
    printf("vis %s,%d%s", control_name, visible ? 1 : 0, HMI_CMD_SUFFIX);
}

/**
 * @brief  设置控件使能状态
 * @param  control_name: 控件名称
 * @param  enabled: true-使能, false-禁用
 * @retval None
 */
void HMI_SetEnabled(const char* control_name, bool enabled)
{
    printf("tsw %s,%d%s", control_name, enabled ? 1 : 0, HMI_CMD_SUFFIX);
}

/**
 * @brief  获取接收缓冲区数据
 * @param  buffer: 数据缓冲区
 * @param  max_length: 最大长度
 * @retval 实际接收的数据长度
 */
uint16_t HMI_GetReceivedData(uint8_t* buffer, uint16_t max_length)
{
    if (buffer == NULL || max_length == 0) return 0;
    
    uint16_t data_length = hmi_rx_status & 0x3FFF;  // 获取数据长度
    if (data_length > max_length) data_length = max_length;
    
    memcpy(buffer, hmi_rx_buffer, data_length);
    return data_length;
}

/**
 * @brief  检查是否有完整命令接收
 * @retval true-有完整命令, false-无完整命令
 */
bool HMI_HasCompleteCommand(void)
{
    return (hmi_rx_status & 0x8000) != 0;  // 检查完成标志位
}

/**
 * @brief  清空接收缓冲区
 * @retval None
 */
void HMI_ClearReceiveBuffer(void)
{
    hmi_rx_status = 0;
    rx_status = UART_RX_IDLE;
    memset(hmi_rx_buffer, 0, sizeof(hmi_rx_buffer));
}

/**
 * @brief  获取接收状态
 * @retval UartRxStatus_t 接收状态
 */
UartRxStatus_t HMI_GetReceiveStatus(void)
{
    return rx_status;
}

/* ==================== AD9910专用HMI函数 ==================== */

/**
 * @brief  显示AD9910频率
 * @param  frequency_hz: 频率值 (Hz)
 * @retval None
 */
void HMI_DisplayFrequency(uint32_t frequency_hz)
{
    // 显示频率，单位转换为更易读的格式
    if (frequency_hz >= 1000000) {
        // MHz显示
        float freq_mhz = (float)frequency_hz / 1000000.0f;
        HMI_SendFloat("freq", freq_mhz, 2);
        HMI_SendText("freq_unit", "MHz");
    } else if (frequency_hz >= 1000) {
        // kHz显示
        float freq_khz = (float)frequency_hz / 1000.0f;
        HMI_SendFloat("freq", freq_khz, 1);
        HMI_SendText("freq_unit", "kHz");
    } else {
        // Hz显示
        HMI_SendNumber("freq", frequency_hz);
        HMI_SendText("freq_unit", "Hz");
    }
}

/**
 * @brief  显示AD9910幅度
 * @param  amplitude_mv: 幅度值 (mV)
 * @retval None
 */
void HMI_DisplayAmplitude(uint16_t amplitude_mv)
{
    if (amplitude_mv >= 1000) {
        // V显示
        float amp_v = (float)amplitude_mv / 1000.0f;
        HMI_SendFloat("amplitude", amp_v, 2);
        HMI_SendText("amp_unit", "V");
    } else {
        // mV显示
        HMI_SendNumber("amplitude", amplitude_mv);
        HMI_SendText("amp_unit", "mV");
    }
}

/**
 * @brief  显示增益系数
 * @param  gain: 增益值
 * @retval None
 */
void HMI_DisplayGain(double gain)
{
    HMI_SendFloat("gain", (float)gain, 2);
}

/**
 * @brief  显示输出状态
 * @param  enabled: 输出状态
 * @retval None
 */
void HMI_DisplayOutputStatus(bool enabled)
{
    HMI_SendText("output_status", enabled ? "ON" : "OFF");
    HMI_SendNumber("output_led", enabled ? 1 : 0);
}

/**
 * @brief  显示系统状态
 * @param  status_text: 状态文本
 * @retval None
 */
void HMI_DisplaySystemStatus(const char* status_text)
{
    HMI_SendText("system_status", status_text);
}

/**
 * @brief  显示错误信息
 * @param  error_text: 错误文本
 * @retval None
 */
void HMI_DisplayError(const char* error_text)
{
    HMI_SendText("error_msg", error_text);
}

/**
 * @brief  更新所有AD9910参数显示
 * @retval None
 */
void HMI_UpdateAllParameters(void)
{
    SystemParams_t params;
    if (AD9910_Control_GetParams(&params) == CTRL_STATUS_OK) {
        HMI_DisplayFrequency(params.frequency_hz);
        HMI_DisplayAmplitude(params.target_amplitude_mv);
        HMI_DisplayGain(params.gain_factor);
        HMI_DisplayOutputStatus(params.output_enabled);
        HMI_DisplaySystemStatus("Ready");
    } else {
        HMI_DisplaySystemStatus("Error");
    }
}

/**
 * @brief  显示欢迎界面
 * @retval None
 */
void HMI_ShowWelcomeScreen(void)
{
    HMI_SendText("title", "AD9910 DDS Generator");
    HMI_SendText("version", "V3.0");
    HMI_SendText("status", "Initializing...");
}

/**
 * @brief  串口中断处理函数 (需要在stm32f4xx_it.c中调用)
 * @retval None
 */
void HMI_UART_IRQHandler(void)
{
    if (USART_GetITStatus(HMI_USART, USART_IT_RXNE) != RESET) {
        uint8_t received_char = USART_ReceiveData(HMI_USART);
        
        if ((hmi_rx_status & 0x8000) == 0) {  // 接收未完成
            if (hmi_rx_status & 0x4000) {  // 已收到0x0D
                if (received_char != HMI_CMD_END_CHAR2) {
                    hmi_rx_status = 0;  // 接收错误，重新开始
                    rx_status = UART_RX_ERROR;
                } else {
                    hmi_rx_status |= 0x8000;  // 接收完成
                    rx_status = UART_RX_COMPLETE;
                }
            } else {  // 还没收到0x0D
                if (received_char == HMI_CMD_END_CHAR1) {
                    hmi_rx_status |= 0x4000;
                } else {
                    hmi_rx_buffer[hmi_rx_status & 0x3FFF] = received_char;
                    hmi_rx_status++;
                    rx_status = UART_RX_RECEIVING;
                    
                    if ((hmi_rx_status & 0x3FFF) > (HMI_RX_BUFFER_SIZE - 1)) {
                        hmi_rx_status = 0;  // 缓冲区溢出，重新开始
                        rx_status = UART_RX_ERROR;
                    }
                }
            }
        }
    }
}

/* printf重定向到HMI串口 */
#if !defined(__MICROLIB)
#if (__ARMCLIB_VERSION <= 6000000)
struct __FILE {
    int handle;
};
#endif

FILE __stdout;

void _sys_exit(int x) {
    x = x;
}
#endif

int fputc(int ch, FILE *f)
{
    USART_SendData(HMI_USART, (uint8_t)ch);
    while (RESET == USART_GetFlagStatus(HMI_USART, USART_FLAG_TXE)) {}
    return ch;
}

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
