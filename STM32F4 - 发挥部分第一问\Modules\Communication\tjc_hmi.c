/**
  ******************************************************************************
  * @file    tjc_hmi.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   陶晶池串口屏驱动实现文件
  ******************************************************************************
  * @attention
  *
  * 本文件实现陶晶池串口屏的通信功能
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "tjc_hmi.h"
#include "../Control/ad9910_control.h"
#include "../HardWare/systick.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <math.h>

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
#define TJC_EVENT_QUEUE_SIZE        8       ///< 事件队列大小
#define TJC_CMD_TIMEOUT             100     ///< 命令超时时间 (ms)

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/* 串口状态变量 */
static bool tjc_initialized = false;
static TJC_EventCallback_t event_callback = NULL;

/* 接收缓冲区和状态 */
static uint8_t rx_buffer[TJC_RX_BUFFER_SIZE];
static uint16_t rx_index = 0;
static bool rx_complete = false;

/* 事件队列 */
static TJC_Event_t event_queue[TJC_EVENT_QUEUE_SIZE];
static uint8_t event_queue_head = 0;
static uint8_t event_queue_tail = 0;
static uint8_t event_queue_count = 0;

/* Private function prototypes -----------------------------------------------*/
static void TJC_UART_Init(void);
static void TJC_SendByte(uint8_t byte);
static void TJC_SendString(const char* str);
static void TJC_ProcessReceivedData(void);
static void TJC_ParseTouchEvent(uint8_t* data, uint16_t length);
static void TJC_AddEvent(TJC_Event_t* event);
static uint32_t TJC_GetTick(void);

/* Private functions ---------------------------------------------------------*/

/**
 * @brief  陶晶池串口屏初始化
 * @param  None
 * @retval None
 */
void TJC_HMI_Init(void)
{
    /* 串口初始化 */
    TJC_UART_Init();
    
    /* 初始化状态变量 */
    tjc_initialized = true;
    rx_index = 0;
    rx_complete = false;
    
    /* 清空事件队列 */
    event_queue_head = 0;
    event_queue_tail = 0;
    event_queue_count = 0;
    memset(event_queue, 0, sizeof(event_queue));
    
    /* 短暂延时，等待串口屏启动 */
    Delay_ms(500);
    
    /* 发送初始化命令 */
    TJC_HMI_SendCommand("bkcmd=3");  // 设置返回数据格式
    Delay_ms(10);
}

/**
 * @brief  串口初始化
 * @param  None
 * @retval None
 */
static void TJC_UART_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    USART_InitTypeDef USART_InitStructure;
    NVIC_InitTypeDef NVIC_InitStructure;

    /* 使能时钟 */
    RCC_AHB1PeriphClockCmd(TJC_USART_TX_RCC, ENABLE);
    RCC_APB2PeriphClockCmd(TJC_USART_RCC, ENABLE);

    /* 配置GPIO复用功能 */
    GPIO_PinAFConfig(TJC_USART_TX_PORT, TJC_USART_TX_AF_PIN, TJC_USART_AF);
    GPIO_PinAFConfig(TJC_USART_RX_PORT, TJC_USART_RX_AF_PIN, TJC_USART_AF);

    /* 配置TX引脚 */
    GPIO_StructInit(&GPIO_InitStructure);
    GPIO_InitStructure.GPIO_Pin = TJC_USART_TX_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(TJC_USART_TX_PORT, &GPIO_InitStructure);

    /* 配置RX引脚 */
    GPIO_InitStructure.GPIO_Pin = TJC_USART_RX_PIN;
    GPIO_Init(TJC_USART_RX_PORT, &GPIO_InitStructure);

    /* 配置USART */
    USART_DeInit(TJC_USART);
    USART_StructInit(&USART_InitStructure);
    USART_InitStructure.USART_BaudRate = TJC_BAUD_RATE;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_Init(TJC_USART, &USART_InitStructure);

    /* 清除标志位 */
    USART_ClearFlag(TJC_USART, USART_FLAG_RXNE);

    /* 使能串口 */
    USART_Cmd(TJC_USART, ENABLE);

    /* 使能接收中断 */
    USART_ITConfig(TJC_USART, USART_IT_RXNE, ENABLE);

    /* 配置中断 */
    NVIC_InitStructure.NVIC_IRQChannel = USART1_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);
}

/**
 * @brief  发送单个字节
 * @param  byte: 要发送的字节
 * @retval None
 */
static void TJC_SendByte(uint8_t byte)
{
    USART_SendData(TJC_USART, byte);
    while (RESET == USART_GetFlagStatus(TJC_USART, USART_FLAG_TXE)) {}
}

/**
 * @brief  发送字符串
 * @param  str: 要发送的字符串
 * @retval None
 */
static void TJC_SendString(const char* str)
{
    while (*str) {
        TJC_SendByte(*str++);
    }
}

/**
 * @brief  发送命令到陶晶池串口屏
 * @param  cmd: 命令字符串
 * @retval None
 */
void TJC_HMI_SendCommand(const char* cmd)
{
    if (!tjc_initialized || cmd == NULL) return;
    
    /* 发送命令内容 */
    TJC_SendString(cmd);
    
    /* 发送结束符 */
    TJC_SendByte(0xFF);
    TJC_SendByte(0xFF);
    TJC_SendByte(0xFF);
}

/**
 * @brief  设置文本控件内容
 * @param  obj_name: 控件名称
 * @param  text: 文本内容
 * @retval None
 */
void TJC_HMI_SetText(const char* obj_name, const char* text)
{
    char cmd[128];
    snprintf(cmd, sizeof(cmd), "%s.txt=\"%s\"", obj_name, text);
    TJC_HMI_SendCommand(cmd);
}

/**
 * @brief  设置数值控件内容
 * @param  obj_name: 控件名称
 * @param  value: 数值
 * @retval None
 */
void TJC_HMI_SetValue(const char* obj_name, int32_t value)
{
    char cmd[64];
    snprintf(cmd, sizeof(cmd), "%s.val=%ld", obj_name, value);
    TJC_HMI_SendCommand(cmd);
}

/**
 * @brief  设置进度条/滑块值
 * @param  obj_name: 控件名称
 * @param  value: 进度值 (0-100)
 * @retval None
 */
void TJC_HMI_SetProgress(const char* obj_name, uint8_t value)
{
    if (value > 100) value = 100;
    TJC_HMI_SetValue(obj_name, value);
}

/**
 * @brief  切换页面
 * @param  page_id: 页面ID
 * @retval None
 */
void TJC_HMI_ChangePage(uint8_t page_id)
{
    char cmd[32];
    snprintf(cmd, sizeof(cmd), "page %d", page_id);
    TJC_HMI_SendCommand(cmd);
}

/**
 * @brief  设置控件可见性
 * @param  obj_name: 控件名称
 * @param  visible: true-可见, false-隐藏
 * @retval None
 */
void TJC_HMI_SetVisible(const char* obj_name, bool visible)
{
    char cmd[64];
    snprintf(cmd, sizeof(cmd), "vis %s,%d", obj_name, visible ? 1 : 0);
    TJC_HMI_SendCommand(cmd);
}

/**
 * @brief  设置控件颜色
 * @param  obj_name: 控件名称
 * @param  color: 颜色值 (RGB565)
 * @retval None
 */
void TJC_HMI_SetColor(const char* obj_name, uint16_t color)
{
    char cmd[64];
    snprintf(cmd, sizeof(cmd), "%s.bco=%d", obj_name, color);
    TJC_HMI_SendCommand(cmd);
}

/**
 * @brief  陶晶池串口屏处理 (主循环调用)
 * @param  None
 * @retval None
 */
void TJC_HMI_Process(void)
{
    if (!tjc_initialized) return;
    
    /* 处理接收到的数据 */
    if (rx_complete) {
        TJC_ProcessReceivedData();
        rx_complete = false;
        rx_index = 0;
    }
}

/**
 * @brief  处理接收到的数据
 * @param  None
 * @retval None
 */
static void TJC_ProcessReceivedData(void)
{
    if (rx_index < 3) return; // 至少需要3个字节的结束符
    
    /* 检查是否是触摸事件 */
    if (rx_buffer[0] == 0x65) { // 触摸事件标识
        TJC_ParseTouchEvent(rx_buffer, rx_index);
    }
    
    /* 这里可以添加其他类型事件的解析 */
}

/**
 * @brief  解析触摸事件
 * @param  data: 数据缓冲区
 * @param  length: 数据长度
 * @retval None
 */
static void TJC_ParseTouchEvent(uint8_t* data, uint16_t length)
{
    if (length < 7) return; // 触摸事件至少7个字节
    
    TJC_Event_t event;
    event.type = (data[1] == 0x01) ? TJC_EVENT_TOUCH_PRESS : TJC_EVENT_TOUCH_RELEASE;
    event.page_id = data[2];
    event.component_id = data[3];
    event.value = 0;
    memset(event.text, 0, sizeof(event.text));
    
    /* 添加到事件队列 */
    TJC_AddEvent(&event);
}

/**
 * @brief  添加事件到队列
 * @param  event: 事件指针
 * @retval None
 */
static void TJC_AddEvent(TJC_Event_t* event)
{
    if (event_queue_count >= TJC_EVENT_QUEUE_SIZE) {
        return; // 队列满
    }
    
    event_queue[event_queue_tail] = *event;
    event_queue_tail = (event_queue_tail + 1) % TJC_EVENT_QUEUE_SIZE;
    event_queue_count++;
    
    /* 调用回调函数 */
    if (event_callback != NULL) {
        event_callback(event);
    }
}

/**
 * @brief  获取系统时间戳
 * @param  None
 * @retval 时间戳 (ms)
 */
static uint32_t TJC_GetTick(void)
{
    return GetSysTickCount();
}

/**
 * @brief  注册事件回调函数
 * @param  callback: 回调函数指针
 * @retval None
 */
void TJC_HMI_RegisterCallback(TJC_EventCallback_t callback)
{
    event_callback = callback;
}

/**
 * @brief  获取是否有新事件
 * @param  event: 事件输出
 * @retval true-有事件, false-无事件
 */
bool TJC_HMI_HasEvent(TJC_Event_t* event)
{
    if (event_queue_count == 0) {
        return false;
    }
    
    if (event != NULL) {
        *event = event_queue[event_queue_head];
    }
    
    event_queue_head = (event_queue_head + 1) % TJC_EVENT_QUEUE_SIZE;
    event_queue_count--;
    
    return true;
}

/**
 * @brief  清空事件队列
 * @param  None
 * @retval None
 */
void TJC_HMI_ClearEvents(void)
{
    event_queue_head = 0;
    event_queue_tail = 0;
    event_queue_count = 0;
    memset(event_queue, 0, sizeof(event_queue));
}

/**
 * @brief  串口中断处理函数
 * @param  None
 * @retval None
 */
void TJC_HMI_UART_IRQHandler(void)
{
    if (USART_GetITStatus(TJC_USART, USART_IT_RXNE) != RESET) {
        uint8_t received_byte = USART_ReceiveData(TJC_USART);
        
        if (rx_index < TJC_RX_BUFFER_SIZE - 1) {
            rx_buffer[rx_index++] = received_byte;
            
            /* 检查是否收到完整命令 (以0xFF 0xFF 0xFF结尾) */
            if (rx_index >= 3 && 
                rx_buffer[rx_index-3] == 0xFF && 
                rx_buffer[rx_index-2] == 0xFF && 
                rx_buffer[rx_index-1] == 0xFF) {
                rx_complete = true;
            }
        } else {
            /* 缓冲区溢出，重新开始 */
            rx_index = 0;
        }
    }
}

/* printf重定向到陶晶池串口屏 */
#if !defined(__MICROLIB)
#if (__ARMCLIB_VERSION <= 6000000)
struct __FILE {
    int handle;
};
#endif

FILE __stdout;

void _sys_exit(int x) {
    x = x;
}
#endif

int fputc(int ch, FILE *f)
{
    USART_SendData(TJC_USART, (uint8_t)ch);
    while (RESET == USART_GetFlagStatus(TJC_USART, USART_FLAG_TXE)) {}
    return ch;
}

/* ==================== AD9910专用显示函数 ==================== */

/**
 * @brief  显示欢迎界面
 * @param  None
 * @retval None
 */
void TJC_HMI_ShowWelcome(void)
{
    /* 切换到主页面 */
    TJC_HMI_ChangePage(TJC_PAGE_MAIN);
    Delay_ms(100);

    /* 显示欢迎信息 */
    TJC_HMI_SetText("title", "AD9910 DDS Generator");
    TJC_HMI_SetText("version", "V3.0 - TJC Edition");
    TJC_HMI_SetStatus("System Initializing...");

    /* 短暂延时显示欢迎信息 */
    Delay_ms(1000);
    TJC_HMI_SetStatus("System Ready");
}

/**
 * @brief  设置频率显示
 * @param  frequency_hz: 频率值 (Hz)
 * @retval None
 */
void TJC_HMI_SetFrequency(uint32_t frequency_hz)
{
    char freq_text[32];
    char unit_text[16];

    /* 根据频率大小选择合适的单位和显示格式 */
    if (frequency_hz >= 1000000) {
        /* MHz显示 */
        double freq_mhz = (double)frequency_hz / 1000000.0;
        snprintf(freq_text, sizeof(freq_text), "%.3f", freq_mhz);
        strcpy(unit_text, "MHz");
    } else if (frequency_hz >= 1000) {
        /* kHz显示 */
        double freq_khz = (double)frequency_hz / 1000.0;
        snprintf(freq_text, sizeof(freq_text), "%.1f", freq_khz);
        strcpy(unit_text, "kHz");
    } else {
        /* Hz显示 */
        snprintf(freq_text, sizeof(freq_text), "%lu", frequency_hz);
        strcpy(unit_text, "Hz");
    }

    /* 更新显示 */
    TJC_HMI_SetText("freq", freq_text);
    TJC_HMI_SetText("freq_unit", unit_text);

    /* 更新滑块位置 (对数刻度，1Hz-1MHz映射到0-100) */
    if (frequency_hz > 0) {
        double log_freq = log10((double)frequency_hz);
        double log_min = log10(1.0);      // 1Hz
        double log_max = log10(1000000.0); // 1MHz
        uint8_t slider_pos = (uint8_t)((log_freq - log_min) / (log_max - log_min) * 100);
        TJC_HMI_SetProgress("freq_slider", slider_pos);
    }
}

/**
 * @brief  设置幅度显示
 * @param  amplitude_mv: 幅度值 (mV)
 * @retval None
 */
void TJC_HMI_SetAmplitude(uint16_t amplitude_mv)
{
    char amp_text[32];
    char unit_text[16];

    /* 根据幅度大小选择合适的单位和显示格式 */
    if (amplitude_mv >= 1000) {
        /* V显示 */
        double amp_v = (double)amplitude_mv / 1000.0;
        snprintf(amp_text, sizeof(amp_text), "%.2f", amp_v);
        strcpy(unit_text, "V");
    } else {
        /* mV显示 */
        snprintf(amp_text, sizeof(amp_text), "%d", amplitude_mv);
        strcpy(unit_text, "mV");
    }

    /* 更新显示 */
    TJC_HMI_SetText("amplitude", amp_text);
    TJC_HMI_SetText("amp_unit", unit_text);

    /* 更新滑块位置 (0-800mV映射到0-100) */
    uint8_t slider_pos = (uint8_t)((amplitude_mv * 100) / 800);
    if (slider_pos > 100) slider_pos = 100;
    TJC_HMI_SetProgress("amp_slider", slider_pos);
}

/**
 * @brief  设置增益显示
 * @param  gain: 增益值
 * @retval None
 */
void TJC_HMI_SetGain(double gain)
{
    char gain_text[32];
    snprintf(gain_text, sizeof(gain_text), "%.2f", gain);
    TJC_HMI_SetText("gain", gain_text);
}

/**
 * @brief  设置输出状态显示
 * @param  enabled: 输出状态
 * @retval None
 */
void TJC_HMI_SetOutputStatus(bool enabled)
{
    /* 更新状态文本 */
    TJC_HMI_SetText("output_status", enabled ? "ON" : "OFF");

    /* 更新LED指示 */
    TJC_HMI_SetValue("output_led", enabled ? 1 : 0);

    /* 更新按钮颜色 */
    uint16_t color = enabled ? 0x07E0 : 0xF800; // 绿色:红色
    TJC_HMI_SetColor("btn_output", color);
}

/**
 * @brief  设置系统状态显示
 * @param  status: 状态文本
 * @retval None
 */
void TJC_HMI_SetStatus(const char* status)
{
    TJC_HMI_SetText("status", status);

    /* 清空错误信息 */
    TJC_HMI_SetText("error", "");
}

/**
 * @brief  设置错误信息显示
 * @param  error: 错误文本
 * @retval None
 */
void TJC_HMI_SetError(const char* error)
{
    TJC_HMI_SetText("error", error);
    TJC_HMI_SetText("status", "Error");

    /* 设置错误文本为红色 */
    TJC_HMI_SetColor("error", 0xF800); // 红色
}

/**
 * @brief  更新所有AD9910参数显示
 * @param  None
 * @retval None
 */
void TJC_HMI_UpdateAllParameters(void)
{
    SystemParams_t params;

    if (AD9910_Control_GetParams(&params) == CTRL_STATUS_OK) {
        TJC_HMI_SetFrequency(params.frequency_hz);
        TJC_HMI_SetAmplitude(params.target_amplitude_mv);
        TJC_HMI_SetGain(params.gain_factor);
        TJC_HMI_SetOutputStatus(params.output_enabled);
        TJC_HMI_SetStatus("Parameters Updated");
    } else {
        TJC_HMI_SetError("Failed to Get Parameters");
    }
}

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
