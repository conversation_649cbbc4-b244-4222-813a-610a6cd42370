/**
  ******************************************************************************
  * @file    tjc_hmi.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024-08-02
  * @brief   陶晶池串口屏驱动实现文件
  ******************************************************************************
  * @attention
  *
  * 本文件实现陶晶池串口屏的通信功能
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "tjc_hmi.h"
#include "../Control/ad9910_control.h"
#include "../Control/gain_calculator.h"
#include "../Core/systick.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <math.h>

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
#define TJC_EVENT_QUEUE_SIZE        8       ///< 事件队列大小
#define TJC_CMD_TIMEOUT             100     ///< 命令超时时间 (ms)

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/* 串口状态变量 */
static bool tjc_initialized = false;
static TJC_EventCallback_t event_callback = NULL;
static uint8_t current_page = 0;

/* 接收缓冲区和状态 */
static uint8_t rx_buffer[TJC_RX_BUFFER_SIZE];
static uint16_t rx_index = 0;
static bool rx_complete = false;

/* 事件队列 */
static TJC_Event_t event_queue[TJC_EVENT_QUEUE_SIZE];
static uint8_t event_queue_head = 0;
static uint8_t event_queue_tail = 0;
static uint8_t event_queue_count = 0;

/* 当前参数状态 */
static uint32_t current_frequency_hz = 0;
static uint16_t current_voltage_mv = 0;
static uint32_t input_number = 0;
static char input_unit[16] = "Hz";
static bool measurement_active = false;

/* 避免编译器警告的宏 */
#define UNUSED(x) ((void)(x))

/* Private function prototypes -----------------------------------------------*/
static void TJC_UART_Init(void);
static void TJC_SendByte(uint8_t byte);
static void TJC_SendString(const char* str);
static void TJC_ProcessReceivedData(void);
static void TJC_ParseTouchEvent(uint8_t* data, uint16_t length);
static void TJC_ParseNumberInput(uint8_t* data, uint16_t length);
static void TJC_ParseTextInput(uint8_t* data, uint16_t length);
static void TJC_AddEvent(TJC_Event_t* event);
static uint32_t TJC_GetTick(void);
static void TJC_ParseUnitString(const char* unit_str, char* normalized_unit);
static uint32_t TJC_ConvertToHz(uint32_t value, const char* unit);

/* Private functions ---------------------------------------------------------*/

/**
 * @brief  陶晶池串口屏初始化
 * @param  None
 * @retval None
 */
void TJC_HMI_Init(void)
{
    /* 串口初始化 */
    TJC_UART_Init();
    
    /* 初始化状态变量 */
    tjc_initialized = true;
    current_page = 0;
    rx_index = 0;
    rx_complete = false;
    measurement_active = false;

    /* 清空事件队列 */
    event_queue_head = 0;
    event_queue_tail = 0;
    event_queue_count = 0;
    memset(event_queue, 0, sizeof(event_queue));

    /* 初始化参数 */
    current_frequency_hz = 0;
    current_voltage_mv = 0;
    input_number = 0;
    strcpy(input_unit, "Hz");

    /* 短暂延时，等待串口屏启动 */
    Delay_ms(500);

    /* 发送初始化命令 */
    TJC_HMI_SendCommand("bkcmd=3");  // 设置返回数据格式
    Delay_ms(10);
}

/**
 * @brief  串口初始化
 * @param  None
 * @retval None
 */
static void TJC_UART_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    USART_InitTypeDef USART_InitStructure;
    NVIC_InitTypeDef NVIC_InitStructure;

    /* 使能时钟 */
    RCC_AHB1PeriphClockCmd(TJC_USART_TX_RCC, ENABLE);
    RCC_APB1PeriphClockCmd(TJC_USART_RCC, ENABLE);  // USART2在APB1总线上

    /* 配置GPIO复用功能 */
    GPIO_PinAFConfig(TJC_USART_TX_PORT, TJC_USART_TX_AF_PIN, TJC_USART_AF);
    GPIO_PinAFConfig(TJC_USART_RX_PORT, TJC_USART_RX_AF_PIN, TJC_USART_AF);

    /* 配置TX引脚 */
    GPIO_StructInit(&GPIO_InitStructure);
    GPIO_InitStructure.GPIO_Pin = TJC_USART_TX_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(TJC_USART_TX_PORT, &GPIO_InitStructure);

    /* 配置RX引脚 */
    GPIO_InitStructure.GPIO_Pin = TJC_USART_RX_PIN;
    GPIO_Init(TJC_USART_RX_PORT, &GPIO_InitStructure);

    /* 配置USART */
    USART_DeInit(TJC_USART);
    USART_StructInit(&USART_InitStructure);
    USART_InitStructure.USART_BaudRate = TJC_BAUD_RATE;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_Init(TJC_USART, &USART_InitStructure);

    /* 清除标志位 */
    USART_ClearFlag(TJC_USART, USART_FLAG_RXNE);

    /* 使能串口 */
    USART_Cmd(TJC_USART, ENABLE);

    /* 使能接收中断 */
    USART_ITConfig(TJC_USART, USART_IT_RXNE, ENABLE);

    /* 配置中断 */
    NVIC_InitStructure.NVIC_IRQChannel = USART2_IRQn;  // 使用USART2中断
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);
}

/**
 * @brief  发送单个字节
 * @param  byte: 要发送的字节
 * @retval None
 */
static void TJC_SendByte(uint8_t byte)
{
    USART_SendData(TJC_USART, byte);
    while (RESET == USART_GetFlagStatus(TJC_USART, USART_FLAG_TXE)) {}
}

/**
 * @brief  发送字符串
 * @param  str: 要发送的字符串
 * @retval None
 */
static void TJC_SendString(const char* str)
{
    while (*str) {
        TJC_SendByte(*str++);
    }
}

/**
 * @brief  发送命令到陶晶池串口屏
 * @param  cmd: 命令字符串
 * @retval None
 */
void TJC_HMI_SendCommand(const char* cmd)
{
    if (!tjc_initialized || cmd == NULL) return;
    
    /* 发送命令内容 */
    TJC_SendString(cmd);
    
    /* 发送结束符 */
    TJC_SendByte(0xFF);
    TJC_SendByte(0xFF);
    TJC_SendByte(0xFF);
}

/**
 * @brief  设置文本控件内容
 * @param  obj_name: 控件名称
 * @param  text: 文本内容
 * @retval None
 */
void TJC_HMI_SetText(const char* obj_name, const char* text)
{
    char cmd[128];
    snprintf(cmd, sizeof(cmd), "%s.txt=\"%s\"", obj_name, text);
    TJC_HMI_SendCommand(cmd);
}

/**
 * @brief  设置数值控件内容
 * @param  obj_name: 控件名称
 * @param  value: 数值
 * @retval None
 */
void TJC_HMI_SetValue(const char* obj_name, int32_t value)
{
    char cmd[64];
    snprintf(cmd, sizeof(cmd), "%s.val=%ld", obj_name, value);
    TJC_HMI_SendCommand(cmd);
}

/**
 * @brief  设置进度条/滑块值
 * @param  obj_name: 控件名称
 * @param  value: 进度值 (0-100)
 * @retval None
 */
void TJC_HMI_SetProgress(const char* obj_name, uint8_t value)
{
    if (value > 100) value = 100;
    TJC_HMI_SetValue(obj_name, value);
}

/**
 * @brief  切换页面
 * @param  page_id: 页面ID
 * @retval None
 */
void TJC_HMI_ChangePage(uint8_t page_id)
{
    char cmd[32];
    snprintf(cmd, sizeof(cmd), "page %d", page_id);
    TJC_HMI_SendCommand(cmd);
}

/**
 * @brief  设置控件可见性
 * @param  obj_name: 控件名称
 * @param  visible: true-可见, false-隐藏
 * @retval None
 */
void TJC_HMI_SetVisible(const char* obj_name, bool visible)
{
    char cmd[64];
    snprintf(cmd, sizeof(cmd), "vis %s,%d", obj_name, visible ? 1 : 0);
    TJC_HMI_SendCommand(cmd);
}

/**
 * @brief  设置控件颜色
 * @param  obj_name: 控件名称
 * @param  color: 颜色值 (RGB565)
 * @retval None
 */
void TJC_HMI_SetColor(const char* obj_name, uint16_t color)
{
    char cmd[64];
    snprintf(cmd, sizeof(cmd), "%s.bco=%d", obj_name, color);
    TJC_HMI_SendCommand(cmd);
}

/**
 * @brief  陶晶池串口屏处理 (主循环调用)
 * @param  None
 * @retval None
 */
void TJC_HMI_Process(void)
{
    if (!tjc_initialized) return;
    
    /* 处理接收到的数据 */
    if (rx_complete) {
        TJC_ProcessReceivedData();
        rx_complete = false;
        rx_index = 0;
    }
}

/**
 * @brief  处理接收到的数据
 * @param  None
 * @retval None
 */
static void TJC_ProcessReceivedData(void)
{
    if (rx_index < 3) return; // 至少需要3个字节的结束符

    /* 检查数据类型并解析 */
    if (rx_buffer[0] == 0x65) {
        /* 触摸事件 */
        TJC_ParseTouchEvent(rx_buffer, rx_index);
    } else if (rx_buffer[0] == 0x71) {
        /* 数字键盘输入事件 */
        TJC_ParseNumberInput(rx_buffer, rx_index);
    } else if (rx_buffer[0] == 0x70) {
        /* 文本输入事件 */
        TJC_ParseTextInput(rx_buffer, rx_index);
    } else if (rx_index >= 4 && rx_buffer[0] == 0x1A) {
        /* 特殊事件 (printh 31) */
        if (rx_buffer[1] == 31) {
            TJC_HMI_StartMeasurement();
        }
    }
}

/**
 * @brief  解析触摸事件
 * @param  data: 数据缓冲区
 * @param  length: 数据长度
 * @retval None
 */
static void TJC_ParseTouchEvent(uint8_t* data, uint16_t length)
{
    if (length < 7) return; // 触摸事件至少7个字节

    TJC_Event_t event;
    event.type = (data[1] == 0x01) ? TJC_EVENT_TOUCH_PRESS : TJC_EVENT_TOUCH_RELEASE;
    event.page_id = data[2];
    event.component_id = data[3];
    event.value = 0;
    memset(event.text, 0, sizeof(event.text));

    /* 处理特定控件的触摸事件 */
    if (event.type == TJC_EVENT_TOUCH_PRESS) {
        if (event.component_id == TJC_ID_B0) {
            /* b0按钮按下 */
            TJC_HMI_HandleMeasureButton(event.page_id);
        }
    }

    /* 添加到事件队列 */
    TJC_AddEvent(&event);
}

/**
 * @brief  解析数字键盘输入事件
 * @param  data: 数据缓冲区
 * @param  length: 数据长度
 * @retval None
 */
static void TJC_ParseNumberInput(uint8_t* data, uint16_t length)
{
    if (length < 8) return; // 数字输入事件至少8个字节

    /* 提取数值 (小端格式) */
    uint32_t value = (uint32_t)data[1] |
                    ((uint32_t)data[2] << 8) |
                    ((uint32_t)data[3] << 16) |
                    ((uint32_t)data[4] << 24);

    /* 处理数字输入 */
    TJC_HMI_HandleNumberInput(value);
}

/**
 * @brief  解析文本输入事件
 * @param  data: 数据缓冲区
 * @param  length: 数据长度
 * @retval None
 */
static void TJC_ParseTextInput(uint8_t* data, uint16_t length)
{
    if (length < 4) return; // 至少需要头部和结束符

    /* 提取文本内容 (去除头部和结束符) */
    char text_buffer[32];
    uint16_t text_length = length - 4; // 去除头部1字节和结束符3字节
    if (text_length > sizeof(text_buffer) - 1) {
        text_length = sizeof(text_buffer) - 1;
    }

    memcpy(text_buffer, &data[1], text_length);
    text_buffer[text_length] = '\0';

    /* 处理文本输入 */
    TJC_HMI_HandleUnitInput(text_buffer);
}

/**
 * @brief  添加事件到队列
 * @param  event: 事件指针
 * @retval None
 */
static void TJC_AddEvent(TJC_Event_t* event)
{
    if (event_queue_count >= TJC_EVENT_QUEUE_SIZE) {
        return; // 队列满
    }
    
    event_queue[event_queue_tail] = *event;
    event_queue_tail = (event_queue_tail + 1) % TJC_EVENT_QUEUE_SIZE;
    event_queue_count++;
    
    /* 调用回调函数 */
    if (event_callback != NULL) {
        event_callback(event);
    }
}

/**
 * @brief  解析单位字符串 (处理大小写不敏感)
 * @param  unit_str: 输入的单位字符串
 * @param  normalized_unit: 标准化后的单位字符串
 * @retval None
 */
static void TJC_ParseUnitString(const char* unit_str, char* normalized_unit)
{
    char temp[16];
    int i;

    /* 转换为小写 */
    for (i = 0; i < strlen(unit_str) && i < 15; i++) {
        temp[i] = (unit_str[i] >= 'A' && unit_str[i] <= 'Z') ?
                  (unit_str[i] + 32) : unit_str[i];
    }
    temp[i] = '\0';

    /* 识别频率单位 */
    if (strstr(temp, "hz") != NULL) {
        if (strstr(temp, "mhz") != NULL || strstr(temp, "m") != NULL) {
            strcpy(normalized_unit, "MHz");
        } else if (strstr(temp, "khz") != NULL || strstr(temp, "k") != NULL) {
            strcpy(normalized_unit, "kHz");
        } else {
            strcpy(normalized_unit, "Hz");
        }
    }
    /* 识别电压单位 */
    else if (strstr(temp, "v") != NULL) {
        if (strstr(temp, "mv") != NULL || strstr(temp, "m") != NULL) {
            strcpy(normalized_unit, "mV");
        } else {
            strcpy(normalized_unit, "V");
        }
    }
    /* 默认为Hz */
    else {
        strcpy(normalized_unit, "Hz");
    }
}

/**
 * @brief  将数值和单位转换为Hz
 * @param  value: 数值
 * @param  unit: 单位
 * @retval 转换后的Hz值
 */
static uint32_t TJC_ConvertToHz(uint32_t value, const char* unit)
{
    if (strcmp(unit, "MHz") == 0) {
        return value * 1000000;
    } else if (strcmp(unit, "kHz") == 0) {
        return value * 1000;
    } else {
        return value; // Hz
    }
}

/**
 * @brief  获取系统时间戳
 * @param  None
 * @retval 时间戳 (ms)
 */
static uint32_t TJC_GetTick(void)
{
    return SysTick_GetTick(); // 使用正确的函数名
}

/**
 * @brief  获取系统运行时间 (对外接口)
 * @param  None
 * @retval 时间戳 (ms)
 */
uint32_t TJC_HMI_GetSystemTime(void)
{
    return TJC_GetTick(); // 使用内部函数，避免未使用警告
}

/**
 * @brief  设置AD9910输出幅度 (通过串口屏控制)
 * @param  amplitude_mv: 目标幅度 (mV)
 * @retval None
 */
void TJC_HMI_SetAD9910Amplitude(uint16_t amplitude_mv)
{
    ControlStatus_t status = AD9910_Control_SetTargetAmplitude(amplitude_mv);
    if (status == CTRL_STATUS_OK) {
        /* 更新显示 */
        TJC_HMI_SetCircuitVoltage(amplitude_mv);

        /* 更新频率显示 (可能因增益变化而改变) */
        SystemParams_t params;
        if (AD9910_Control_GetParams(&params) == CTRL_STATUS_OK) {
            TJC_HMI_SetOutputFrequency(params.frequency_hz);
        }
    }
}

/**
 * @brief  注册事件回调函数
 * @param  callback: 回调函数指针
 * @retval None
 */
void TJC_HMI_RegisterCallback(TJC_EventCallback_t callback)
{
    event_callback = callback;
}

/**
 * @brief  获取是否有新事件
 * @param  event: 事件输出
 * @retval true-有事件, false-无事件
 */
bool TJC_HMI_HasEvent(TJC_Event_t* event)
{
    if (event_queue_count == 0) {
        return false;
    }
    
    if (event != NULL) {
        *event = event_queue[event_queue_head];
    }
    
    event_queue_head = (event_queue_head + 1) % TJC_EVENT_QUEUE_SIZE;
    event_queue_count--;
    
    return true;
}

/**
 * @brief  清空事件队列
 * @param  None
 * @retval None
 */
void TJC_HMI_ClearEvents(void)
{
    event_queue_head = 0;
    event_queue_tail = 0;
    event_queue_count = 0;
    memset(event_queue, 0, sizeof(event_queue));
}

/**
 * @brief  串口中断处理函数
 * @param  None
 * @retval None
 */
void TJC_HMI_UART_IRQHandler(void)
{
    if (USART_GetITStatus(TJC_USART, USART_IT_RXNE) != RESET) {
        uint8_t received_byte = USART_ReceiveData(TJC_USART);
        
        if (rx_index < TJC_RX_BUFFER_SIZE - 1) {
            rx_buffer[rx_index++] = received_byte;
            
            /* 检查是否收到完整命令 (以0xFF 0xFF 0xFF结尾) */
            if (rx_index >= 3 && 
                rx_buffer[rx_index-3] == 0xFF && 
                rx_buffer[rx_index-2] == 0xFF && 
                rx_buffer[rx_index-1] == 0xFF) {
                rx_complete = true;
            }
        } else {
            /* 缓冲区溢出，重新开始 */
            rx_index = 0;
        }
    }
}

/* printf重定向到陶晶池串口屏 */
#if !defined(__MICROLIB)
#if (__ARMCLIB_VERSION <= 6000000)
struct __FILE {
    int handle;
};
#endif

FILE __stdout;

void _sys_exit(int x) {
    x = x;
}
#endif

int fputc(int ch, FILE *f)
{
    USART_SendData(TJC_USART, (uint8_t)ch);
    while (RESET == USART_GetFlagStatus(TJC_USART, USART_FLAG_TXE)) {}
    return ch;
}

/* ==================== AD9910专用显示函数 ==================== */

/**
 * @brief  显示欢迎界面并初始化page0
 * @param  None
 * @retval None
 */
void TJC_HMI_ShowWelcome(void)
{
    /* 切换到主页面 */
    TJC_HMI_ChangePage(TJC_PAGE_MAIN);
    current_page = TJC_PAGE_MAIN;
    UNUSED(current_page); // 避免警告，保留变量供将来使用
    Delay_ms(100);

    /* 初始化显示 */
    TJC_HMI_SetOutputFrequency(0);
    TJC_HMI_SetCircuitVoltage(0);

    /* 设置初始单位显示 */
    TJC_HMI_SetText(TJC_CTRL_T0, "Hz");

    Delay_ms(500);
}

/**
 * @brief  设置输出频率显示 (t2控件，带单位)
 * @param  frequency_hz: 频率值 (Hz)
 * @retval None
 */
void TJC_HMI_SetOutputFrequency(uint32_t frequency_hz)
{
    char freq_display[32];

    current_frequency_hz = frequency_hz;
    UNUSED(current_frequency_hz); // 避免警告，保留变量供将来使用

    /* 根据频率大小选择合适的单位和显示格式 */
    if (frequency_hz >= 1000000) {
        /* MHz显示 */
        double freq_mhz = (double)frequency_hz / 1000000.0;
        snprintf(freq_display, sizeof(freq_display), "%.3f MHz", freq_mhz);
    } else if (frequency_hz >= 1000) {
        /* kHz显示 */
        double freq_khz = (double)frequency_hz / 1000.0;
        snprintf(freq_display, sizeof(freq_display), "%.1f kHz", freq_khz);
    } else {
        /* Hz显示 */
        snprintf(freq_display, sizeof(freq_display), "%lu Hz", frequency_hz);
    }

    /* 更新t2控件显示 */
    TJC_HMI_SetText(TJC_CTRL_T2, freq_display);
}

/**
 * @brief  设置电路电压输出峰峰值 (t4控件，考虑增益)
 * @param  voltage_mv: 电压峰峰值 (mV)
 * @retval None
 */
void TJC_HMI_SetCircuitVoltage(uint16_t voltage_mv)
{
    char voltage_display[32];

    current_voltage_mv = voltage_mv;
    UNUSED(current_voltage_mv); // 避免警告，保留变量供将来使用

    /* 根据电压大小选择合适的单位和显示格式 */
    if (voltage_mv >= 1000) {
        /* V显示 */
        double voltage_v = (double)voltage_mv / 1000.0;
        snprintf(voltage_display, sizeof(voltage_display), "%.2f V", voltage_v);
    } else {
        /* mV显示 */
        snprintf(voltage_display, sizeof(voltage_display), "%d mV", voltage_mv);
    }

    /* 更新t4控件显示 */
    TJC_HMI_SetText(TJC_CTRL_T4, voltage_display);
}

/**
 * @brief  设置滤波类型显示 (t6控件)
 * @param  filter_type: 滤波类型字符串
 * @retval None
 */
void TJC_HMI_SetFilterType(const char* filter_type)
{
    TJC_HMI_SetText(TJC_CTRL_T6, filter_type);
}

/**
 * @brief  处理数字键盘输入 (n0控件)
 * @param  value: 输入的数值
 * @retval None
 */
void TJC_HMI_HandleNumberInput(uint32_t value)
{
    input_number = value;

    /* 根据当前单位计算实际频率 */
    uint32_t frequency_hz = TJC_ConvertToHz(value, input_unit);

    /* 设置AD9910频率 */
    if (frequency_hz >= 1 && frequency_hz <= 420000000) { // AD9910频率范围
        ControlStatus_t status = AD9910_Control_SetFrequency(frequency_hz);
        if (status == CTRL_STATUS_OK) {
            /* 更新显示 */
            TJC_HMI_SetOutputFrequency(frequency_hz);

            /* 重新计算并更新电路电压 */
            SystemParams_t params;
            if (AD9910_Control_GetParams(&params) == CTRL_STATUS_OK) {
                TJC_HMI_SetCircuitVoltage(params.target_amplitude_mv);
            }
        }
    }
}

/**
 * @brief  处理全键盘输入 (t0控件，单位设置)
 * @param  unit_text: 输入的单位文本
 * @retval None
 */
void TJC_HMI_HandleUnitInput(const char* unit_text)
{
    char normalized_unit[16];

    /* 解析并标准化单位 */
    TJC_ParseUnitString(unit_text, normalized_unit);
    strcpy(input_unit, normalized_unit);

    /* 更新t0控件显示标准化后的单位 */
    TJC_HMI_SetText(TJC_CTRL_T0, normalized_unit);

    /* 如果已有输入数值，重新计算频率 */
    if (input_number > 0) {
        TJC_HMI_HandleNumberInput(input_number);
    }
}

/**
 * @brief  处理测量按钮事件 (b0控件)
 * @param  current_page: 当前页面
 * @retval None
 */
void TJC_HMI_HandleMeasureButton(uint8_t page_id)
{
    if (page_id == TJC_PAGE_MAIN) {
        /* 在page0，切换到page1并开始测量 */
        TJC_HMI_ChangePage(TJC_PAGE_MEASURE);
        current_page = TJC_PAGE_MEASURE;
        Delay_ms(100);

        /* 初始化page1显示 */
        TJC_HMI_SetFilterType("等待");

        /* 发送测量开始信号 */
        TJC_HMI_SendCommand("printh 31");

    } else if (page_id == TJC_PAGE_MEASURE) {
        /* 在page1，切换回page0 */
        TJC_HMI_ChangePage(TJC_PAGE_MAIN);
        current_page = TJC_PAGE_MAIN;
    }
}

/**
 * @brief  开始测量未知电路
 * @param  None
 * @retval None
 */
void TJC_HMI_StartMeasurement(void)
{
    measurement_active = true;
    UNUSED(measurement_active); // 避免警告，保留变量供将来使用

    /* 设置滤波类型为等待状态 */
    TJC_HMI_SetFilterType("Measuring...");

    /* 这里可以添加实际的测量逻辑 */
    /* 例如：启动频率扫描、幅度测量等 */

    /* 模拟测量完成后的结果显示 */
    // TJC_HMI_SetFilterType("Low Pass Filter");
}

/**
 * @brief  更新所有显示参数
 * @param  None
 * @retval None
 */
void TJC_HMI_UpdateAllParameters(void)
{
    SystemParams_t params;

    if (AD9910_Control_GetParams(&params) == CTRL_STATUS_OK) {
        /* 更新频率显示 */
        TJC_HMI_SetOutputFrequency(params.frequency_hz);

        /* 计算并更新电路电压输出 (考虑增益和传递函数) */
        GainResult_t gain_result;
        GainConfig_t gain_config = {
            .frequency_hz = params.frequency_hz,
            .input_amplitude_mv = params.target_amplitude_mv,
            .temperature_c = 25.0,
            .enable_nonlinear_correction = true
        };

        if (GainCalculator_Calculate(&gain_config, &gain_result)) {
            uint16_t circuit_voltage = (uint16_t)(params.target_amplitude_mv * gain_result.calculated_gain);
            TJC_HMI_SetCircuitVoltage(circuit_voltage);
        } else {
            TJC_HMI_SetCircuitVoltage(0);
        }
    }
}

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
