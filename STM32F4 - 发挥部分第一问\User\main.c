/**
  ******************************************************************************
  * @file    main.c
  * <AUTHOR>
  * @version V3.0
  * @date    2024-08-02
  * @brief   STM32F4控制AD9910 DDS信号发生器 - 外部控制版本
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include <stdio.h>
#include <stdint.h>

// 核心基础模块
#include "../Modules/Core/systick.h"
#include "bsp.h"

// AD9910波形产生模块
#include "../Modules/Generation/ad9910_waveform.h"

// 外部控制模块
#include "../Modules/Control/ad9910_control.h"
#include "../Modules/Control/command_parser.h"
#include "../Modules/Control/gain_calculator.h"

// 陶晶池串口屏通信模块
#include "../Modules/Communication/tjc_hmi.h"

/* Global variables ----------------------------------------------------------*/
__IO uint32_t uwTick = 0;  ///< 系统滴答计数器

// AD9910外部控制系统技术规格：
// - 支持串口屏和4x4矩阵键盘控制
// - 实时频率调节：1Hz - 420MHz
// - 实时峰峰值调节：考虑后续电路增益
// - 增益计算算法：自动补偿频率、温度、非线性
// - 预设参数管理：8组预设配置
// - 高精度控制：频率±0.001%，幅度±0.01%

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void ControlCallback(ControlCommand_t cmd, ControlStatus_t status, void* data);

/**
  * @brief  Main program
  * @param  None
  * @retval None
  */
int main(void)
{
    /* 系统初始化 */
    SystemClock_Config();
    SysTick_Init();
    BSP_Init();

    /* 陶晶池串口屏初始化 */
    TJC_HMI_Init();

    /* 外部控制系统初始化 */
    CommandParser_Init();
    GainCalculator_Init();
    AD9910_Control_Init();

    /* 注册控制回调函数 */
    AD9910_Control_RegisterCallback(ControlCallback);

    /* 设置默认参数 (5MHz/0.5V，增益1.0) */
    AD9910_Control_SetFrequency(5000000);      // 5MHz
    AD9910_Control_SetTargetAmplitude(500);    // 0.5V峰峰值
    AD9910_Control_SetGainFactor(1.0);         // 1倍增益
    AD9910_Control_EnableOutput(true);         // 使能输出

    /* 显示欢迎界面并更新参数 */
    TJC_HMI_ShowWelcome();
    TJC_HMI_UpdateAllParameters();

    /* ==================== 外部控制系统就绪 ==================== */
    // 系统已准备好接受外部控制命令
    // 支持串口屏和4x4矩阵键盘控制
    // 支持实时频率、峰峰值、增益调节

    while (1) {
        /* 控制系统任务处理 */
        AD9910_Control_Task();

        /* 陶晶池串口屏处理 */
        TJC_HMI_Process();

        /* 短暂延时，避免CPU占用过高 */
        Delay_ms(1);
    }
}

/**
  * @brief  控制命令回调函数
  * @param  cmd: 执行的命令
  * @param  status: 执行状态
  * @param  data: 相关数据
  * @retval None
  */
void ControlCallback(ControlCommand_t cmd, ControlStatus_t status, void* data)
{
    /* 命令执行后的处理逻辑 - 更新陶晶池串口屏显示 */

    if (status == CTRL_STATUS_OK) {
        /* 命令执行成功 - 更新串口屏显示 */
        switch (cmd) {
            case CMD_SET_FREQUENCY:
                {
                    SystemParams_t* params = (SystemParams_t*)data;
                    TJC_HMI_SetOutputFrequency(params->frequency_hz);
                    // TJC_HMI_SetStatus("Frequency Set"); // 暂时注释，函数不存在
                }
                break;

            case CMD_SET_AMPLITUDE:
                {
                    SystemParams_t* params = (SystemParams_t*)data;
                    TJC_HMI_SetCircuitVoltage(params->target_amplitude_mv);
                    // TJC_HMI_SetStatus("Amplitude Set"); // 暂时注释，函数不存在
                }
                break;

            case CMD_SET_GAIN:
                {
                    // SystemParams_t* params = (SystemParams_t*)data; // 暂时注释，避免警告
                    // TJC_HMI_SetGain(params->gain_factor); // 暂时注释，函数不存在
                    // TJC_HMI_SetStatus("Gain Set"); // 暂时注释，函数不存在
                    (void)data; // 避免未使用参数警告
                }
                break;

            case CMD_ENABLE_OUTPUT:
                // TJC_HMI_SetOutputStatus(true); // 暂时注释，函数不存在
                // TJC_HMI_SetStatus("Output Enabled"); // 暂时注释，函数不存在
                break;

            case CMD_DISABLE_OUTPUT:
                // TJC_HMI_SetOutputStatus(false); // 暂时注释，函数不存在
                // TJC_HMI_SetStatus("Output Disabled"); // 暂时注释，函数不存在
                break;

            case CMD_SET_PRESET:
                TJC_HMI_UpdateAllParameters();
                // TJC_HMI_SetStatus("Preset Loaded"); // 暂时注释，函数不存在
                break;

            default:
                // TJC_HMI_SetStatus("Command OK"); // 暂时注释，函数不存在
                break;
        }
    } else {
        /* 命令执行失败 - 显示错误信息 */
        switch (status) {
            case CTRL_STATUS_OUT_OF_RANGE:
                // TJC_HMI_SetError("Parameter Out of Range"); // 暂时注释，函数不存在
                break;
            case CTRL_STATUS_INVALID_PARAM:
                // TJC_HMI_SetError("Invalid Parameter"); // 暂时注释，函数不存在
                break;
            case CTRL_STATUS_NOT_READY:
                // TJC_HMI_SetError("System Not Ready"); // 暂时注释，函数不存在
                break;
            default:
                // TJC_HMI_SetError("Command Failed"); // 暂时注释，函数不存在
                break;
        }
    }
}

/* Private functions ---------------------------------------------------------*/















/**
  * @brief  系统时钟配置
  * @param  None
  * @retval None
  */
void SystemClock_Config(void)
{
    /* 嘉立创天空星STM32F407VGT6时钟配置 */
    /* 外部晶振：25MHz，目标系统时钟：168MHz */

    RCC_DeInit();

    // 使能HSE
    RCC_HSEConfig(RCC_HSE_ON);
    if (RCC_WaitForHSEStartUp() != SUCCESS) {
        // HSE启动失败，使用HSI
        while(1) {}
    }

    // 配置PLL: HSE = 25MHz, VCO = 25MHz/25*336 = 336MHz, SYSCLK = 336MHz/2 = 168MHz
    RCC_PLLConfig(RCC_PLLSource_HSE, 25, 336, 2, 7);
    RCC_PLLCmd(ENABLE);

    // 等待PLL就绪
    while (RCC_GetFlagStatus(RCC_FLAG_PLLRDY) == RESET) {}

    // 配置Flash延迟
    FLASH_SetLatency(FLASH_Latency_5);

    // 配置总线分频
    RCC_HCLKConfig(RCC_SYSCLK_Div1);   // AHB = 168MHz
    RCC_PCLK1Config(RCC_HCLK_Div4);    // APB1 = 42MHz
    RCC_PCLK2Config(RCC_HCLK_Div2);    // APB2 = 84MHz

    // 切换到PLL
    RCC_SYSCLKConfig(RCC_SYSCLKSource_PLLCLK);
    while (RCC_GetSYSCLKSource() != 0x08) {}
}

/**
  * @brief  定时延时递减函数 (SysTick中断调用)
  * @param  None
  * @retval None
  */
void TimingDelay_Decrement(void)
{
    // 这个函数由SysTick中断调用，用于系统延时
    // 实际的延时逻辑已经在SysTick模块中实现
    // 这里保持空实现以满足链接需求
}

/**
  * @brief  断言失败处理函数
  * @param  file: 源文件名
  * @param  line: 行号
  * @retval None
  */
#ifdef USE_FULL_ASSERT
void assert_failed(uint8_t* file, uint32_t line)
{
    /* 用户可在此添加记录或打印功能，这里简单死循环 */
    while (1) {}
}
#endif

// DDS_TIM6_IRQHandler_Internal函数已在dds_wavegen.c中实现，这里删除重复定义

/**
  * @brief  EXTI0中断处理函数内部实现 (空实现)
  * @param  None
  * @retval None
  */
void EXTI0_IRQHandler_Internal(void)
{
    // 空实现，因为我们不使用外部中断
    // 第二问只需要DAC输出正弦波
}


