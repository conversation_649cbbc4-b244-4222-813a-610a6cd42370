/**
  ******************************************************************************
  * @file    main.c
  * <AUTHOR>
  * @version V3.0
  * @date    2024-08-02
  * @brief   STM32F4控制AD9910 DDS信号发生器 - 外部控制版本
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include <stdio.h>
#include <stdint.h>

// 核心基础模块
#include "../Modules/Core/systick.h"
#include "bsp.h"

// AD9910波形产生模块
#include "../Modules/Generation/ad9910_waveform.h"

// 外部控制模块
#include "../Modules/Control/ad9910_control.h"
#include "../Modules/Control/command_parser.h"
#include "../Modules/Control/gain_calculator.h"

// 串口屏通信模块 (暂时注释，先集成4x4矩阵键盘)
// #include "../Modules/Communication/uart_hmi.h"

// 4x4矩阵键盘模块
#include "../Modules/Input/keypad_4x4.h"

/* Global variables ----------------------------------------------------------*/
__IO uint32_t uwTick = 0;  ///< 系统滴答计数器

// AD9910外部控制系统技术规格：
// - 支持串口屏和4x4矩阵键盘控制
// - 实时频率调节：1Hz - 420MHz
// - 实时峰峰值调节：考虑后续电路增益
// - 增益计算算法：自动补偿频率、温度、非线性
// - 预设参数管理：8组预设配置
// - 高精度控制：频率±0.001%，幅度±0.01%

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void ControlCallback(ControlCommand_t cmd, ControlStatus_t status, void* data);

/**
  * @brief  Main program
  * @param  None
  * @retval None
  */
int main(void)
{
    /* 系统初始化 */
    SystemClock_Config();
    SysTick_Init();
    BSP_Init();

    /* 串口屏初始化 (暂时注释) */
    // HMI_UART_Init(HMI_BAUD_RATE);
    // HMI_ShowWelcomeScreen();

    /* 4x4矩阵键盘初始化 */
    Keypad4x4_Init();

    /* 外部控制系统初始化 */
    CommandParser_Init();
    GainCalculator_Init();
    AD9910_Control_Init();

    /* 注册控制回调函数 */
    AD9910_Control_RegisterCallback(ControlCallback);

    /* 设置默认参数 (5MHz/0.5V，增益1.0) */
    AD9910_Control_SetFrequency(5000000);      // 5MHz
    AD9910_Control_SetTargetAmplitude(500);    // 0.5V峰峰值
    AD9910_Control_SetGainFactor(1.0);         // 1倍增益
    AD9910_Control_EnableOutput(true);         // 使能输出

    /* 更新串口屏显示 (暂时注释) */
    // HMI_UpdateAllParameters();
    // HMI_DisplaySystemStatus("System Ready");

    /* 启用4x4矩阵键盘 */
    Keypad4x4_Enable(true);

    /* ==================== 外部控制系统就绪 ==================== */
    // 系统已准备好接受外部控制命令
    // 支持串口屏和4x4矩阵键盘控制
    // 支持实时频率、峰峰值、增益调节

    while (1) {
        /* 控制系统任务处理 */
        AD9910_Control_Task();

        /* 4x4矩阵键盘扫描和处理 */
        Keypad4x4_Scan();

        /* 处理键盘事件 */
        KeypadEvent_t keypad_event;
        while (Keypad4x4_HasEvent(&keypad_event)) {
            if (keypad_event.state == KEY_STATE_PRESSED) {
                /* 按键按下事件已在Keypad4x4_ProcessKey中处理 */
                /* 这里可以添加额外的处理逻辑，如LED指示、蜂鸣器等 */
            }
        }

        /* 串口屏命令处理 (暂时注释) */
        /*
        if (HMI_HasCompleteCommand()) {
            uint8_t cmd_buffer[HMI_RX_BUFFER_SIZE];
            uint16_t cmd_length = HMI_GetReceivedData(cmd_buffer, sizeof(cmd_buffer));

            if (cmd_length > 0) {
                ParsedCommand_t command;
                if (CommandParser_ParseUART(cmd_buffer, cmd_length, &command) == PARSE_STATUS_OK) {
                    ControlStatus_t status = AD9910_Control_Execute(command.command, command.param1, command.param2);

                    if (status == CTRL_STATUS_OK) {
                        HMI_UpdateAllParameters();
                        HMI_DisplaySystemStatus("Command OK");
                    } else {
                        HMI_DisplayError("Command Failed");
                    }
                }
            }

            HMI_ClearReceiveBuffer();
        }
        */

        /* 短暂延时，避免CPU占用过高 */
        Delay_ms(1);
    }
}

/**
  * @brief  控制命令回调函数
  * @param  cmd: 执行的命令
  * @param  status: 执行状态
  * @param  data: 相关数据
  * @retval None
  */
void ControlCallback(ControlCommand_t cmd, ControlStatus_t status, void* data)
{
    /* 命令执行后的处理逻辑 */

    if (status == CTRL_STATUS_OK) {
        /* 命令执行成功 - 可以添加LED指示、蜂鸣器提示等 */
        switch (cmd) {
            case CMD_SET_FREQUENCY:
                /* 频率设置成功，可以点亮频率指示LED */
                break;

            case CMD_SET_AMPLITUDE:
                /* 幅度设置成功，可以点亮幅度指示LED */
                break;

            case CMD_SET_GAIN:
                /* 增益设置成功 */
                break;

            case CMD_ENABLE_OUTPUT:
                /* 输出使能成功，可以点亮输出指示LED */
                break;

            case CMD_DISABLE_OUTPUT:
                /* 输出禁用成功，可以熄灭输出指示LED */
                break;

            case CMD_SET_PRESET:
                /* 预设加载成功 */
                break;

            default:
                break;
        }
    } else {
        /* 命令执行失败 - 可以点亮错误指示LED或蜂鸣器提示 */
        switch (status) {
            case CTRL_STATUS_OUT_OF_RANGE:
                /* 参数超出范围错误 */
                break;
            case CTRL_STATUS_INVALID_PARAM:
                /* 无效参数错误 */
                break;
            case CTRL_STATUS_NOT_READY:
                /* 系统未就绪错误 */
                break;
            default:
                /* 其他错误 */
                break;
        }
    }

    /* 串口屏显示更新 (暂时注释) */
    /*
    if (status == CTRL_STATUS_OK) {
        switch (cmd) {
            case CMD_SET_FREQUENCY:
                {
                    SystemParams_t* params = (SystemParams_t*)data;
                    HMI_DisplayFrequency(params->frequency_hz);
                    HMI_DisplaySystemStatus("Frequency Set");
                }
                break;
            // ... 其他命令处理
        }
    } else {
        HMI_DisplayError("Command Failed");
    }
    */
}

/* Private functions ---------------------------------------------------------*/















/**
  * @brief  系统时钟配置
  * @param  None
  * @retval None
  */
void SystemClock_Config(void)
{
    /* 嘉立创天空星STM32F407VGT6时钟配置 */
    /* 外部晶振：25MHz，目标系统时钟：168MHz */

    RCC_DeInit();

    // 使能HSE
    RCC_HSEConfig(RCC_HSE_ON);
    if (RCC_WaitForHSEStartUp() != SUCCESS) {
        // HSE启动失败，使用HSI
        while(1) {}
    }

    // 配置PLL: HSE = 25MHz, VCO = 25MHz/25*336 = 336MHz, SYSCLK = 336MHz/2 = 168MHz
    RCC_PLLConfig(RCC_PLLSource_HSE, 25, 336, 2, 7);
    RCC_PLLCmd(ENABLE);

    // 等待PLL就绪
    while (RCC_GetFlagStatus(RCC_FLAG_PLLRDY) == RESET) {}

    // 配置Flash延迟
    FLASH_SetLatency(FLASH_Latency_5);

    // 配置总线分频
    RCC_HCLKConfig(RCC_SYSCLK_Div1);   // AHB = 168MHz
    RCC_PCLK1Config(RCC_HCLK_Div4);    // APB1 = 42MHz
    RCC_PCLK2Config(RCC_HCLK_Div2);    // APB2 = 84MHz

    // 切换到PLL
    RCC_SYSCLKConfig(RCC_SYSCLKSource_PLLCLK);
    while (RCC_GetSYSCLKSource() != 0x08) {}
}

/**
  * @brief  定时延时递减函数 (SysTick中断调用)
  * @param  None
  * @retval None
  */
void TimingDelay_Decrement(void)
{
    // 这个函数由SysTick中断调用，用于系统延时
    // 实际的延时逻辑已经在SysTick模块中实现
    // 这里保持空实现以满足链接需求
}

/**
  * @brief  断言失败处理函数
  * @param  file: 源文件名
  * @param  line: 行号
  * @retval None
  */
#ifdef USE_FULL_ASSERT
void assert_failed(uint8_t* file, uint32_t line)
{
    /* 用户可在此添加记录或打印功能，这里简单死循环 */
    while (1) {}
}
#endif

// DDS_TIM6_IRQHandler_Internal函数已在dds_wavegen.c中实现，这里删除重复定义

/**
  * @brief  EXTI0中断处理函数内部实现 (空实现)
  * @param  None
  * @retval None
  */
void EXTI0_IRQHandler_Internal(void)
{
    // 空实现，因为我们不使用外部中断
    // 第二问只需要DAC输出正弦波
}


